<?php

/**
 * @see https://github.com/InnoManic-Org/ClickItFame/issues/570
 */

namespace App\Helpers\Instagram;

use App\Models\SocialPost;
use App\Models\SocialConnect;
use App\Models\InfluencerRequestDetail;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class InsightsForTypePost
{
    protected SocialConnect $socialConnect;
    protected InfluencerRequestDetail $influencerRequestDetail;

    const FIELDS = ['id', 'caption', 'media_type', 'thumbnail_url', 'media_url', 'permalink', 'timestamp'];
    const METRICS = ['likes', 'comments', 'views', 'reach', /*'shares'*/];

    public function __construct(SocialConnect $socialConnect, InfluencerRequestDetail $influencerRequestDetail)
    {
        $this->socialConnect = $socialConnect;
        $this->influencerRequestDetail = $influencerRequestDetail;
    }

    public function importSocialMediaPost(): void
    {
        $baseApiUrl = 'https://graph.facebook.com/v18.0/' . $this->socialConnect->token_secret . '/media?&access_token=' . $this->socialConnect->token;

        // Add time restrictions to fetch posts from campaign creation date onwards
        $since = Carbon::parse($this->influencerRequestDetail->created_at)->timestamp; // Campaign start
        $until = Carbon::now()->timestamp; // Now

        Log::info('Instagram API: Fetching posts with dynamic time range', [
            'campaign_id' => $this->influencerRequestDetail->compaign_id,
            'user_id' => $this->socialConnect->user_id,
            'since_timestamp' => $since,
            'until_timestamp' => $until,
            'since_date' => Carbon::createFromTimestamp($since)->toDateTimeString(),
            'until_date' => Carbon::createFromTimestamp($until)->toDateTimeString(),
            'campaign_created_at' => $this->influencerRequestDetail->created_at
        ]);

        $callApiUrl = $baseApiUrl . '&fields=' . implode(',', self::FIELDS) . '&since=' . $since . '&until=' . $until;

        try {
            $response = Http::get($callApiUrl);

            if (!$response->successful()) {
                Log::error('Failed to fetch Instagram posts', [
                    'user_id' => $this->socialConnect->user_id,
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return;
            }

            $socialPostData = $response->json();

            // sample $socialPostData
            // dd($socialPostData);
            // array:2 [▼ // app/Helpers/Instagram/InsightsForTypePost.php:58
            //     "data" => array:1 [▼
            //         0 => array:7 [▼
            //         "id" => "18052177574169668"
            //         "caption" => "C296"
            //         "media_type" => "VIDEO"
            //         "thumbnail_url" => "https://scontent-fra3-1.cdninstagram.com/v/t51.71878-15/523343115_2178996569213231_5029857450078572026_n.jpg?stp=dst-jpg_e35_tt6&_nc_cat=108&ccb=1-7&_nc_sid=18d ▶"
            //         "media_url" => "https://scontent-fra5-1.cdninstagram.com/o1/v/t2/f2/m86/AQOSGjEc8j2nQ6U5GJfqMEPe1frSBsNAY4eZHf8ITrYhU1o6jsTeto5c0TozvT9yWQbwqLR9cVbzVHc7Z6Fjl1SMQg6oCIrfOGPfNkU. ▶"
            //         "permalink" => "https://www.instagram.com/reel/DMucdWOISpw/"
            //         "timestamp" => "2025-07-30T08:45:28+0000"
            //         ]
            //     ]
            //     "paging" => array:1 [▼
            //         "cursors" => array:2 [▼
            //         "before" => "QVFGbXRWWWptV1lIQU9qejI2V2VQd01Sc09WYzZAzakdzSENTRHc5TjhtcFJ3NWdsbWVGM0N3T2dMWDltOFZApNk5tZAi16M2stZA0t1ZA1RaU2daNXRmdGplUFZAB"
            //         "after" => "QVFGbXRWWWptV1lIQU9qejI2V2VQd01Sc09WYzZAzakdzSENTRHc5TjhtcFJ3NWdsbWVGM0N3T2dMWDltOFZApNk5tZAi16M2stZA0t1ZA1RaU2daNXRmdGplUFZAB"
            //         ]
            //     ]
            // ]

            if (isset($socialPostData['data'])) {
                foreach ($socialPostData['data'] as $socialPostDataItem) {
                    $this->processSocialPostItem($socialPostDataItem);
                }
            }
        } catch (\Exception $e) {
            Log::error('Exception while fetching Instagram posts', [
                'user_id' => $this->socialConnect->user_id,
                'error' => $e->getMessage()
            ]);
            return;
        }
    }

    private function processSocialPostItem(array $socialPostDataItem): void
    {
        $postType = '';
        if (str_contains($socialPostDataItem['permalink'] ?? '', '/reel/')) {
            $postType = 'reel';
        }

        if (empty($socialPostDataItem['id'])) {
            Log::error('Instagram post missing ID during submission processing', [
                'user_id' => $this->socialConnect->user_id,
                'influencer_request_id' => $this->influencerRequestDetail->id,
                'post_data' => $socialPostDataItem,
                'permalink' => $socialPostDataItem['permalink'] ?? 'N/A',
                'media_type' => $socialPostDataItem['media_type'] ?? 'N/A',
                'timestamp' => $socialPostDataItem['timestamp'] ?? 'N/A'
            ]);
            return;
        }

        $insightUrl = 'https://graph.facebook.com/v18.0/' . $socialPostDataItem['id'] . '/insights?access_token=' . $this->socialConnect->token;
        $insightUrl .= '&metric=' . implode(',', self::METRICS);

        try {
            $insightResponse = Http::get($insightUrl);

            if (!$insightResponse->successful()) {
                Log::warning('Failed to fetch Instagram post insights', [
                    'post_id' => $socialPostDataItem['id'],
                    'user_id' => $this->socialConnect->user_id,
                    'status' => $insightResponse->status()
                ]);
                $insightData = [];
            } else {
                $insightData = $insightResponse->json();
            }
        } catch (\Exception $e) {
            Log::error('Exception while fetching Instagram post insights', [
                'post_id' => $socialPostDataItem['id'],
                'user_id' => $this->socialConnect->user_id,
                'error' => $e->getMessage()
            ]);
            $insightData = [];
        }

        $preparedMetricsData = [];

        if (isset($insightData['data'])) {
            $preparedMetricsData['complete__' . date('Y_m_d_H_i_s')] = $insightData;
            foreach ($insightData['data'] as $insightDataItem) {
                $preparedMetricsData[$insightDataItem['name']] = $insightDataItem['values'][0]['value'] ?? 0;
            }
        }

        $postTimestamp = Carbon::parse($socialPostDataItem['timestamp']);
        $requestCreatedAt = Carbon::parse($this->influencerRequestDetail->created_at);
        $now = Carbon::now(); // FIX: Use current time instead of today at midnight

        if ($postTimestamp->greaterThanOrEqualTo($requestCreatedAt) && $postTimestamp->lessThanOrEqualTo($now)) {
            $filepath = '';
            $contentType = 'video';

            if (isset($socialPostDataItem['media_url'])) {
                try {
                    $fileContents = file_get_contents($socialPostDataItem['media_url']);
                    $filename = $socialPostDataItem['id'] . '_instagram';

                    $fileExt = '.mp4';
                    if (
                        ($socialPostDataItem['media_type'] ?? '') == 'IMAGE' ||
                        ($socialPostDataItem['media_type'] ?? '') == 'CAROUSEL_ALBUM'
                    ) {
                        $fileExt = '.jpg';
                        $contentType = 'photo';
                    }

                    $filenameWithExt = $filename . $fileExt;
                    $filepath = 'social_pics/' . $filenameWithExt;

                    Storage::disk('public')->put($filepath, $fileContents);
                } catch (\Exception $e) {
                    Log::error('Failed to download Instagram media file', [
                        'post_id' => $socialPostDataItem['id'],
                        'media_url' => $socialPostDataItem['media_url'] ?? 'N/A',
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // FIX: Use updateOrCreate to handle both create and update scenarios
            // This prevents duplicate entry errors and ensures proper upsert behavior
            $uniqueFields = [
                'user_id' => $this->socialConnect->user_id,
                'media' => 'instagram',
                'post_id' => $socialPostDataItem['id']
            ];

            $updateData = [
                'influencer_request_accept_id' => $postType,
                'text' => $socialPostDataItem['caption'] ?? null,
                'link' => $filepath,
                'type' => $contentType,
                'published_at' => $postTimestamp->format('Y-m-d H:i:s'),
                'thumbnail' => $socialPostDataItem['permalink'] ?? null,
                'insights' => $preparedMetricsData
            ];

            // Use updateOrCreate for proper upsert behavior
            $socialPost = SocialPost::updateOrCreate($uniqueFields, $updateData);

            Log::info('Instagram post processed successfully', [
                'campaign_id' => $this->influencerRequestDetail->compaign_id,
                'user_id' => $this->socialConnect->user_id,
                'post_id' => $socialPostDataItem['id'],
                'action' => $socialPost->wasRecentlyCreated ? 'created' : 'updated',
                'insights_count' => count($preparedMetricsData)
            ]);
        }
    }
}