<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\SocialConnect;
use App\Models\Category;
use App\Models\Hashtag;
use App\Models\InfluencerDetail;
use App\Models\AdvertisingMethod;
use App\Models\AdvertisingMethodPrice;
use App\Models\Country;
use App\Models\State;
use App\Models\City;
use App\Models\InfluencerRequest;
use App\Models\InfluencerRequestDetail;
use App\Models\InfluencerRequestAccept;
use App\Models\CampaignRequestTime;
use App\Models\Invoice;
use App\Models\SocialPost;
use App\Models\SocialKeys;
use App\Models\Mode;
use App\Models\Dialogue;
use App\Models\MollieAccount;
use App\Models\StripeAccount;
use App\Models\RatingReview;
use App\Models\Complaint;
use App\Models\TransferInfluencer;
use App\Models\AdminComission;
use App\Models\InfluencerRequestTime;
use App\Models\ContactSupport;
use App\Models\SavedCard;
use App\Models\Task;
use App\Models\AdvertisingMethodNewPrice;
use App\Models\AdminPricing;
use App\Models\AdminGamification;
use App\Models\UserDailyLogin;
use App\Models\RequestTask;
use App\Models\Statistic;
use App\Models\SmCampaign;
use App\Models\AdminHashtag;
use App\Support\CampaignHelpers;
use App\Helpers\Instagram\InsightsForTypePost;
use App\Helpers\Instagram\InsightsForTypeStory;

use Stripe;
use Auth;
use Hash;
use File;
use DB;
use Str;
use Socialite;
use Session;
use Storage;
use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Subscriber\Oauth\Oauth1;
use App\TwitterPost;
use Twitter;
use Config;
use Carbon\Carbon;
use Mail;


use App\Notifications\PasswordChanged;
use App\Notifications\verifyMail;
use App\Notifications\socialAlreadyUsed;
use App\Notifications\RequestInfluencer;
use App\Notifications\RequestAcceptInfluencer;
use App\Notifications\RequestMoreTimeInfluencer;
use App\Notifications\RequestAcceptStatusInfluencer;
use App\Notifications\AcceptRequestMoreTimeInfluencer;
use App\Notifications\ComplaintInfluencer;
use App\Notifications\ComplaintAdmin;
use App\Notifications\paidToInfluencer;
use App\Notifications\ConfirmPostInfluencer;
use App\Notifications\RatingSubmit;
use App\Notifications\StartCampaignInfluencer;
use App\Notifications\CancelRefund;
use App\Notifications\RequestCancelInfluencer;
use App\Notifications\DisputeContactUser;
use App\Notifications\DisputeContactAdmin;
use App\Notifications\SupportContactAdmin;
use App\Notifications\SupportContactUser;

use Laravel\Socialite\SocialiteManager;
use Laravel\Socialite\Two\FacebookProvider;
use Laravel\Socialite\Two\GoogleProvider;
use Laravel\Socialite\One\TwitterProvider;
use Laravel\Socialite\Two\TwitterProvider as TwitterOAuth2Provider;
use SocialiteProviders\YouTube\Provider as YoutubeProvider;


use App\Notifications\allInfluencersHaveCheckedYourRequest;
use App\Jobs\NewallInfluencersHaveCheckedYourRequest;



use App\Jobs\NewPasswordChanged;
use App\Jobs\SendverifyMail;
use App\Jobs\NewsocialAlreadyUsed;
use App\Jobs\NewRequestInfluencer;
use App\Jobs\NewRequestAcceptStatusInfluencer;
use App\Jobs\NewCancelRefund;
use App\Jobs\NewStartCampaignInfluencer;
use App\Jobs\NewConfirmPostInfluencer;
use App\Jobs\NewRatingSubmit;
use App\Jobs\NewComplaintInfluencer;
use App\Jobs\NewComplaintAdmin;
use App\Jobs\NewpaidToInfluencer;
use App\Jobs\NewRequestCancelInfluencer;
use App\Jobs\NewDisputeContactAdmin;
use App\Jobs\NewDisputeContactUser;
use App\Jobs\NewSupportContactAdmin;
use App\Jobs\NewSupportContactUser;
use App\Jobs\NewRequestAcceptInfluencer;
use App\Jobs\NewtheInfluencersHaveDoneTheirJob;
use App\Jobs\NewyourCampaignisMovingForward;
use App\Jobs\NewcampaignSuccessfullyCompleted;
use App\Jobs\NewReviewPhaseEnded;
use App\Jobs\NewweReceivedYourComplaint;
use App\Models\Campaign;
use App\Models\PausedCampaign;
use Exception;
use Illuminate\Support\Facades\Auth as FacadesAuth;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Page redirection after the successfull payment
     *
     * @return Response
     */
    public function paymentSuccess()
    {
        echo 'payment has been received';
    }


    public function collectionForget()
    {
        Session::forget('collection');
        Session::put('collection', '0');
    }
    public function settings()
    {
        $user = User::find(Auth::id());
        return view('front-user.common.verify-email', compact('user'));
    }


    public function getchangePassword()
    {
        $this->collectionForget();
        $user = User::find(Auth::id());
        return view('front-user.common.settings', compact('user'));
    }

    public function verifyEmail()
    {
        $this->collectionForget();
        $user = User::find(Auth::id());
        return view('front-user.common.verify-email', compact('user'));
    }
    public function changePassword(request $request)
    {

        $user = User::find(Auth::id());

        if (!Hash::check($request->current_password, $user->password)) {
            return back()->with('error', 'Sorry your current password does not match.!');
        }

        $this->validate($request, [
            'current_password' => 'required',
            'password' => 'confirmed|min:8|different:current_password'
        ], [
            "password.different" => "Your new password can not be the same as your old password!",
        ]);

        if ($request->password == $request->password_confirmation) {
            if ($user) {
                $password_updated = $user->update(['password' => Hash::make($request->password)]);

                if ($password_updated) {
                    dispatch(new NewPasswordChanged($user));
                    $user->notify(new PasswordChanged($user));
                    return back()->with(['success' => 'Password is changed successfully.!']);
                } else {
                    return back()->with(['error' => 'There is an error while changing the password please try again later.!']);
                }
            }
        } else {
            return back()->with('error', 'New password do not matched with confirm password.!');
        }
    }

    public function changeStatus($status)
    {
        User::find(Auth::id())->update(['status' => $status]);
        return response()->json(['code' => '200', 'msg' => 'Profile status changed successfully!!']);
    }

    public function myProfile()
    {
        if (Auth::user()->user_type == 'influencer') {
            return response()->view('errors.' . '404', [], 404);
        }
        $this->collectionForget();
        $user = User::find(Auth::id());
        $countries = Country::get();
        $cities =  DB::table('states')->leftjoin('cities',  'states.id', '=', 'cities.state_id')
            ->select('cities.*')
            ->where('states.country_id', Auth::user()->country)->get();
        $states = State::find(Auth::user()->state);

        $social_connect_twitter = SocialConnect::where('media', 'twitter')->where('user_id', Auth::id())->first();
        $social_connect_youtube = SocialConnect::where('media', 'youtube')->where('user_id', Auth::id())->first();
        $social_connect_twitch = SocialConnect::where('media', 'twitch')->where('user_id', Auth::id())->first();
        $social_connect_facebook = SocialConnect::where('media', 'facebook')->where('user_id', Auth::id())->first();
        $social_connect_instagram = SocialConnect::where('media', 'instagram')->where('user_id', Auth::id())->first();
        $social_connect_snapchat = SocialConnect::where('media', 'snapchat')->where('user_id', Auth::id())->first();
        $social_connect_tiktok = SocialConnect::where('media', 'tiktok')->where('user_id', Auth::id())->first();


        $stripeAccount = StripeAccount::where('user_id', Auth::id())->first();

        return view('front-user.common.my-profile', compact('cities', 'countries', 'states', 'user', 'social_connect_twitter', 'social_connect_youtube', 'social_connect_twitch', 'social_connect_facebook', 'social_connect_instagram', 'social_connect_snapchat', 'social_connect_tiktok', 'stripeAccount'));
    }

    /**
     * This method add's a vat id to influencer's stripe connected account.
     */
    private function addVatIdToInfluencerStripeConnectedAccount($connectedAccountId, $vatId) {
        // Sample response. We need to save the id value to set the default
        // tax ids of the connectedAccount.
        // Array
        // (
        //     [id] => txi_1R5ZAICQU9TzuCcqRmp0iCCH
        //     [object] => tax_id
        //     [country] => DE
        //     [created] => **********
        //     [customer] =>
        //     [livemode] =>
        //     [owner] => Array
        //         (
        //             [type] => self
        //         )

        //     [type] => eu_vat
        //     [value] => DE123456788
        //     [verification] =>
        // )

        $vatId = trim($vatId);
        $vatPattern = '/^[A-Z]{2}\d+$/';
        $taxIdPattern = '/^\d+$/';

        $taxType = '';
        if (preg_match($vatPattern, $vatId)) {
            $taxType = 'eu_vat';
        } elseif (preg_match($taxIdPattern, $vatId)) {
            $taxType = 'de_stn';
        } else {
            return null;
        }

        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => "https://api.stripe.com/v1/tax_ids",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query([
                'type' => $taxType, // TODO configure this based on location
                'value' => $vatId,
            ]),
            CURLOPT_HTTPHEADER => [
                'Stripe-Account: ' . $connectedAccountId,
                'Content-Type: application/x-www-form-urlencoded',
            ],
            CURLOPT_USERPWD => config('settings.env.STRIPE_SECRET') . ":",
        ]);

        $response = curl_exec($ch);

        $responseData = null;
        if (curl_errno($ch)) {
            // TODO what to do if vatID addition fails
            // Should we throw an exception and hault the whole onboarding process?
        } else {
            $responseData = json_decode($response, true);
            $success = true;
        }

        curl_close($ch);

        return $responseData;
    }

    private function addDefaultTaxIdsToInfluencerStripeConnectedAccount($connectedAccountId, $taxId) {
        // Set the tax IDs, has the format 'txi_1R5ZAICQU9TzuCcqRmp0iCCH'
        $taxIds = [$taxId];

        // Prepare POST data, can be added multiple by sending array request
        // http_build_query is important, won't work without
        $postData = http_build_query([
            'settings[invoices][default_account_tax_ids][0]' => $taxIds[0],
        ]);

        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => "https://api.stripe.com/v1/accounts/{$connectedAccountId}",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $postData,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/x-www-form-urlencoded', // Important, won't work without
            ],
            CURLOPT_USERPWD => config('settings.env.STRIPE_SECRET') . ":",
        ]);

        $response = curl_exec($ch);

        $responseData = null;
        if (curl_errno($ch)) {
            // TODO what to do if vatID addition fails
            // Should we throw an exception and hault the whole onboarding process?
        } else {
            // If no error, decode the JSON response
            $responseData = json_decode($response, true);
        }

        curl_close($ch);

        // A successful api call will return an array of array where we will have the following item
        // Array
        // (
        //     [invoices] => Array
        //         (
        //             [default_account_tax_ids] => Array
        //                 (
        //                     [0] => txi_1R5ZAICQU9TzuCcqRmp0iCCH
        //                 )

        //         )
        // )

        return $responseData;
    }

    /**
     * Update brand profile info when submitted from Profile Information section.
     */
    public function updateBrandProfile(Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request,
            [
                'profile_pic' => 'file|max:5000',
            ],
            [
                "profile_pic.max" => "Procile image should be less than 5MB!",
            ]
        );

        if ($request->hasFile('profile_pic')) {
            $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg'];
            $file = $request->file('profile_pic');
            File::delete($formData['oldImageValue']);

            $extension = $file->getClientOriginalExtension();

            if (in_array($extension, $allowedfileExtension)) {
                $formData['profile_pic'] = $file->store('profile_pics', 'public');
            }
        }

        // Check if Tax ID is provided in the request
        // TODO We do not have Tax ID in the profile informatio section yet
        $vatId = $request->input('vat_id');
        $user = User::find(Auth::id());

        $user->update($formData);
        // TODO This might work too
        // $user->stripeAccount->stripe_user_id
        $stripeAccount = StripeAccount::where('user_id', Auth::id())->first();

        if (!empty($vatId) && !empty($stripeAccount->stripe_user_id)) {
            try {
                $response = $this->addVatIdToInfluencerStripeConnectedAccount($stripeAccount->stripe_user_id, $vatId);
                // If we get a valid response and if the tax id exists in the response array
                if (is_array($response) && !empty($response['id'])) {
                    $this->addDefaultTaxIdsToInfluencerStripeConnectedAccount($stripeAccount->stripe_user_id, $response['id']);
                }
            } catch (\Exception $e) {
                return response(['status' => false, 'msg' => 'Failed to update VAT/Tax ID in Stripe.']);
            }
        }

        if ($request->ajax()) {
            return response(['status' => true,  'msg' => 'Details saved successfully.']);
        } else {
            return back()->with('success', 'Profile updated successfully.');
        }
    }

    public function updateProfile(Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request,
            [
                'profile_pic' => 'file|max:5000',
                'is_small_business_owner' => 'required|in:0,1',
            ],
            [
                "profile_pic.max" => "Procile image should be less than 5MB!",
                "is_small_business_owner.required" => "Please select whether you are a small business owner.",
                "is_small_business_owner.in" => "Please select a valid option for small business owner status.",
            ]
        );

        if ($request->hasFile('profile_pic')) {
            $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg'];
            $file = $request->file('profile_pic');
            File::delete($formData['oldImageValue']);

            $extension = $file->getClientOriginalExtension();

            if (in_array($extension, $allowedfileExtension)) {
                $formData['profile_pic'] = $file->store('profile_pics', 'public');
            }
        }

        // Check if Tax ID is provided in the request
        $vatId = $request->input('vat_id');
        $user = User::find(Auth::id());

        // User::find(Auth::id())->update($formData);
        // Update user profile data
        $user->update($formData);
        // TODO This might work too
        // $user->stripeAccount->stripe_user_id
        $stripeAccount = StripeAccount::where('user_id', Auth::id())->first();

        if (!empty($vatId) && !empty($stripeAccount->stripe_user_id)) {
            try {
                $response = $this->addVatIdToInfluencerStripeConnectedAccount($stripeAccount->stripe_user_id, $vatId);
                // If we get a valid response and if the tax id exists in the response array
                if (is_array($response) && !empty($response['id'])) {
                    $this->addDefaultTaxIdsToInfluencerStripeConnectedAccount($stripeAccount->stripe_user_id, $response['id']);
                }
            } catch (\Exception $e) {
                return response(['status' => false, 'msg' => 'Failed to update VAT/Tax ID in Stripe.']);
            }
        }

        if ($request->ajax()) {
            return response(['status' => true,  'msg' => 'Details saved successfully.']);
        } else {
            return back()->with('success', 'Profile updated successfully.');
        }
    }

    public function updateBrandProfileImage(Request $request)
    {
        $this->validate($request, [
            'profile_pic' => 'file|max:5000',
        ], [
            "profile_pic.max" => "Procile image should be less than 5MB !",
        ]);

        if ($request->hasFile('profile_pic')) {
            $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg'];
            $file = $request->file('profile_pic');

            $extension = $file->getClientOriginalExtension();

            if (in_array($extension, $allowedfileExtension)) {

                $formData['profile_pic'] = $file->store('profile_pics', 'public');
            }
        }

        User::find(Auth::id())->update($formData);

        return response()->json(['code' => '200', 'msg' => 'Profile image saved successfully!!']);
    }

    public function activateDeactivateUser(Request $request)
    {
        $id = $request->id;
        $status = $request->status;

        if ($status == 1)
            $msg = 'You have successfully activated  .';
        else
            $msg = 'You have successfully deactivated  .';

        if (User::find($id)->update(['status' => $status])) {
            return response()->json(['status' => 'success', 'msg' => $msg]);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error while changing the status of User. Please try again later!']);
        }
    }

    public function notifications($value = '')
    {
        DB::table('notifications')->where('read_at', NULL)->where('notifiable_id', Auth::id())->update(['read_at' => Date('Y-m-d H:i:s')]);

        $all_notifications = Auth::user()->notifications()->get();
        $notifications = [];
        foreach ($all_notifications as $notification) {
            $notifications[] = $notification;
        }

        return view('front-user.common.notifications', compact('notifications'));
    }


    public function sendVerificationMailProfile($email)
    {
        $email = base64_decode($email);
        $user = User::whereEmail($email)->first();
        $user->verify_token = str_random(30);
        $user->save();

        dispatch(new SendverifyMail($user));


        // $user->notify(new verifyMail($user));

        return back()->with('success', 'Verification email resent. Please check your email.');
    }
    public function verify_user_profile($token)
    {
        $verifyUser = User::where('verify_token', $token)->first();
        if ($verifyUser) {
            $verifyUser->email_verified_at = Date('Y-m-d H:i:s');
            $verifyUser->save();
        }
        return redirect('/')->with('success', 'Email verified successfully.');
    }
    //Social connect

    private function configDriver($provider)
    {
        $result = SocialKeys::first();
        $config['client_id'] = $result->facebook_app_id;
        $config['client_secret'] = $result->facebook_app_secret;
        $config['redirect'] = $result->facebook_callback_url;
        return Socialite::buildProvider(FacebookProvider::class, $config);
    }

    private function configDriverGoogle($provider)
    {
        $result = SocialKeys::first();
        $config['client_id'] = $result->youtube_app_id;
        $config['client_secret'] = $result->youtube_app_secret;
        $config['redirect'] = $result->youtube_callback_url;
        return Socialite::buildProvider(YoutubeProvider::class, $config);
    }
    private function configDriverTwitter($provider)
    {
        $result = SocialKeys::first();
        $config['client_id'] = $result->twitter_app_id;
        $config['client_secret'] = $result->twitter_app_secret;
        $config['redirect'] = $result->twitter_callback_url;
        return Socialite::buildProvider(TwitterProvider::class, $config);
    }
    public function redirectToProvider($provider)
    {
        $result = SocialKeys::first();
        Config::set('services.twitch.client_id', $result->twitch_app_id);
        Config::set('services.twitch.client_secret', $result->twitch_app_secret);
        Config::set('services.twitch.redirect', $result->twitch_callback_url);

        Config::set('services.youtube.client_id', $result->youtube_app_id);
        Config::set('services.youtube.client_secret', $result->youtube_app_secret);
        Config::set('services.youtube.redirect', $result->youtube_callback_url);

        Config::set('services.facebook.client_id', $result->facebook_app_id);
        Config::set('services.facebook.client_secret', $result->facebook_app_secret);
        Config::set('services.facebook.redirect', $result->facebook_callback_url);

        Config::set('services.instagram.client_id', $result->instagram_app_id);
        Config::set('services.instagram.client_secret', $result->instagram_app_secret);
        Config::set('services.instagram.redirect', $result->instagram_callback_url);

        Config::set('services.twitter.client_id', $result->twitter_app_id);
        Config::set('services.twitter.client_secret', $result->twitter_app_secret);
        Config::set('services.twitter.redirect', $result->twitter_callback_url);

        Config::set('services.snapchat.client_id', $result->snapchat_app_id);
        Config::set('services.snapchat.client_secret', $result->snapchat_app_secret);
        Config::set('services.snapchat.redirect', $result->snapchat_callback_url);

        Config::set('services.tiktok.client_id', $result->tiktok_app_id);
        Config::set('services.tiktok.client_secret', $result->tiktok_app_secret);
        Config::set('services.tiktok.redirect', $result->tiktok_callback_url);

        Session::forget('influencer_follower_error');
        Session::forget('influencer_error');
        Session::put('provider', $provider);
        if ($provider == 'facebook') {
            $fb = $this->configDriver($provider)->scopes(['public_profile', 'pages_show_list', 'pages_read_engagement', 'business_management']);
            return $fb->redirect();
        } elseif ($provider == 'instagram') {
            $fb = $this->configDriver('facebook')->scopes(['pages_show_list', 'instagram_basic', 'instagram_manage_insights', 'pages_read_engagement', 'business_management']);
            return $fb->redirect();
        } elseif ($provider == 'youtube') {
            $fb = $this->configDriverGoogle('youtube');
            return $fb->redirect();
        } elseif ($provider == 'twitter') {
            $fb = $this->configDriverTwitter('twitter');
            return $fb->redirect();
        } elseif ($provider == 'snapchat') {
            $result = SocialKeys::first();
            $clientId = $result->snapchat_app_id;
            $clientSecret = $result->snapchat_app_secret;
            $redirectUrl = $result->snapchat_callback_url;
            $additionalProviderConfig = [];
            $config = new \SocialiteProviders\Manager\Config($clientId, $clientSecret, $redirectUrl, $additionalProviderConfig);
            return Socialite::with('snapchat')->setConfig($config)->redirect();
        } elseif ($provider == 'tiktok') {
            $result = SocialKeys::first();
            $clientId = $result->tiktok_app_id;
            $clientSecret = $result->tiktok_app_secret;
            $redirectUrl = $result->tiktok_callback_url;
            $additionalProviderConfig = [];
            $config = new \SocialiteProviders\Manager\Config($clientId, $clientSecret, $redirectUrl, $additionalProviderConfig);
            return Socialite::with('tiktok')->setConfig($config)->redirect();
        } else
            return Socialite::driver($provider)->redirect();
    }

    public function handleProviderCallback(Request $request, $provider = null)
    {
        if ($request->error != '') {
            return "<script type='text/javascript'> self.close(); window.opener.refreshParentError();</script>";
        }

        // $result = SocialKeys::first();
        // $config = [];
        // $config['client_id'] = $result->facebook_app_id;
        // $config['client_secret'] = $result->facebook_app_secret;
        // $config['redirect'] = $result->facebook_callback_url;
        // dd($config);
        
        if (!$provider) {
            $provider = 'instagram';
        }
            
        if ($provider == 'facebook' || $provider == 'instagram') {
            try {
                $userSocial = $this->configDriver($provider)->user();
            } catch (\Laravel\Socialite\Two\InvalidStateException $e) {
                \Log::error('OAuth InvalidStateException for provider ' . $provider . ': ' . $e->getMessage());
                return redirect('/login')->with('error', 'Authentication session expired. Please try logging in again.');
            } catch (\Exception $e) {
                \Log::error('OAuth Exception for provider ' . $provider . ': ' . $e->getMessage());
                return redirect('/login')->with('error', 'Authentication failed. Please try again.');
            }
        }

        $userSocial->social_id = '';

        if ($provider == 'facebook' || $provider == 'instagram') {
            if (empty($userSocial->token)) {
                \Log::error('Missing access token from social provider', [
                    'provider' => $provider,
                    'user_id' => Auth::id(),
                    'request' => $request->all(),
                    'userSocial' => isset($userSocial) ? (array) $userSocial : null,
                    'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
                ]);
                throw new \Exception("Missing access token from social provider: {$provider}");
            }

            $url = "https://graph.facebook.com/v18.0/me/accounts?access_token=" . $userSocial->token;
            $client = new Client();
            $response = $client->request('GET', $url);
            $content = $response->getBody()->getContents();
            $oAuth = json_decode($content);
            $userSocial->pages = @$oAuth->data;
        }

        if (@Session::get('provider') == 'instagram') {
            if (empty($userSocial->pages[0]->id)) {
                \Log::error('No Instagram business page found for user', [
                    'provider' => $provider,
                    'user_id' => Auth::id(),
                    'request' => $request->all(),
                    'pages' => $userSocial->pages ?? null,
                    'userSocial' => isset($userSocial) ? (array) $userSocial : null,
                    'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
                ]);
                throw new \Exception("No Instagram business page found for user: " . Auth::id());
            }

            $provider = 'instagram';

            // TODO IF the user were not correctly connect his facebook page to their instagram account then the below code will not work.
            // So we need to check if the user has correctly connected his facebook page to his instagram account.
            // This can be checked by checking the emptyness of the $userSocial->pages array.
            $url = "https://graph.facebook.com/v18.0/" . $userSocial->pages[0]->id . "?fields=instagram_business_account&access_token=" . $userSocial->token;
            $client = new Client();
            $response = $client->request('GET', $url);
            $content = $response->getBody()->getContents();
            $socialBusinessData = json_decode($content);
            if (empty($socialBusinessData->instagram_business_account->id)) {
                \Log::error('Missing $oAuth->instagram_business_account->id', [
                    'provider' => $provider,
                    'user_id' => Auth::id(),
                    'request' => $request->all(),
                    'userSocial' => isset($userSocial) ? (array) $userSocial : null,
                    'socialBusinessData' => $socialBusinessData,
                    'response' => $response,
                    'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
                ]);

                Session::put('influencer_follower_error', 'Invalid page or instagram account selected');
                return "<script type='text/javascript'> self.close(); window.opener.refreshParentError();</script>";
            }

            $insta_user_id = $socialBusinessData->instagram_business_account->id;

            $url = "https://graph.facebook.com/v18.0/" . $insta_user_id . "?fields=id,name,username,profile_picture_url,followers_count&access_token=" . $userSocial->token;
            $client = new Client();
            $response = $client->request('GET', $url);
            $content = $response->getBody()->getContents();
            $socialProfileData = json_decode($content);

            // Prefer username, fallback to name, fallback to empty string
            if (!empty($socialProfileData->username)) {
                $userSocial->name = $socialProfileData->username;
            } elseif (!empty($socialProfileData->name)) {
                $userSocial->name = $socialProfileData->name;
            } else {
                $userSocial->name = '';
            }

            $userSocial->followers_count = $socialProfileData->followers_count ?? 0;
            // Set the Instagram profile URL if username is available
            if (!empty($socialProfileData->username)) {
                $userSocial->profileUrl = "https://www.instagram.com/" . $socialProfileData->username;
            } else {
                $userSocial->profileUrl = null;
            }

            if (empty($socialProfileData->profile_picture_url)) {
                \Log::info('Instagram profile_picture_url is empty', [
                    'provider' => $provider,
                    'user_id' => Auth::id(),
                    'request' => $request->all(),
                    'userSocial' => isset($userSocial) ? (array) $userSocial : null,
                    'socialProfileData' => $socialProfileData,
                    'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
                ]);
                $picture = '';
            } else {
                $picture = $socialProfileData->profile_picture_url;
            }
            $userSocial->tokenSecret = $insta_user_id;
        } elseif ($provider == 'facebook') {
            // Build the Facebook Graph API URL to fetch page details
            $pageId = !empty($userSocial->pages[0]->id) ? $userSocial->pages[0]->id : null;
            if (!$pageId) {
                \Log::error('No Facebook page ID found for user', [
                    'provider' => $provider,
                    'user_id' => Auth::id(),
                    'request' => $request->all(),
                    'userSocial' => isset($userSocial) ? (array) $userSocial : null,
                    'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
                ]);
                throw new \Exception("No Facebook page ID found for user: " . Auth::id());
            }
            $fields = 'id,name,username,picture,followers_count,link,access_token';
            $url = "https://graph.facebook.com/v18.0/{$pageId}?fields={$fields}&access_token=" . $userSocial->token;
            $client = new Client();
            $response = $client->request('GET', $url);
            $content = $response->getBody()->getContents();
            $userProfileData = json_decode($content);
            $userSocial->page_access_token = $userProfileData->access_token;
            $userSocial->social_id = $userSocial->page_access_token;
            if (isset($userProfileData->username) && !empty($userProfileData->username)) {
                $userSocial->name = $userProfileData->username;
            } elseif (isset($userProfileData->name)) {
                $userSocial->name = $userProfileData->name;
            } else {
                $userSocial->name = '';
            }
            $userSocial->followers_count = $userProfileData->followers_count ?? 0;
            $userSocial->profileUrl = $userProfileData->link ?? null;
            if (empty($userProfileData->picture->data->url)) {
                \Log::info('Facebook profile_picture_url is empty', [
                    'provider' => $provider,
                    'user_id' => Auth::id(),
                    'request' => $request->all(),
                    'userSocial' => isset($userSocial) ? (array) $userSocial : null,
                    'userProfileData' => $userProfileData,
                    'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
                ]);
                $picture = '';
            } else {
                $picture = $userProfileData->picture->data->url;
            }
            $userSocial->tokenSecret = $userSocial->pages[0]->id ?? null;
            $accessToken = $userSocial->token;
        }

        // Determine the best available name (prefer nickname over name)
        $name = !empty($userSocial->nickname) ? $userSocial->nickname : ($userSocial->name ?? '');

        // Determine the best available avatar (prefer original over regular)
        $avatar = !empty($userSocial->avatar_original) ? $userSocial->avatar_original : ($userSocial->avatar ?? '');
        
        $socialconnect = SocialConnect::where('media', $provider)->where('name', $name)->first();
        if ($socialconnect == null) {
            $name = !empty($userSocial->nickname) ? $userSocial->nickname : ($userSocial->name ?? '');

            $mode = Mode::first();
            if ($mode) {
                if ($mode->status == 1) {
                    $influencer_count = $mode->production_influencer;
                } else {
                    $influencer_count = $mode->testing_influencer;
                }
            } else {
                $influencer_count = 0;
            }

            if ($userSocial->followers_count < $influencer_count) {
                Session::put('influencer_follower_error', 'Need at least ' . $influencer_count . ' Follower(s)');
                return "<script type='text/javascript'> self.close(); window.opener.refreshParentError();</script>";
            } else {
                if (!empty($picture)) {
                    $fileContents = @file_get_contents($picture);
                } else {
                    $fileContents = @file_get_contents(str_replace('http://', 'https://', @$userSocial->getAvatar()));
                }

                $filepath = null;
                if (!empty($fileContents)) {
                    $filepath = 'social_pics/' . str_random(40) . '.jpg';
                    Storage::disk('public')->put($filepath, $fileContents);
                }

                $user = SocialConnect::updateOrCreate([
                    'user_id' => Auth::id(),
                    'name' => $name,
                    'media' => $provider,
                    'picture' => $filepath,
                    'url' => @$userSocial->profileUrl,
                    'followers' => @$userSocial->followers_count,
                    'token' => @$userSocial->token,
                    'token_secret' => @$userSocial->tokenSecret,
                    'social_id' => @$userSocial->social_id
                ]);
            }
        } else {
            $user = User::find(Auth::id());
            $sender_user = User::find($socialconnect->user_id);

            dispatch(new NewsocialAlreadyUsed($sender_user, $user));
            $sender_user->notify(new socialAlreadyUsed($sender_user, $user));
            Session::put('influencer_error', 'This ' . $provider . ' account was already linked with another clickitfame-account.Please contact the support, if you think this should have not happened!!');

            return "<script type='text/javascript'> self.close(); window.opener.refreshParentError();</script>";
        }
        
        Session::forget('influencer_follower_error');
        Session::forget('influencer_error');
        
        return "<script type='text/javascript'> self.close(); window.opener.refreshParent();</script>";
    }

    public function disconnectProvider($provider)
    {
        $social_connectc = SocialConnect::where('media', $provider)->where('user_id', Auth::id())->delete();
        // AdvertisingMethod::whereRaw("FIND_IN_SET('" . $provider . "', social_media)")->where('user_id',Auth::id())->delete();

        $detail1 = AdvertisingMethodPrice::whereUserId(Auth::id())->where('sharecontent_media', $provider)->first();
        if (isset($detail1)) {
            $influencer_detail = array(
                'sharecontent_media' =>  '',
                'sharecontent_price' =>  '',
                'sharecontent_price_additional' =>  ''
            );
            $detail1->update($influencer_detail);
        }

        $detail2 = AdvertisingMethodPrice::whereUserId(Auth::id())->where('video_media', $provider)->first();
        if (isset($detail2)) {
            $influencer_detail = array(
                'video_media' =>  '',
                'video_price' =>  '',
                'video_price_additional' => ''
            );
            $detail2->update($influencer_detail);
        }


        $detail3 = AdvertisingMethodPrice::whereUserId(Auth::id())->where('livestream_media', $provider)->first();
        if (isset($detail3)) {
            $influencer_detail = array(
                'livestream_media' =>  '',
                'livestream_price' =>  '',
                'livestream_price_additional' => ''
            );
            $detail3->update($influencer_detail);
        }

        return redirect('/influencer-onboarding')->with('success', 'Disconnected successfully!');
    }

    //Influencer
    public function fetchStates(Request $request)
    {
        $cities = DB::table('states')->leftjoin('cities',  'states.id', '=', 'cities.state_id')
            ->select('cities.*')
            ->where('states.country_id', $request->country)->get();
        $data = '<option value="">Select Cities</option>';
        foreach ($cities as $key => $row) {
            if ($row->name != '') {
                $data .= "<option value=$row->id>$row->name</option>";
            }
        }
        return $data;
    }

    public function fetchCities(Request $request)
    {
        $cities = DB::table('cities')->where('state_id', $request->state)->get();
        $data = '<option value="">Select City</option>';
        foreach ($cities as $key => $city) {
            if ($city->name != '') {
                $data .= "<option value=$city->id>$city->name</option>";
            }
        }
        return $data;
    }


    public function influencerWizard()
    {
        if (Auth::user()->user_type == 'customer') {
            return response()->view('errors.' . '404', [], 404);
        }

        $stripeAccount = StripeAccount::where('user_id', Auth::id())->first();
        if (!$stripeAccount) {
            return view('front-user.pages.stripe_not_connected');
        }

        \Stripe\Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));

        $account = Stripe\Account::retrieve($stripeAccount->stripe_user_id);

        if ($account && !$account->details_submitted){
            return redirect()->route('createOnboardingAccountLink');
        }

        Session::forget('influencer_follower_error');
        Session::forget('influencer_error');
        $user = User::find(Auth::id());

        $social_connect_twitter = SocialConnect::where('media', 'twitter')->where('user_id', Auth::id())->first();
        $social_connect_youtube = SocialConnect::where('media', 'youtube')->where('user_id', Auth::id())->first();
        $social_connect_twitch = SocialConnect::where('media', 'twitch')->where('user_id', Auth::id())->first();
        $social_connect_facebook = SocialConnect::where('media', 'facebook')->where('user_id', Auth::id())->first();
        $social_connect_instagram = SocialConnect::where('media', 'instagram')->where('user_id', Auth::id())->first();
        $social_connect_tiktok = SocialConnect::where('media', 'tiktok')->where('user_id', Auth::id())->first();
        $social_connect_snapchat = SocialConnect::where('media', 'snapchat')->where('user_id', Auth::id())->first();

        $atleastOneSocialMediaConnected = $social_connect_twitter || $social_connect_youtube || 
                                  $social_connect_twitch || $social_connect_facebook || 
                                  $social_connect_instagram || $social_connect_tiktok || 
                                  $social_connect_snapchat;

        $socialFollowerCount = SocialConnect::whereUserId(Auth::id())->get()->sortByDesc('followers');
        $influencers = User::whereUserType('influencer')->where('id', '!=', Auth::id())->get();
        $totalMedias = ['twitter', 'youtube', 'twitch', 'facebook', 'instagram', 'tiktok', 'snapchat'];
        $medias = [];

        foreach ($socialFollowerCount as $social) {
            $medias[] = $social->media;
        }

        $remainingMedias = array_diff($totalMedias, $medias);

        $videoDemandPrice = 0;
        $addVideoDemandPrice = 0;
        $livestreamPrice = 0;
        $addLivestreamPrice = 0;
        $sharePrice = 0;

        $follower = ($socialFollowerCount->count('followers') > 0) ? $socialFollowerCount->count('followers') : 1;

        $videoDemandPrice = $influencers->sum('video_price') /  $follower;
        $livestreamPrice = $influencers->sum('livestream_price') /  $follower;
        $addVideoDemandPrice = $influencers->sum('video_price_additional') /  $follower;
        $addLivestreamPrice = $influencers->sum('livestream_price_additional') /  $follower;
        $sharePrice = $influencers->sum('sharecontent_price') /  $follower;

        $category = Category::get();

        $influencer_detail = InfluencerDetail::where('user_id', Auth::id())->first();
        $hashtags = Hashtag::where('user_id', Auth::id())->get('tags');
        $advertising_methods = AdvertisingMethodPrice::where('user_id', Auth::id())->get();

        $hashtag =  $hashtags;

        $country = Country::get();
        $social_connect = SocialConnect::where('user_id', Auth::id())->first();

        $social_connect_data = SocialConnect::where('user_id', Auth::id())->get();
        $social_connect_other = SocialConnect::leftjoin('influencer_details',  'social_connects.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('hashtags',  'hashtags.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->select('influencer_details.*', 'social_connects.*', 'hashtags.tags')
            ->where('users.id', '!=', Auth::id())
            ->groupBy('social_connects.media')
            ->get();
        foreach ($social_connect_other as $row) {
            $hashtags = Hashtag::where('user_id', $row->user_id)->get('tags');
            $row->tags =  $hashtags;
        }

        foreach ($social_connect_other as $row) {
            $cat = Category::where('id', $row->category_id)->get('name');
            $row->category =   $cat->implode('name', ',');
        }

        $hashtag_arr = [];
        $count = Hashtag::groupBy('tags')->get();
        foreach ($count as $row) {
            $count_arr =  Hashtag::where('tags', $row['tags'])->count();
            if ($count_arr > 2) {
                array_push($hashtag_arr, $row['tags']);
            }
        }

        $collection = Session::get('collection');
        $social_connect_arr = [
            $social_connect_twitter,
            $social_connect_youtube,
            $social_connect_twitch,
            $social_connect_facebook,
            $social_connect_instagram,
            $social_connect_tiktok,
            $social_connect_snapchat,
        ];

        $newSocialConnectArr = [];
        foreach ($social_connect_arr as $sortArr) {
            if ($sortArr && $sortArr['media'] != 'twitter') {
                $newSocialConnectArr[$sortArr['media']] = $sortArr['followers'];
            }
        }
        arsort($newSocialConnectArr);

        $type_count = AdvertisingMethodNewPrice::where('user_id', Auth::id())->groupBy('media')->count();

        $sm_campaigns = SmCampaign::get();

        if (isset($stripeAccount)) {
            $videosApi = 'https://api.stripe.com/v1/accounts/' . $stripeAccount->stripe_user_id;
            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_HTTPHEADER => array(
                    "Authorization: Bearer " . env('STRIPE_SECRET')
                ),
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_URL => $videosApi
            ));

            $response1 = curl_exec($curl);
            $err = curl_error($curl);
            curl_close($curl);
            $userAccountDtail1 = json_decode($response1);

            $stripeAccount->email = isset($userAccountDtail1->email) ? $userAccountDtail1->email : '';
        }

        $countries = Country::get();
        $cities =  DB::table('states')->join('cities',  'states.id', '=', 'cities.state_id')
            ->select('cities.*')
            ->where('states.country_id', Auth::user()->country)->get();
        $states = State::find(Auth::user()->state);

        $social_max_followers = SocialConnect::where('user_id', Auth::id())->orderByRaw('CONVERT(followers, SIGNED) desc')->first();
        $social_connect_high  = SocialConnect::where('user_id', Auth::id())->orderByRaw('CONVERT(followers, SIGNED) desc')->first();

        ////////////////////
        // Moved in from influencer-onboarding.blade.php template
        ////////////////////
        $influencerDetail = InfluencerDetail::where('user_id', Auth::id())->first();

        $tot_count = InfluencerRequestDetail::where('influencer_detail_id', @$influencerDetail->id)
            ->where('finish', NULL)
            ->where('refund_reason', NULL)
            ->where(function ($query) {
                $query->where('review', '!=', 1);
            })
            ->count();

        $open_count = InfluencerRequestDetail::join('influencer_request_accepts', 'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->select('influencer_request_details.*')
            ->where('influencer_request_details.influencer_detail_id', @$influencerDetail->id)
            ->where('influencer_request_details.finish', NULL)
            ->where('influencer_request_details.refund_reason', NULL)
            ->where(function ($query) {
                $query->where('influencer_request_details.review', '=', NULL)
                    ->orWhere('influencer_request_details.review', '=', 0);
            })
            ->count();

        $req_count = $tot_count;

        $myCampaignList = InfluencerRequestDetail::join('influencer_details', 'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts', 'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->select(
                'influencer_request_details.*',
                'influencer_details.id as i_id',
                'influencer_details.user_id as i_user_id',
                'influencer_request_accepts.request',
                'influencer_request_accepts.request_time',
                'influencer_request_accepts.request_time_accept',
                'influencer_request_accepts.id as influencer_request_accept_id',
                'influencer_request_accepts.created_at as accept_time'
            )
            ->where('influencer_details.user_id', Auth::id())
            ->orderBy('influencer_request_details.id', 'desc')
            ->get();

        $postCount = 0;
        $postCountReq = 0;
        foreach ($myCampaignList as $campaign) {
            if (
                $campaign->request == 1 &&
                $campaign->invoice_id != '' &&
                $campaign->review != '1' &&
                $campaign->review != '0' &&
                $campaign->refund_reason == ''
            ) {
                $postCountReq++;
                if ($campaign->invoice_id != '') {
                    $postCount++;
                }
            }
        }

        $reqCountList = InfluencerRequestDetail::join('influencer_details', 'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts', 'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->select(
                'influencer_request_details.*',
                'influencer_details.id as i_id',
                'influencer_details.user_id as i_user_id',
                'influencer_request_accepts.request',
                'influencer_request_accepts.request_time',
                'influencer_request_accepts.request_time_accept',
                'influencer_request_accepts.id as influencer_request_accept_id'
            )
            ->where('influencer_details.user_id', Auth::id())
            ->orderBy('influencer_request_details.id', 'desc')
            ->get();

        $campaignRequestTime = CampaignRequestTime::first();
        $reqCount = 0;
        $openCountReq = 0;
        foreach ($reqCountList as $reqCountItem) {
            if ($reqCountItem->invoice_id == '' && $reqCountItem->review != '2' && $reqCountItem->refund_reason == '') {
                $time = (isset($reqCountItem->request_time_accept) && $reqCountItem->request_time_accept == 1) ?
                    $reqCountItem->request_time + $reqCountItem->time : $reqCountItem->time;
                $created_date = date('Y-m-d H:i:s', strtotime($reqCountItem->created_at));
                $updated_date = date('Y-m-d H:i:s', strtotime($reqCountItem->updated_at));
                $campaignDate = date('Y-m-d H:i:s', strtotime($created_date . ' + ' . $campaignRequestTime->request_time . ' days'));
                $date = date('Y-m-d H:i:s');
                $seconds = strtotime($campaignDate) - strtotime($date);

                $days = floor($seconds / 86400);
                if ($date <= $campaignDate) {
                    $openCountReq++;
                    $reqCount++;
                }
            }
        }
        
        $tot = $reqCount;
        $req_count = $postCountReq + $openCountReq;
        /////////////////////
        // Moving in finished
        /////////////////////

        return view('front-user.pages.influencer-onboarding', compact(
            'user',
            'social_connect_twitter',
            'social_connect_youtube',
            'social_connect_twitch',
            'social_connect_facebook',
            'social_connect_instagram',
            'category',
            'influencer_detail',
            'hashtag',
            'advertising_methods',
            'country',
            'social_connect',
            'hashtag_arr',
            'social_connect_tiktok',
            'social_connect_snapchat',
            'socialFollowerCount',
            'videoDemandPrice',
            'addVideoDemandPrice',
            'livestreamPrice',
            'addLivestreamPrice',
            'sharePrice',
            'social_connect_data',
            'social_connect_other',
            'remainingMedias',
            'collection',
            'newSocialConnectArr',
            'type_count',
            'sm_campaigns',
            'stripeAccount',
            'countries',
            'cities',
            'states',
            'social_max_followers',
            'social_connect_high',
            // moved in values
            'influencerDetail',
            'tot_count',
            'open_count',
            'req_count',
            'myCampaignList',
            'postCount',
            'myCampaignList',
            'reqCountList',
            'campaignRequestTime',
            'reqCount',
            'openCountReq',
            'tot',
            'atleastOneSocialMediaConnected'
        ));
    }



    public function getSocialconnect()
    {
        $social_connect = SocialConnect::where('user_id', Auth::id())->first();

        return response()->json(['code' => '200', 'social_connect' => $social_connect]);
    }


    public function latestSocialconnect()
    {
        // $social_connect_twitter = SocialConnect::where('media', 'twitter')->where('user_id', Auth::id())->first();
        // $social_connect_youtube = SocialConnect::where('media', 'youtube')->where('user_id', Auth::id())->first();
        // $social_connect_twitch = SocialConnect::where('media', 'twitch')->where('user_id', Auth::id())->first();
        // $social_connect_snapchat = SocialConnect::where('media', 'snapchat')->where('user_id', Auth::id())->first();
        // $social_connect_tiktok = SocialConnect::where('media', 'tiktok')->where('user_id', Auth::id())->first();
        $social_connect_facebook = SocialConnect::where('media', 'facebook')->where('user_id', Auth::id())->first();
        $social_connect_instagram = SocialConnect::where('media', 'instagram')->where('user_id', Auth::id())->first();

        $user = User::find(Auth::id());
        $social_connect = SocialConnect::where('user_id', Auth::id())->first();

        $social_connect_data = SocialConnect::where('user_id', Auth::id())->get();
        $social_connect_arr = [
            // $social_connect_twitter,
            // $social_connect_youtube,
            // $social_connect_twitch,
            // $social_connect_tiktok,
            // $social_connect_snapchat,
            $social_connect_facebook,
            $social_connect_instagram,
        ];

        $newSocialConnectArr = [];
        foreach ($social_connect_arr as $sortArr) {
            if ($sortArr && $sortArr['media'] != 'twitter') {
                $newSocialConnectArr[$sortArr['media']] = $sortArr['followers'];
            }
        }

        arsort($newSocialConnectArr);

        $sm_campaigns = SmCampaign::get();
        return view(
            'front-user.pages.influencer-onboarding-socialconnect', compact(
                // 'social_connect_snapchat',
                // 'social_connect_tiktok',
                // 'social_connect_twitter',
                // 'social_connect_youtube',
                // 'social_connect_twitch',
                'social_connect_facebook',
                'social_connect_instagram',
                'user',
                'social_connect',
                'social_connect_data',
                'newSocialConnectArr',
                'sm_campaigns'
            )
        );
    }

    public function latestSocialconnectData()
    {
        $social_connect_data = SocialConnect::where('user_id', Auth::id())->get();
        $social_connect_other = SocialConnect::leftjoin('influencer_details',  'social_connects.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('hashtags',  'hashtags.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->select('influencer_details.*', 'social_connects.*', 'hashtags.tags')
            ->where('users.id', '!=', Auth::id())
            ->groupBy('social_connects.media')
            ->get();
        foreach ($social_connect_other as $row) {
            $hashtags = Hashtag::where('user_id', $row->user_id)->get('tags');
            $row->tags =  $hashtags;
            // $row->tags =  '#'.$hashtags->implode('tags', '#') ;
        }

        foreach ($social_connect_other as $row) {
            $cat = Category::where('id', $row->category_id)->get('name');
            $row->category =   $cat->implode('name', ',');
        }
        $sm_campaigns = SmCampaign::get();
        return view('front-user.pages.influencer-publish', compact('social_connect_data', 'social_connect_other', 'sm_campaigns'));
    }

    public function getInfluencerData()
    {
        $social_connect_other = SocialConnect::leftjoin('influencer_details',  'social_connects.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('hashtags',  'hashtags.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->select('influencer_details.*', 'social_connects.*', 'hashtags.tags')
            ->where('users.id', Auth::id())
            ->groupBy('social_connects.media')
            ->get();
        foreach ($social_connect_other as $row) {
            $hashtags = Hashtag::where('user_id', $row->user_id)->get('tags');
            $row->tags =  $hashtags;
            // $row->tags =  '#'.$hashtags->implode('tags', '#') ;
        }

        foreach ($social_connect_other as $row) {
            $cat = Category::where('id', $row->category_id)->get('name');
            $row->category =   $cat->implode('name', ',');
        }

        return response()->json(['code' => '200', 'social_connect_other' => $social_connect_other]);
    }
    public function latestAdvertising()
    {
        $social_connect_twitter = SocialConnect::where('media', 'twitter')->where('user_id', Auth::id())->first();
        $social_connect_youtube = SocialConnect::where('media', 'youtube')->where('user_id', Auth::id())->first();
        $social_connect_twitch = SocialConnect::where('media', 'twitch')->where('user_id', Auth::id())->first();
        $social_connect_facebook = SocialConnect::where('media', 'facebook')->where('user_id', Auth::id())->first();
        $social_connect_snapchat = SocialConnect::where('media', 'snapchat')->where('user_id', Auth::id())->first();
        $social_connect_tiktok = SocialConnect::where('media', 'tiktok')->where('user_id', Auth::id())->first();
        $social_connect_instagram = SocialConnect::where('media', 'instagram')->where('user_id', Auth::id())->first();
        
        $user = User::find(Auth::id());
        $advertising_methods = AdvertisingMethodPrice::where('user_id', Auth::id())->get();
        $sm_campaigns = SmCampaign::get();
        $socialFollowerCount = SocialConnect::whereUserId(Auth::id())->get()->sortByDesc('followers');
        $social_connect_high  = SocialConnect::where('user_id', Auth::id())->orderByRaw('CONVERT(followers, SIGNED) desc')->first();

        $remainingMedias = ['instagram'];
        $livestreamPrice = 0;
        $addLivestreamPrice = 0;
        $sharePrice = 0;

        return view(
            'front-user.pages.influencer-advertising',
            compact(
                'social_connect_twitter',
                'social_connect_high',
                'social_connect_youtube',
                'social_connect_twitch',
                'social_connect_facebook',
                'social_connect_instagram',
                'user',
                'advertising_methods',
                'social_connect_snapchat',
                'social_connect_tiktok',
                'sm_campaigns',
                'socialFollowerCount',
                'remainingMedias',
                'livestreamPrice',
                'addLivestreamPrice',
                'sharePrice'
            )
        );
    }

    public function saveInfluencerWizard(Request $request)
    {
        $formData = request()->except(['_token']);
        $formData['flag'] = 1;
        User::find(Auth::id())->update($formData);

        return response()->json(['code' => '200', 'msg' => 'Account details saved successfully!!']);
    }



    public function hashtags(Request $request)
    {
        $tags = $_GET['query'];

        $query = Hashtag::where("tags", "like", "%$tags%");
        $count = $query->count();
        if ($count > 2) {
            $return_arr = $query->groupBy('tags')->get();
            $json = [];
            foreach ($return_arr as $row) {
                $json[] = $row['tags'];
            }
            echo json_encode($json);
            // return  $return_arr ;
        }
    }

    public function getAdminPricing(Request $request)
    {
        $social_connect_media = SocialConnect::where('user_id', Auth::id())->where('media', $request->media)->first();
        if (!isset($social_connect_media)) {
            // Return error response when social media connection is not found
            return response()->json(['code' => '404', 'price' => '0.00', 'price_next' => '0.00', 'error' => 'Social media connection not found']);
            // TODO should we need this?
            // $social_connect_high =  SocialConnect::where('user_id', Auth::id())->orderByRaw('CONVERT(followers, SIGNED) desc')->first();
            // $social_connect_media = SocialConnect::where('user_id', Auth::id())->where('media', $social_connect_high->media)->first();
        }

        $pricingData = AdminGamification::where('select_type', 'Pricing & Rank')->where('type', Auth::user()->trophy)->first();
        $price = 0;
        $price_next = 0;

        $cpt_price = $social_connect_media->followers / 1000;
        $pricingData_next = AdminGamification::where('id', $pricingData->id + 1)->first();
        if ($request->type == 'Boost me') {
            $boostData = AdminPricing::where('media', ucfirst($social_connect_media->media))
                ->where('type', 'Boost me')
                ->where(function ($query) {
                    $query->where('country', '=', Auth::user()->country)
                        ->orWhere('country', '=', 'Standard');
                })
                ->where(function ($query) use ($social_connect_media) {
                    $query->where('range', '>', $social_connect_media->followers)
                        ->orWhere('range', '=', 'All');
                })
                ->latest('id')->first();

            if (isset($boostData->cpt)) {
                $price = $boostData->cpt * ((intval($cpt_price) != 0) ? intval($cpt_price) : 1);
                $price_next = ($boostData->cpt * $pricingData_next->pricing) / 100;
            }
        } elseif ($request->type == 'Reaction video') {
            $reactionData = AdminPricing::where('media', ucfirst($social_connect_media->media))
                ->where('type', 'Reaction-Video')
                ->where(function ($query) {
                    $query->where('country', '=', Auth::user()->country)
                        ->orWhere('country', '=', 'Standard');
                })
                ->where(function ($query) use ($social_connect_media) {
                    $query->where('range', '>', $social_connect_media->followers)
                        ->orWhere('range', '=', 'All');
                })
                ->latest('id')->first();

            if (isset($reactionData->cpt)) {
                $price = $reactionData->cpt * ((intval($cpt_price) != 0) ? intval($cpt_price) : 1);
                $price_next = ($reactionData->cpt * $pricingData_next->pricing) / 100;
            }
        } elseif ($request->type == 'Survey') {
            $surveyData = AdminPricing::where('media', ucfirst($social_connect_media->media))
                ->where('type', 'Survey')
                ->where(function ($query) {
                    $query->where('country', '=', Auth::user()->country)
                        ->orWhere('country', '=', 'Standard');
                })
                ->where(function ($query) use ($social_connect_media) {
                    $query->where('range', '>', $social_connect_media->followers)
                        ->orWhere('range', '=', 'All');
                })
                ->latest('id')->first();

            if (isset($surveyData->cpt)) {
                $price = $surveyData->cpt * ((intval($cpt_price) != 0) ? intval($cpt_price) : 1);
                $price_next = ($surveyData->cpt * $pricingData_next->pricing) / 100;
            }
        }

        $price = ($price * $pricingData->pricing) / 100;

        // If not a small business owner, include the 19% vat
        if (!Auth::user()->is_small_business_owner) {
            $price = $price + ($price * 0.19);
            $price_next = $price_next + ($price_next * 0.19);
        }

        // Deduct 20% CIF provision
        $price = (0.8) * $price;
        $price_next = (0.8) * $price_next;

        return response()->json(['code' => '200', 'price' => number_format($price, 2), 'price_next' => number_format($price_next, 2)]);
    }

    public function getAdminPricing_original(Request $request)
    {
        $social_connect_media = SocialConnect::where('user_id', Auth::id())->where('media', $request->media)->first();
        if (!isset($social_connect_media)) {
            $social_connect_high =  SocialConnect::where('user_id', Auth::id())->orderByRaw('CONVERT(followers, SIGNED) desc')->first();
            $social_connect_media = SocialConnect::where('user_id', Auth::id())->where('media', $social_connect_high->media)->first();
        }

        $pricingData = AdminGamification::where('select_type', 'Pricing & Rank')->where('type', Auth::user()->trophy)->first();
        $price = 0;
        $price_next = 0;

        $cpt_price = $social_connect_media->followers / 1000;
        $pricingData_next = AdminGamification::where('id', $pricingData->id + 1)->first();
        if ($request->type == 'Boost me') {
            $boostData = AdminPricing::where('media', ucfirst($social_connect_media->media))
                ->where('type', 'Boost me')
                ->where(function ($query) {
                    $query->where('country', '=', Auth::user()->country)
                        ->orWhere('country', '=', 'Standard');
                })
                ->where(function ($query) use ($social_connect_media) {
                    $query->where('range', '>', $social_connect_media->followers)
                        ->orWhere('range', '=', 'All');
                })
                ->latest('id')->first();

            if (isset($boostData->cpt)) {
                $price = $boostData->cpt * ((intval($cpt_price) != 0) ? intval($cpt_price) : 1);
                $price_next = ($boostData->cpt * $pricingData_next->pricing) / 100;
                $price_next = (80 / 100) * $price_next;
            } else {
                $price = 0;
                $price_next = 0;
            }

            $price = ($price * $pricingData->pricing) / 100;
            $price = (80 / 100) * $price;
        } elseif ($request->type == 'Reaction video') {
            $reactionData = AdminPricing::where('media', ucfirst($social_connect_media->media))
                ->where('type', 'Reaction-Video')
                ->where(function ($query) {
                    $query->where('country', '=', Auth::user()->country)
                        ->orWhere('country', '=', 'Standard');
                })
                ->where(function ($query) use ($social_connect_media) {
                    $query->where('range', '>', $social_connect_media->followers)
                        ->orWhere('range', '=', 'All');
                })
                ->latest('id')->first();

            if (isset($reactionData->cpt)) {
                $price = $reactionData->cpt * ((intval($cpt_price) != 0) ? intval($cpt_price) : 1);
                $price_next = ($reactionData->cpt * $pricingData_next->pricing) / 100;
                $price_next = (80 / 100) * $price_next;
            } else {
                $price = 0;
                $price_next = 0;
            }

            $price = ($price * $pricingData->pricing) / 100;
            $price = (80 / 100) * $price;
        } elseif ($request->type == 'Survey') {
            $surveyData = AdminPricing::where('media', ucfirst($social_connect_media->media))
                ->where('type', 'Survey')
                ->where(function ($query) {
                    $query->where('country', '=', Auth::user()->country)
                        ->orWhere('country', '=', 'Standard');
                })
                ->where(function ($query) use ($social_connect_media) {
                    $query->where('range', '>', $social_connect_media->followers)
                        ->orWhere('range', '=', 'All');
                })
                ->latest('id')->first();

            if (isset($surveyData->cpt)) {
                $price = $surveyData->cpt * ((intval($cpt_price) != 0) ? intval($cpt_price) : 1);
                $price_next = ($surveyData->cpt * $pricingData_next->pricing) / 100;
                $price_next = (80 / 100) * $price_next;
            } else {
                $price = 0;
                $price_next = 0;
            }

            $price = ($price * $pricingData->pricing) / 100;
            $price = (80 / 100) * $price;
        }

        return response()->json(['code' => '200', 'price' => number_format($price, 2), 'price_next' => number_format($price_next, 2)]);
    }

    public function marketplaceSearch(Request $request)
    {
        $influencerDetails =  InfluencerDetail::leftjoin('social_connects',  'social_connects.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('advertising_method_prices',  'advertising_method_prices.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('hashtags',  'hashtags.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_requests', function ($join) {
                $join->on('influencer_details.id', '=', 'influencer_requests.influencer_id')->where('influencer_requests.user_id', '=', Auth::id());
            })
            ->select('influencer_details.*', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'social_connects.url', 'social_connects.picture', 'social_connects.name as username', 'social_connects.followers', 'advertising_method_prices.*', 'hashtags.tags', 'influencer_requests.request as request');

        if (isset($request->target_age) && $request->target_age != 'Select') {
            $influencerDetails->where('influencer_details.ages', $request->target_age);
        }
        if (isset($request->language) && $request->language != 'Select') {
            $influencerDetails->where('influencer_details.content_language', $request->language);
        }
        if (isset($request->content_attracts) && $request->content_attracts != 'Select') {
            $influencerDetails->where('influencer_details.content_attracts', $request->content_attracts);
        }

        if (isset($request->gender) && $request->gender != 'Select') {
            $influencerDetails->where('influencer_details.gender', $request->gender);
        }
        if (isset($request->hashtags) && $request->hashtags != 'Select') {

            $condition = " 1 = 1 ";
            $subject_con_arr = [];
            foreach ($request->hashtags as $row) {
                if ($row != '' && $row != 'Select') {
                    $subject_con_arr[] = " FIND_IN_SET('" . $row . "', hashtags.tags) ";
                }
            }
            if (count($subject_con_arr) > 0) {
                $condition .= ' and (' . implode(' or ', $subject_con_arr) . ')';
            }
            // dd($condition);
            //$influencerDetails->whereRaw(" FIND_IN_SET('" . $request->hashtags . "', hashtags.tags)");
            $influencerDetails->whereRaw($condition);
        }


        if (isset($request->influencer_type) && $request->influencer_type != 'Select') {
            $influencerDetails->where('influencer_details.influencer_type', $request->influencer_type);
        }
        if (isset($request->price)) {
            // $price = explode('-', $request->price);
            // dd($price);
            // $influencerDetails->whereBetween('price', [(int)$price[0],(int)$price[1]]);

            $fieldName = $request->advertising . '_price';
            if ($request->price > 0) {
                $influencerDetails->where('advertising_method_prices.' . $fieldName, '<=', (int)$request->price);
            }
            // $influencerDetails->where('advertising_method_prices.price','<=',$price[1]);
        }

        if (isset($request->followers)) {
            // $followers = explode('-', $request->followers);
            // dd($followers);
            // $influencerDetails->whereBetween('followers', [(int)$followers[0],(int)$followers[1]]);
            // $influencerDetails->where('social_connects.followers','>',$followers[0]);

            if ($request->followers > 0) {
                $influencerDetails->where('social_connects.followers', '>', (int)$request->followers);
            }
        }

        if (isset($request->media)) {
            $fieldName = $request->advertising . '_media';
            $influencerDetails->where('advertising_method_prices.' . $fieldName, $request->media);
            $influencerDetails->where('social_connects.media', $request->media);
        }
        if (isset($request->advertising)) {
            $fieldName = $request->advertising . '_media';
            $influencerDetails->where('advertising_method_prices.' . $fieldName, $request->media);
            $influencerDetails->where('advertising_method_prices.' . $request->advertising, 1);
        }
        if (isset($request->category_id)) {

            $condition = " 1 = 1 ";
            $subject_con_arr = [];
            foreach ($request->category_id as $cat) {
                if ($cat != '') {

                    $subject_con_arr[] = " FIND_IN_SET('" . $cat . "', influencer_details.category_id) ";
                }
            }
            if (count($subject_con_arr) > 0) {
                $condition .= ' and (' . implode(' or ', $subject_con_arr) . ')';
            }
            // dd($condition);
            $influencerDetails->whereRaw($condition);

            // $influencerDetails->whereIn('influencer_details.category_id', $request->category_id  );
        }
        $influencerDetails = $influencerDetails->where('influencer_details.publish', 'Publish')->where('users.status', 1);
        if (isset($request->sort_by) && $request->sort_by != 'Select') {
            $sort = explode('-', $request->sort_by);
            $param = $sort[0];
            $order = $sort[1];
            if ($sort[0] == 'price') {
                $param = $request->advertising . '_price';
            }
            $influencerDetails = $influencerDetails->orderByRaw('LENGTH(' . $param . ') ' . $order . '')->orderBy($param, $order);
        } else {
            $param = 'influencer_details.id';
            $order = 'DESC';
            $influencerDetails = $influencerDetails->orderBy($param, $order);
        }

        $influencerDetails = $influencerDetails->groupBy('influencer_details.id')->get();

        foreach ($influencerDetails as $row) {
            $hashtags = Hashtag::where('user_id', $row->i_user_id)->get('tags');
            $row->tags =  $hashtags;
            // $row->tags =  '#'.$hashtags->implode('tags', '#') ;
        }

        foreach ($influencerDetails as $row) {

            $category = Category::where('id', $row->category_id)->get('name');
            $row->category =   $category->implode('name', ',');
        }

        $categories = Category::whereIn('id', $request->category_id)->get();
        $advertising = $request->advertising;
        $media =  $request->media;
        //$hashtags = Hashtag::groupBy('tags')->get();

        $influencerData =   InfluencerDetail::leftjoin('social_connects',  'social_connects.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('advertising_method_prices',  'advertising_method_prices.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('hashtags',  'hashtags.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_requests', function ($join) {
                $join->on('influencer_details.id', '=', 'influencer_requests.influencer_id')->where('influencer_requests.user_id', '=', Auth::id());
            })
            ->select('influencer_details.*', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'social_connects.media', 'social_connects.url', 'social_connects.picture', 'social_connects.name as username', 'social_connects.followers', 'advertising_method_prices.*', 'hashtags.tags', 'influencer_requests.request as request', DB::raw('(MAX(social_connects.followers)) AS max_followers'))
            ->where('influencer_details.publish', 'Publish')->where('users.status', 1);


        if (isset($request->media)) {
            $influencerData->where('social_connects.media', $request->media);
        }
        if (isset($request->advertising)) {
            $fieldName = $request->advertising . '_media';
            $influencerDetails->where('advertising_method_prices.' . $fieldName, $request->media);
            $influencerDetails->where('advertising_method_prices.' . $advertising, 1);
            // $influencerData->where('advertising_method_prices.name',$request->advertising);
        }
        if (isset($request->category_id)) {

            $condition = " 1 = 1 ";
            $subject_con_arr = [];
            foreach ($request->category_id as $cat) {
                if ($cat != '') {

                    $subject_con_arr[] = " FIND_IN_SET('" . $cat . "', influencer_details.category_id) ";
                }
            }
            if (count($subject_con_arr) > 0) {
                $condition .= ' and (' . implode(' or ', $subject_con_arr) . ')';
            }
            // dd($condition);
            $influencerData->whereRaw($condition);

            // $influencerDetails->whereIn('influencer_details.category_id', $request->category_id  );
        }
        $influencerData = $influencerData->groupBy('influencer_details.id')->get();

        $hashtags = '';
        foreach ($influencerData as $row) {
            $hashtags = Hashtag::where('user_id', $row->i_user_id)->get('tags');
            $row->tags =  $hashtags;
            // $row->tags =  '#'.$hashtags->implode('tags', '#') ;
        }
        foreach ($influencerData as $row) {

            $category = Category::where('id', $row->category_id)->get('name');
            $row->category =   $category->implode('name', ',');
        }
        // dd(array_keys($influencerData, max($influencerData)));

        $formData = request()->except(['_token']);

        $max_price = $influencerData->max('max_price');
        $max_followers = $influencerData->max('max_followers');





        $totalCount = InfluencerRequest::where('user_id', Auth::id())->count();

        $totalFollowers = InfluencerRequest::leftjoin('social_connects',  'social_connects.user_id', '=', 'influencer_requests.user_id')
            ->where('influencer_requests.user_id', Auth::id())
            ->count('social_connects.followers');

        $totalPrice = InfluencerRequest::leftjoin('advertising_method_prices',  'advertising_method_prices.user_id', '=', 'influencer_requests.user_id')
            ->where('influencer_requests.user_id', Auth::id())->first();
        // dd($influencerDetails);
        return \Response::json(\View::make('front-user.pages.marketplace-influencer-list', compact('influencerDetails', 'categories', 'advertising', 'media', 'hashtags', 'influencerData', 'max_followers', 'max_price', 'formData', 'totalCount', 'totalFollowers', 'totalPrice'))->render());
    }



    public function marketplaceSidebar(Request $request)
    {
        $influencerDetails =  InfluencerDetail::leftjoin('social_connects',  'social_connects.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('advertising_method_prices',  'advertising_method_prices.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('hashtags',  'hashtags.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_requests', function ($join) {
                $join->on('influencer_details.id', '=', 'influencer_requests.influencer_id')->where('influencer_requests.user_id', '=', Auth::id());
            })
            ->select('influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_details.*', 'social_connects.media', 'social_connects.url', 'social_connects.picture', 'social_connects.name as username', 'social_connects.followers', 'advertising_method_prices.*', 'hashtags.tags', 'influencer_requests.request as request');

        if (isset($request->target_age) && $request->target_age != 'Select') {
            $influencerDetails->where('influencer_details.ages', $request->target_age);
        }
        if (isset($request->language) && $request->language != 'Select') {
            $influencerDetails->where('influencer_details.content_language', $request->language);
        }
        if (isset($request->content_attracts) && $request->content_attracts != 'Select') {
            $influencerDetails->where('influencer_details.content_attracts', $request->content_attracts);
        }

        if (isset($request->gender) && $request->gender != 'Select') {
            $influencerDetails->where('influencer_details.gender', $request->gender);
        }
        if (isset($request->hashtags) && $request->hashtags != 'Select') {

            $influencerDetails->whereRaw(" FIND_IN_SET('" . $request->hashtags . "', hashtags.tags)");
        }


        if (isset($request->influencer_type) && $request->influencer_type != 'Select') {
            $influencerDetails->where('influencer_details.influencer_type', $request->influencer_type);
        }
        if (isset($request->price)) {
            if ($request->price > 0) {

                $fieldName = $request->advertising . '_price';
                // $influencerDetails->where('advertising_method_prices.'.$fieldName,'<=',(int)$request->price);
                $influencerDetails->where('advertising_method_prices.' . $fieldName, '<=', (int)$request->price);
            }
        }

        if (isset($request->followers)) {
            if ((int)$request->followers > 0) {
                $influencerDetails->where('social_connects.followers', '>', (int)$request->followers);
            }
        }



        if (isset($request->media)) {
            $fieldName = $request->advertising . '_media';
            $influencerDetails->where('advertising_method_prices.' . $fieldName, $request->media);
        }
        if (isset($request->advertising)) {
            $fieldName = $request->advertising . '_media';
            $influencerDetails->where('advertising_method_prices.' . $fieldName, $request->media);
            $influencerDetails->where('advertising_method_prices.' . $request->advertising, 1);
        }

        if (isset($request->category_id)) {

            $condition = " 1 = 1 ";
            $subject_con_arr = [];
            foreach ($request->category_id as $cat) {
                if ($cat != '') {

                    $subject_con_arr[] = " FIND_IN_SET('" . $cat . "', influencer_details.category_id) ";
                }
            }
            if (count($subject_con_arr) > 0) {
                $condition .= ' and (' . implode(' or ', $subject_con_arr) . ')';
            }
            // dd($condition);
            $influencerDetails->whereRaw($condition);

            // $influencerDetails->whereIn('influencer_details.category_id', $request->category_id  );
        }
        $influencerDetails = $influencerDetails->where('influencer_details.publish', 'Publish')->where('users.status', 1);
        if (isset($request->sort_by) && $request->sort_by != 'Select') {
            $sort = explode('-', $request->sort_by);
            $param = $sort[0];
            $order = $sort[1];
            if ($sort[0] == 'price') {
                $param = $request->advertising . '_price_' . $request->media;
            }
            $influencerDetails = $influencerDetails->orderByRaw('LENGTH(' . $param . ') ' . $order . '')->orderBy($param, $order);
        } else {
            $param = 'influencer_details.id';
            $order = 'DESC';
            $influencerDetails = $influencerDetails->orderBy($param, $order);
        }

        $influencerDetails = $influencerDetails->groupBy('influencer_details.id')->get();

        foreach ($influencerDetails as $row) {
            $hashtags = Hashtag::where('user_id', $row->i_user_id)->get('tags');
            $row->tags =  $hashtags;
            // $row->tags =  '#'.$hashtags->implode('tags', '#') ;
        }

        foreach ($influencerDetails as $row) {

            $category = Category::where('id', $row->category_id)->get('name');
            $row->category =   $category->implode('name', ',');
        }

        $categories = Category::whereIn('id', $request->category_id)->get();
        $advertising = $request->advertising;
        $media =  $request->media;
        //$hashtags = Hashtag::groupBy('tags')->get();


        $influencerData =   InfluencerDetail::leftjoin('social_connects',  'social_connects.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('advertising_method_prices',  'advertising_method_prices.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('hashtags',  'hashtags.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_requests', function ($join) {
                $join->on('influencer_details.id', '=', 'influencer_requests.influencer_id')->where('influencer_requests.user_id', '=', Auth::id());
            })
            ->select('influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_details.*', 'social_connects.media', 'social_connects.url', 'social_connects.picture', 'social_connects.name as username', 'social_connects.followers', 'advertising_method_prices.*', 'hashtags.tags', 'influencer_requests.request as request', DB::raw('(MAX(social_connects.followers)) AS max_followers'))
            ->where('influencer_details.publish', 'Publish')->where('users.status', 1);


        if (isset($request->target_age) && $request->target_age != 'Select') {
            $influencerData->where('influencer_details.ages', $request->target_age);
        }
        if (isset($request->language) && $request->language != 'Select') {
            $influencerData->where('influencer_details.content_language', $request->language);
        }
        if (isset($request->content_attracts) && $request->content_attracts != 'Select') {
            $influencerData->where('influencer_details.content_attracts', $request->content_attracts);
        }

        if (isset($request->gender) && $request->gender != 'Select') {
            $influencerData->where('influencer_details.gender', $request->gender);
        }
        if (isset($request->hashtags) && $request->hashtags != 'Select') {

            $influencerData->whereRaw(" FIND_IN_SET('" . $request->hashtags . "', hashtags.tags)");
        }


        if (isset($request->influencer_type) && $request->influencer_type != 'Select') {
            $influencerData->where('influencer_details.influencer_type', $request->influencer_type);
        }
        if (isset($request->price)) {
            if ($request->price > 0) {
                $fieldName = $request->advertising . '_price_' . $request->media;
                $influencerData->where('advertising_method_prices.' . $fieldName, '<=', (int)$request->price);
                // $influencerData->where('advertising_method_prices.price','<=',(int)$request->price);
            }
        }

        if (isset($request->followers)) {
            if ($request->followers > 0) {
                $influencerData->where('social_connects.followers', '>', (int)$request->followers);
            }
        }

        if (isset($request->media)) {
            $fieldName = $request->advertising . '_media';
            $influencerDetails->where('advertising_method_prices.' . $fieldName, $request->media);
        }
        if (isset($request->advertising)) {
            $fieldName = $request->advertising . '_media';
            $influencerDetails->where('advertising_method_prices.' . $fieldName, $request->media);
            $influencerDetails->where('advertising_method_prices.' . $request->advertising, 1);
        }
        if (isset($request->category_id)) {

            $condition = " 1 = 1 ";
            $subject_con_arr = [];
            foreach ($request->category_id as $cat) {
                if ($cat != '') {

                    $subject_con_arr[] = " FIND_IN_SET('" . $cat . "', influencer_details.category_id) ";
                }
            }
            if (count($subject_con_arr) > 0) {
                $condition .= ' and (' . implode(' or ', $subject_con_arr) . ')';
            }
            // dd($condition);
            $influencerDetails->whereRaw($condition);

            // $influencerDetails->whereIn('influencer_details.category_id', $request->category_id  );
        }
        $influencerData = $influencerData->where('influencer_details.publish', 'Publish')->where('users.status', 1);
        if (isset($request->sort_by) && $request->sort_by != 'Select') {
            $sort = explode('-', $request->sort_by);
            $param = $sort[0];
            $order = $sort[1];

            if ($sort[0] == 'price') {
                $param = $request->advertising . '_price_' . $request->media;
            }

            $influencerData = $influencerData->orderByRaw('LENGTH(' . $param . ') ' . $order . '')->orderBy($param, $order);
        } else {
            $param = 'influencer_details.id';
            $order = 'DESC';
            $influencerData = $influencerData->orderBy($param, $order);
        }

        $influencerData = $influencerData->groupBy('influencer_details.id')->get();

        foreach ($influencerData as $row) {
            $hashtags = Hashtag::where('user_id', $row->i_user_id)->get('tags');
            $row->tags =  $hashtags;
            // $row->tags =  '#'.$hashtags->implode('tags', '#') ;
        }

        foreach ($influencerData as $row) {

            $category = Category::where('id', $row->category_id)->get('name');
            $row->category =   $category->implode('name', ',');
        }
        // dd(array_keys($influencerData, max($influencerData)));

        $formData = request()->except(['_token']);

        $max_price = $influencerData->max('max_price');
        $max_followers = $influencerData->max('max_followers');



        // dd($influencerData);


        $totalCount = InfluencerRequest::where('user_id', Auth::id())->count();

        $totalFollowers = InfluencerRequest::leftjoin('social_connects',  'social_connects.user_id', '=', 'influencer_requests.user_id')
            ->where('influencer_requests.user_id', Auth::id())
            ->count('social_connects.followers');

        $totalPrice = InfluencerRequest::leftjoin('advertising_method_prices',  'advertising_method_prices.user_id', '=', 'influencer_requests.user_id')
            ->where('influencer_requests.user_id', Auth::id())
            ->count();

        return \Response::json(\View::make('front-user.pages.marketplace-influencer-sidebar', compact('influencerDetails', 'categories', 'advertising', 'media', 'hashtags', 'influencerData', 'max_followers', 'max_price', 'formData', 'totalCount', 'totalFollowers', 'totalPrice'))->render());
    }


    public function selectRequest($id)
    {

        $exist = InfluencerRequest::where('user_id', Auth::id())->where('influencer_id', $id)->first();
        if ($exist) {
            if ($exist->request != 1) {
                $exist->request = 1;
                $exist->save();
            }
        } else {
            InfluencerRequest::create(
                [
                    'user_id' => Auth::id(),
                    'influencer_id' => $id,
                    'request' => 1
                ]
            );
        }

        return true;
    }

    public function removeRequest($influencer_id)
    {

        InfluencerRequest::where('user_id', Auth::id())->where('influencer_id', $influencer_id)->delete();
        return true;
    }
    public function removeRequestAll()
    {

        InfluencerRequest::where('user_id', Auth::id())->delete();
        return true;
    }




    public function requestNow(Request $request)
    {


        $influencerDetails = InfluencerDetail::leftjoin('social_connects',  'social_connects.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('advertising_method_prices',  'advertising_method_prices.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('hashtags',  'hashtags.user_id', '=', 'influencer_details.user_id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_requests', function ($join) {
                $join->on('influencer_details.id', '=', 'influencer_requests.influencer_id')->where('influencer_requests.user_id', '=', Auth::id());
            })
            ->select('influencer_details.*', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'social_connects.url', 'social_connects.picture', 'social_connects.name as username', 'social_connects.followers', 'advertising_method_prices.*', 'hashtags.tags', 'influencer_requests.request as request');


        if (isset($request->media)) {
            $fieldName = $request->advertising . '_media';
            $influencerDetails->where('advertising_method_prices.' . $fieldName, $request->media);
            $influencerDetails->where('social_connects.media', $request->media);
        }
        if (isset($request->advertising)) {
            $fieldName = $request->advertising . '_media';
            $influencerDetails->where('advertising_method_prices.' . $fieldName, $request->media);
            $influencerDetails->where('advertising_method_prices.' . $request->advertising, 1);
        }
        if (isset($request->category_id)) {
            $condition = " 1 = 1 ";
            $subject_con_arr = [];
            foreach ($request->category_id as $cat) {
                if ($cat != '') {
                    $subject_con_arr[] = " FIND_IN_SET('" . $cat . "', influencer_details.category_id) ";
                }
            }
            if (count($subject_con_arr) > 0) {
                $condition .= ' and (' . implode(' or ', $subject_con_arr) . ')';
            }
            $influencerDetails->whereRaw($condition);
        }
        $influencerData = $influencerDetails->where('influencer_details.publish', 'Publish')->where('users.status', 1)->groupBy('influencer_details.id')->get();

        foreach ($influencerData as $row) {
            $hashtags = Hashtag::where('user_id', $row->i_user_id)->get('tags');
            $row->tags =  $hashtags;
            // $row->tags =  '#'.$hashtags->implode('tags', '#') ;
        }
        foreach ($influencerData as $row) {

            $category = Category::where('id', $row->category_id)->get('name');
            $row->category =   $category->implode('name', ',');
        }

        $max_price = $influencerData->max('max_price');
        $max_followers = $influencerData->max('max_followers');


        $totalCount = InfluencerRequest::where('user_id', Auth::id())->count();

        $totalFollowers = InfluencerRequest::leftjoin('social_connects',  'social_connects.user_id', '=', 'influencer_requests.user_id')
            ->where('influencer_requests.user_id', Auth::id())
            ->count('social_connects.followers');

        $totalPrice = InfluencerRequest::leftjoin('advertising_method_prices',  'advertising_method_prices.user_id', '=', 'influencer_requests.user_id')
            ->where('influencer_requests.user_id', Auth::id())->first();

        $advertising = $request->advertising;
        $media =  $request->media;
        $categories =  isset($request->category_id) ? implode(',', $request->category_id) : '';

        $type = $request->type;
        $AdminComission = AdminComission::first();
        return \Response::json(\View::make('front-user.pages.marketplace-request', compact('influencerData', 'max_followers', 'max_price', 'totalCount', 'totalFollowers', 'totalPrice', 'advertising', 'media', 'categories', 'AdminComission', 'type'))->render());
    }

    public function requestNowSave(Request $request)
    {
        $this->validate($request, [
            'name' => 'required',
        ]);


        $formData = request()->except(['_token']);

        $canpaignCheck = InfluencerRequestDetail::orderBy('id', 'desc')->first();

        if ($canpaignCheck != '') {
            $formData['compaign_id'] = 'C-' . (sprintf('%06d',  intval(preg_replace('/[^0-9]/', '', $canpaignCheck->compaign_id)) + 1));
        } else {
            $formData['compaign_id'] = 'C-000001';
        }

        $InfluencerRequest = InfluencerRequest::where('user_id', Auth::id())->get();

        if (count($InfluencerRequest) == 0) {

            return back()->with('error', 'Please select at least one influencer to send request!');
        } else {
            $price = $formData['discount_price'];
            foreach ($InfluencerRequest as $row) {
                $formData['user_id'] = $row->user_id;
                $formData['influencer_detail_id'] = $row->influencer_id;
                $formData['request_id'] = $row->id;

                $formData['current_price'] = isset($formData['support']) ? $price : $formData['current_price'];

                $formData['discount_price'] =  $formData['discount_price'];

                $InfluencerDetail = InfluencerDetail::where('id', $row->influencer_id)->first();
                $influencer = User::whereId($InfluencerDetail->user_id)->first();

                $newLivetreamPrice = 0;
                $fieldName = $formData['advertising'] . '_price';
                if ($influencer->advertisingMethodPrice != null) {
                    $newLivetreamPrice = $influencer->advertisingMethodPrice->$fieldName;
                }
                $AdminComission = AdminComission::first();

                $influencer_price = ($newLivetreamPrice * $AdminComission->influencer) / 100;
                if ($influencer_price < 2) {
                    $influencer_price = 2;
                }




                $formData['influencer_price'] = $newLivetreamPrice - $influencer_price;

                InfluencerRequestDetail::create($formData);
                $user = User::whereId($row->user_id)->first();

                dispatch(new NewRequestInfluencer($influencer, $user, $formData));
                $influencer->notify(new RequestInfluencer($influencer, $user, $formData));
            }
            return redirect('/open-campaigns')->with('success', 'Request send successfully.');
        }
    }

    public function requestNowUpdate(Request $request)
    {
        $InfluencerRequest = InfluencerRequestDetail::where('compaign_id', $request->compaign_id)->get();

        $row = $InfluencerRequest[0];
        $selectInfluencer = $request->selectInfluencer;
        $price = $request->discount_price;
        $totalPrice = 0;
        if (isset($request->selectInfluencer)) {

            foreach ($InfluencerRequest as $influencer) {
                if (is_array($request->selectInfluencer)) {
                    if (!in_array($influencer->influencer_detail_id, $request->selectInfluencer)) {
                        InfluencerRequestDetail::where('compaign_id', $request->compaign_id)->where('influencer_detail_id', $influencer->influencer_detail_id)->delete();
                    }
                }
            }

            $Influencers = InfluencerRequestDetail::where('compaign_id', $request->compaign_id)->pluck('influencer_detail_id')->toArray();

            if (is_array($selectInfluencer)) {
                foreach ($selectInfluencer as $influencer) {

                    if ($influencer != '') {
                        $formData = request()->except(['_token', 'confirm', 'id', 'selectInfluencer', 'selectCard']);

                        $InfluencerDetail = InfluencerDetail::where('id', $influencer)->first();
                        $AdminComission = AdminComission::first();
                        $user = User::whereId($InfluencerDetail->user_id)->first();



                        $AdvertisingMethodNewPrice = AdvertisingMethodNewPrice::where('user_id', $InfluencerDetail->user_id)
                            ->where('type', $InfluencerRequest[0]->post_type)
                            ->where('media', $InfluencerRequest[0]->media)
                            ->first();
                        $formData['current_price'] = ($AdvertisingMethodNewPrice->type_price * 80) / 100;


                        if ($user->advertisingMethodPrice != null) {
                            $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                        }
                        $totalPrice = $newLivetreamPrice + $totalPrice;

                        if (isset($formData['support'])) {

                            $price = ($totalPrice *  ($AdminComission->influencer - 5)) / 100;
                            if ($price < 2) {
                                $price = 2;
                            }

                            $totalPrice = $price + $totalPrice;
                        } else {
                            $price = ($totalPrice *  5) / 100;
                            // $price =($totalPrice *  $AdminComission->influencer)/100;
                            if ($price < 2) {
                                $price = 2;
                            }
                            $totalPrice = $price + $totalPrice;
                        }
                        // $formData['current_price'] = $totalPrice;
                        if (!in_array($influencer, $Influencers)) {
                            if ($influencer != '') {


                                $formData['user_id'] = $row->user_id;
                                $formData['influencer_detail_id'] = $influencer;
                                $formData['request_id'] = $row->request_id;
                                $formData['current_price'] = $formData['current_price'];
                                $formData['discount_price'] =  isset($formData['support']) ? 1 : 0;
                                $formData['categories'] = $row->categories;
                                $formData['advertising'] = $row->advertising;
                                $formData['type'] = $row->type;
                                $formData['media'] = $row->media;


                                $newLivetreamPrice = 0;
                                $fieldName = $row->advertising . '_price';
                                if ($user->advertisingMethodPrice != null) {
                                    $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                                }
                                // $influencer_price = ($newLivetreamPrice * $AdminComission->influencer)/100;
                                $influencer_price = ($newLivetreamPrice * 5) / 100;

                                $formData['influencer_price'] = $newLivetreamPrice - $influencer_price;

                                InfluencerRequestDetail::create($formData);
                                $customer = User::whereId($row->user_id)->first();

                                dispatch(new NewRequestInfluencer($user, $customer, $formData));
                                $user->notify(new RequestInfluencer($user, $customer, $formData));
                            }
                        }
                    }
                }
            }
        }
        $formData = request()->except(['_token', 'confirm', 'id', 'selectInfluencer', 'selectCard', 'influencer_detail_id']);

        // $formData['current_price'] = $formData['current_price'];
        // $formData['discount_price'] =  isset($formData['support'])?1:0;
        $InfluencerRequest = InfluencerRequestDetail::where('compaign_id', $formData['compaign_id']);

        $InfluencerRequest->update(['total_amount', $total_amount]);

        return back()->with('success', 'Request updated successfully.');
    }

    public function removeInfluencerRequest($influencer_id, $id)
    {

        InfluencerRequestDetail::where('id', $id)->update(['status' => 0]);
        return true;
    }
    //influencer campaign

    public function acceptRequest($id)
    {
        $request = InfluencerRequestAccept::create(
            [
                'user_id' => Auth::id(),
                'influencer_request_detail_id' => $id,
                'status' => 1
            ]
        );

        $influencerDetail = InfluencerRequestDetail::where('id', $id)->first();

        $customer = User::whereId($influencerDetail->user_id)->first();


        dispatch(new NewRequestAcceptInfluencer($customer, $request, $influencerDetail));
        $customer->notify(new RequestAcceptInfluencer($customer, $request, $influencerDetail));

        $influencerData =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->select('influencer_request_details.*', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request')
            ->where('influencer_details.user_id', Auth::id())
            ->where('influencer_request_details.status', NULL);

        $influencerData = $influencerData->get();

        return response()->json(['status' => 'success', 'influencerData' => $influencerData]);
    }
    
    public function pauseCampaign(Request $request)
    {
        $row = InfluencerRequestDetail::where('id', $request->rowId)->first();
        $row->is_paused = 1;
        $row->save();
        $campaign                                       = new PausedCampaign();
        $campaign->influencer_request_detail_id         = $row->id;
        $campaign->description                          = $request->description;
        $campaign->social_media_link                    = $request->social_media_link;
        $campaign->phone_no                             = $request->phon_no;
        $campaign->status                               = "In Progress";
        $campaign->save();

        $support = ContactSupport::create([
            'user_id' => Auth::id(),
            'influencer_request_detail_id' => $request->rowId,
            'comment' => $request->description,
            'file' => $request->social_media_link,
        ]);

        dispatch(new NewSupportContactUser(Auth::user(), $row->user, $support));
        $row->user->notify(new SupportContactUser(Auth::user(), $row->user, $support));
        return redirect()->back()->with('success', 'Campaign has been paused successfully!');
    }

    public function finishCampaign(Request $request)
    {
        // Validate input data
        $this->validate($request, [
            'campaign_id' => 'required',
        ], [
            'campaign_id.required' => 'Campaign ID is required.',
        ]);

        try {
            DB::beginTransaction();

            // Find the influencer request detail by campaign ID
            $influencerDetail = InfluencerRequestDetail::where('compaign_id', $request->campaign_id)->first();

            if (!$influencerDetail) {
                return redirect()->back()->with('error', 'Campaign not found.');
            }

            // Check if campaign is already finished
            if ($influencerDetail->finish == '1') {
                return redirect()->back()->with('error', 'Campaign is already finished.');
            }

            // Get detailed influencer request information with joins
            $influencerRequestDetail = InfluencerRequestDetail::leftjoin(
                'influencer_request_accepts',
                'influencer_request_accepts.influencer_request_detail_id',
                '=',
                'influencer_request_details.id'
            )
            ->select(
                'influencer_request_details.*',
                'influencer_request_accepts.request',
                'influencer_request_accepts.request_time',
                'influencer_request_accepts.request_time_accept'
            )
            ->where('influencer_request_details.id', $influencerDetail->id)
            ->first();

            if (!$influencerRequestDetail) {
                DB::rollBack();
                return redirect()->back()->with('error', 'Campaign details not found.');
            }

            // Validate that the campaign has the necessary data to be finished
            if (!$influencerRequestDetail->influencerdetails) {
                DB::rollBack();
                return redirect()->back()->with('error', 'Influencer details not found for this campaign.');
            }

            // Update campaign status in both tables
            Campaign::where('campaign_id', $request->campaign_id)->update(['has_finished' => true]);
            InfluencerRequestDetail::where('compaign_id', $request->campaign_id)->update(['finish' => '1']);

            // Clean up orphaned social posts
            $this->cleanupOrphanedSocialPosts($influencerRequestDetail);

            DB::commit();

            return redirect('campaign-history')->with('success', 'Campaign finished successfully');

        } catch (\Exception $e) {
            DB::rollBack();

            // Log the error for debugging
            \Log::error('Error finishing campaign: ' . $e->getMessage(), [
                'campaign_id' => $request->campaign_id,
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()->with('error', 'An error occurred while finishing the campaign. Please try again.');
        }
    }

    /**
     * Clean up orphaned social posts that are not linked to any campaign
     *
     * @param \App\Models\InfluencerRequestDetail $influencerRequestDetail
     * @return void
     */
    private function cleanupOrphanedSocialPosts($influencerRequestDetail)
    {
        if (!$influencerRequestDetail->influencerdetails || !$influencerRequestDetail->media) {
            return;
        }

        $posts = SocialPost::where('user_id', $influencerRequestDetail->influencerdetails->user_id)
            ->where('media', $influencerRequestDetail->media)
            ->get();

        foreach ($posts as $post) {
            $linkedInfluencer = InfluencerRequestDetail::where('social_post_id', $post->id)->first();

            // Delete post if it's not linked to any campaign
            if (!$linkedInfluencer) {
                $post->delete();
            }
        }
    }

    public function requestReview($id)
    {
        $influencerRequestDetail = InfluencerRequestDetail::where('id', $id)->first();
        // if paused lets make it active again
        if ($influencerRequestDetail->is_paused) {
            $influencerRequestDetail->is_paused = 0;
            $influencerRequestDetail->save();
        }

        $socialConnects = SocialConnect::where('user_id', $influencerRequestDetail->influencerdetails->user_id)->get();
        foreach ($socialConnects as $socialConnect) {
            if (
                $socialConnect->token != null &&
                $socialConnect->media == $influencerRequestDetail->media
            ) {
                if ($socialConnect->token_secret != null) {
                    if ($socialConnect->media == 'instagram') {
                        $postImporter = new InsightsForTypePost($socialConnect, $influencerRequestDetail);
                        $postImporter->importSocialMediaPost();

                        $storyImporter = new InsightsForTypeStory($socialConnect, $influencerRequestDetail);
                        $storyImporter->importSocialMediaStory();
                    }
                }
            }
        }

        $recordSocial = SocialPost::where('id', $influencerRequestDetail->social_post_id)->where('media', $influencerRequestDetail->media)->first();
        
        // For backward compatibility we assign $influencerRequestDetail to $row because the view page is expecting a variable named $row
        // TODO refactor and remove the generic variable name like $row
        $row = $influencerRequestDetail;
        return \Response::json(\View::make('front-user.pages.reuqest-review', compact('recordSocial', 'row'))->render());
    }

    public function readCampaign($id)
    {
        $row = InfluencerRequestDetail::where('compaign_id', $id)->update(['read_status' => '', 'read_at' => date('Y-m-d')]);

        return response()->json(['status' => 'success']);
    }

    public function requestMoreTime(Request $request)
    {

        $formData = request()->except(['_token']);

        if ($formData['request_time'] != 0) {
            $influencer_request_accept = InfluencerRequestAccept::where('id', $formData['influencer_request_accept_id'])->first();

            $influencer_request_accept->update(['request_time' => $formData['request_time'], 'request_time_accept' => NULL]);


            InfluencerRequestTime::create(
                [
                    'user_id' => Auth::id(),
                    'influencer_request_accept_id' => $formData['influencer_request_accept_id'],
                    'request_time' => $formData['request_time']
                ]
            );


            $influencerDetail = InfluencerRequestDetail::where('id', $influencer_request_accept->influencer_request_detail_id)->first();

            $influencerDetail->update(['read_status' => 'time_request', 'read_at' => NULL]);

            $customer = User::whereId($influencerDetail->user_id)->first();


            $customer->notify(new RequestMoreTimeInfluencer($customer, $influencer_request_accept, $influencerDetail));

            return back()->with('success', 'More time Request send successfully.');
        } else {
            return back()->with('error', 'Please select time to send request');
        }
    }



    public function timeRequestAccept(Request $request)
    {

        $formData = request()->except(['_token']);

        $influencer_request_accept = InfluencerRequestAccept::where('id', $formData['influencer_request_accept_id'])->first();

        $InfluencerRequestDetail = InfluencerRequestDetail::where('id', $influencer_request_accept->influencer_request_detail_id)->first();

        $customer = User::whereId($InfluencerRequestDetail->user_id)->first();

        $influencer = User::whereId($influencer_request_accept->user_id)->first();

        $influencer_request_time = InfluencerRequestTime::where('influencer_request_accept_id', $formData['influencer_request_accept_id'])->latest()->first();

        if (isset($formData['reject'])) {
            $influencer_request_time->update(['status' => 0]);

            $status = 'rejected';
            $influencer_request_accept->update(['request_time_accept' => 0]);
            $influencer->notify(new AcceptRequestMoreTimeInfluencer($customer, $influencer_request_accept, $InfluencerRequestDetail, $status));
        }
        if (isset($formData['confirm'])) {
            $influencer_request_time->update(['status' => 1]);
            $status = 'accepted';
            $influencer_request_accept->update(['request_time_accept' => 1]);
            $influencer->notify(new AcceptRequestMoreTimeInfluencer($customer, $influencer_request_accept, $InfluencerRequestDetail, $status));
        }

        return back()->with('success', 'More time Request ' . $status . ' successfully.');
    }

    public function setupPayment()
    {
        $account = MollieAccount::where('user_id', Auth::id())->first();
        $stripeAccount = StripeAccount::where('user_id', Auth::id())->first();

        // $ch = curl_init();
        // curl_setopt($ch, CURLOPT_URL,"https://api-m.sandbox.paypal.com/v1/oauth2/token");
        // curl_setopt_array($ch, array(
        //     CURLOPT_HTTPHEADER => array(
        //         "Accept: application/json",
        //         "Accept-Language: en_US"
        //     ),
        //     CURLOPT_SSL_VERIFYPEER => false,
        // ));
        // curl_setopt($ch, CURLOPT_USERPWD, env('PAYPAL_SANDBOX_CLIENT_ID') . ":" . env('PAYPAL_SANDBOX_CLIENT_SECRET'));
        // curl_setopt($ch, CURLOPT_POST, 1);
        // curl_setopt($ch, CURLOPT_POSTFIELDS,
        //          http_build_query([
        //             'grant_type' => 'client_credentials'
        //         ]));
        // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // $response = curl_exec($ch);
        // curl_close ($ch);
        // $content = json_decode($response);
        // print_r($content->access_token);


        // $ch = curl_init();
        // curl_setopt($ch, CURLOPT_URL,"https://api-m.sandbox.paypal.com/v2/customer/partner-referrals");
        // curl_setopt_array($ch, array(
        //     CURLOPT_HTTPHEADER => array(
        //         "Content-Type: application/json",
        //         "Authorization: Bearer ". $content->access_token
        //     ),
        //     CURLOPT_SSL_VERIFYPEER => false,
        // ));
        // curl_setopt($ch, CURLOPT_POST, 1);
        // // In real life you should use something like:
        // curl_setopt($ch, CURLOPT_POSTFIELDS,
        //              '{"operations": [
        //               {
        //                 "operation": "API_INTEGRATION",
        //                 "api_integration_preference": {
        //                   "rest_api_integration": {
        //                     "integration_method": "PAYPAL",
        //                     "integration_type": "THIRD_PARTY",
        //                     "third_party_details": {
        //                       "features": [
        //                         "PAYMENT",
        //                         "REFUND"
        //                      ]
        //                     }
        //                   }
        //                 }
        //               }
        //             ],
        //             "products": [
        //               "EXPRESS_CHECKOUT"
        //             ],
        //             "legal_consents": [
        //               {
        //                 "type": "SHARE_DATA_CONSENT",
        //                 "granted": true
        //               }
        //             ]}'
        //          );
        // // Receive server response ...
        // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // $response = curl_exec($ch);
        // curl_close ($ch);
        // $content = json_decode($response);
        // print_r($content);


        return view('front-user.pages.payment', compact('account', 'stripeAccount'));
    }

    /**
     * Display the request history for the authenticated influencer.
     *
     * This method retrieves the history of influencer requests associated with the authenticated user.
     * It ensures that only influencers can access this data. If the user is a customer, a 404 error page is returned.
     * The method fetches data from multiple tables using joins, including influencer details, users, and social posts.
     * It also retrieves associated tasks for each request and passes the data to the 'request-history' view.
     *
     * @return \Illuminate\View\View|\Illuminate\Http\Response
     */
    public function requestHistory()
    {
        // Check if the authenticated user is a brand. If so, return a 404 error page.
        if (Auth::user()->user_type == 'customer') {
            return response()->view('errors.' . '404', [], 404);
        }

        $history = InfluencerRequestDetail::join(
            'influencer_details',
            'influencer_request_details.influencer_detail_id',
            '=',
            'influencer_details.id'
        )
        ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
        ->leftjoin('social_posts',  'social_posts.id', '=', 'influencer_request_details.social_post_id')
        ->select(
            'influencer_request_details.*',
            'social_posts.like',
            'social_posts.comment',
            'social_posts.view',
            'social_posts.share',
            'social_posts.friend',
            'social_posts.favorite'
        )
        ->where('influencer_details.user_id', Auth::id())
        ->orderBy('influencer_request_details.id', 'desc')
        ->get();

        foreach ($history as $data1) {
            $data1->tasks  = RequestTask::where('influencer_request_detail_id', $data1->compaign_id)->get();
        }

        return view('front-user.pages.request-history', compact('history'));
    }


    public function disconnectStripeAccount(Request $request)
    {

        $stripeAccount = StripeAccount::where('user_id', Auth::id())->first();
        $stripe = new \Stripe\StripeClient(
            config('settings.env.STRIPE_SECRET')
        );
        $stripe->accounts->delete(
            $stripeAccount->stripe_user_id
        );

        $stripeAccount->delete();

        return  redirect('payment')->with('success', 'Your stripe account has been disconnected successfully.');
    }
    public function connectStripeAccount(Request $request)
    {
        $tokendata = [
            'client_secret' => env('STRIPE_SECRET'),
            'code' => $request->code,
            'grant_type' => 'authorization_code',
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://connect.stripe.com/oauth/token',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30000,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($tokendata),
            CURLOPT_HTTPHEADER => array(
                "accept: */*",
                "accept-language: en-US,en;q=0.8",
                "content-type: application/json",
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);



        if ($err) {
            echo "cURL Error #:" . $err;
        } else {
            if ($userAccountDtail = json_decode($response)) {
                if (isset($userAccountDtail->error)) {
                    return  redirect('/influencer-onboarding')->with('error', 'Something went wrong. Please try again.');
                }

                $userPayment_setup = StripeAccount::create([
                    'user_id' => Auth::user()->id,
                    'stripe_user_id' => $userAccountDtail->stripe_user_id,
                    'stripe_publishable_key' => $userAccountDtail->stripe_publishable_key
                ]);

                if ($userPayment_setup) {
                    return  redirect('/influencer-onboarding')->with('success', 'Your stripe account has been connected successfully.');
                }
            }
        }
    }

    /**
     * This method is called when the brand reviews and accepts the influencer submission
     * (and also includeing the starred reviews).
     */
    public function submitReview(Request $request)
    {
        $invoice = RatingReview::create(
            [
                'influencer_request_accept_id' => $request->influencer_request_accept_id,
                'user_id' => Auth::id(),
                'rating' => $request->rating,
                'review' => $request->review,
            ]
        );

        $influencer_request_accept = InfluencerRequestAccept::where('id', $request->influencer_request_accept_id)->first();

        $influencer_request = InfluencerRequestDetail::where('id', $influencer_request_accept->influencer_request_detail_id)->first();

        $influencer_request->update(['review' => '1',
            'completed_at' => now(),
            'payment_status' => InfluencerRequestDetail::STATUS_PENDING
        ]);

        $influencer = User::whereId($influencer_request_accept->user_id)->first();
        $user = User::whereId(Auth::id())->first();

        $influencer_request = InfluencerRequestDetail::where('id', $influencer_request_accept->influencer_request_detail_id)->first();

        $results = AdminGamification::where('select_type', 'Point-Rules')->first();
        // <!-- points_five_star -->
        if (isset($request->rating) && $request->rating == '5.0'   && $influencer_request->refund_reason == null) {
            // $points =$points + $results->points_five_star ;
            Statistic::create([
                'user_id' => $influencer_request->influencerdetails->user_id,
                'points' => $results->points_five_star,
                'type' => '1',
                'title' =>  '[' . $influencer_request->compaign_id . ']</br>' . $results->points_five_star . ' points gained for 5 star rating',
                'date' => date('Y-m-d H:i:s'),
            ]);
        }
        // <!-- points_four_star -->
        if (isset($request->rating) && $request->rating == '4.0'   && $influencer_request->refund_reason == null) {
            // $points =$points + $results->points_four_star ;
            Statistic::create([
                'user_id' => $influencer_request->influencerdetails->user_id,
                'points' =>  $results->points_four_star,
                'type' => '1',
                'title' => '[' . $influencer_request->compaign_id . ']</br>' . $results->points_four_star . ' points gained for 4 star rating',
                'date' => date('Y-m-d H:i:s'),
            ]);
        }
        // <!-- points_one_star -->
        if (isset($request->rating) && $request->rating == '1.0') {
            // $points =$points - $results->points_one_star ;
            Statistic::create([
                'user_id' => $influencer_request->influencerdetails->user_id,
                'points' => $results->points_one_star,
                'type' => '0',
                'title' => '[' . $influencer_request->compaign_id . ']</br>' . $results->points_one_star . ' points loses for 1 star rating',
                'date' => date('Y-m-d H:i:s'),
            ]);
        }
        // <!-- points_two_star -->
        if (isset($request->rating) && $request->rating == '2.0') {
            // $points =$points - $results->points_two_star ;
            Statistic::create([
                'user_id' => $influencer_request->influencerdetails->user_id,
                'points' => $results->points_two_star,
                'type' => '0',
                'title'  =>  '[' . $influencer_request->compaign_id . ']</br>' . $results->points_two_star . ' points loses for 2 star rating',
                'date' => date('Y-m-d H:i:s'),
            ]);
        }

        dispatch(new NewcampaignSuccessfullyCompleted($influencer, $user, $influencer_request));

        dispatch(new NewRatingSubmit($influencer, $user, $influencer_request));
        $influencer->notify(new RatingSubmit($influencer, $user, $influencer_request));
        return redirect('active-campaigns/' . $invoice->id);
    }

    public function submitComplaint(Request $request)
    {
        $this->validate($request, [
            'comment' => 'required',
        ], [
            "comment.required" => "Please enter your problem !",
        ]);

        $file = null;

        if ($request->hasFile('file')) {
            // $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg'];
            $file = $request->file('file');

            $extension = $file->getClientOriginalExtension();

            $file = $file->store('file');
        }

        $invoice = Complaint::create(
            [
                'influencer_request_accept_id' => $request->influencer_request_accept_id,
                'user_id' => Auth::id(),
                'status' => 'Inprogress',
                'comment' => $request->comment,
                'file' => $file
            ]
        );

        $influencer_request_accept = InfluencerRequestAccept::where('id', $request->influencer_request_accept_id)->first();
        $influencer = User::whereId($influencer_request_accept->user_id)->first();
        $user = User::whereId(Auth::id())->first();

        $InfluencerRequestDetail = InfluencerRequestDetail::where('id', $influencer_request_accept->influencer_request_detail_id)->first();

        $InfluencerRequestDetail->is_complained = true;
        $InfluencerRequestDetail->save();


        dispatch(new NewweReceivedYourComplaint($influencer, $user, $InfluencerRequestDetail, $invoice));



        dispatch(new NewComplaintInfluencer($influencer, $user, $InfluencerRequestDetail, $invoice));
        $influencer->notify(new ComplaintInfluencer($influencer, $user, $InfluencerRequestDetail, $invoice));

        $admin = User::where('user_type', 'admin')->first();
        dispatch(new NewComplaintAdmin($admin, $influencer, $user, $InfluencerRequestDetail, $invoice));

        $admin->notify(new ComplaintAdmin($influencer, $user, $InfluencerRequestDetail, $invoice));

        if ($request->page == 'order') {
            $url = url('campaign-history/1');
        } else {
            $url = url('/active-campaigns/0/1');
        }

        return redirect($url)->with('success', 'Complaint submitted successfully.');
    }

    public function orderHistory($complaint = false)
    {
        // If the current user is an influencer
        if (Auth::user()->user_type == 'influencer') {
            $influencerRequestDetailsHistory = InfluencerRequestDetail::join('influencer_details', 'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
                ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
                ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
                ->leftjoin('social_posts', 'social_posts.id', '=', 'influencer_request_details.social_post_id')
                ->select('influencer_request_details.*',  'social_posts.like', 'social_posts.comment', 'social_posts.view', 'social_posts.share', 'social_posts.friend', 'social_posts.favorite')
                ->where('influencer_details.user_id', Auth::id())
                ->groupBy('influencer_request_details.compaign_id')
                ->orderBy('influencer_request_details.id', 'desc')
                ->get();

            foreach ($influencerRequestDetailsHistory as &$influencerRequestDetail) {
                $influencerRequestDetail->tasks  = RequestTask::where('influencer_request_detail_id', $influencerRequestDetail->compaign_id)->get();

                foreach ($influencerRequestDetail->tasks as $taskRow) {
                    $media = $influencerRequestDetail->media;
                    $type = $influencerRequestDetail->post_type;
                    $select_type = $influencerRequestDetail->advertising;
                    $post_type_content = $ptc = $influencerRequestDetail->post_content_type;

                    $influencerRequestDetail->tasks->taskDetails  = CampaignHelpers::getCampaignTasks($media, $type, $select_type, $post_type_content);
                }
            }

            $campaignRequestTime = CampaignRequestTime::first();

            // TODO still using history and review just for backward compatibility
            // influencerRequestDetailsHistory and complaint has replaced them respectively
            $history = $influencerRequestDetailsHistory;
            $review = $complaint;

            return view('front-user.pages.influencer.campaign-history', compact('influencerRequestDetailsHistory', 'complaint', 'history', 'campaignRequestTime', 'review'));
        }

        // If the current user is a brand
        $history =  InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->leftjoin('social_posts',  'social_posts.id', '=', 'influencer_request_details.social_post_id')
            ->select('influencer_request_details.*', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request', 'influencer_request_accepts.id as influencer_request_accept_id', DB::raw('COUNT(influencer_request_accepts.id) AS total_count'), DB::raw('SUM(influencer_request_accepts.request) AS accept_request'), DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count'), DB::raw('SUM(social_posts.view) AS views'))
            ->where('influencer_request_details.user_id', Auth::id())
            ->groupBy('influencer_request_details.compaign_id')
            ->orderBy('influencer_request_details.id', 'desc')->get();

        foreach ($history as $row) {
            $row->tasks  = RequestTask::where('influencer_request_detail_id', $row->compaign_id)->get();
        }

        return view('front-user.pages.brand.campaign-history', compact('history', 'complaint'));
    }


    public function reviewsCustomer()
    {

        $history = RatingReview::leftjoin('influencer_request_accepts',  'influencer_request_accepts.id', '=', 'rating_reviews.influencer_request_accept_id')
            ->leftjoin('influencer_request_details',  'influencer_request_details.id', '=', 'influencer_request_accepts.influencer_request_detail_id')
            ->select('rating_reviews.*')
            ->where('rating_reviews.user_id', Auth::id())
            ->orderBy('rating_reviews.id', 'desc')
            ->get();

        return view('front-user.pages.reviews-customer', compact('history'));
    }
    public function reviewsCustomerUpdate(Request $request)
    {

        RatingReview::find($request->review_id)->update([
            'rating' => $request->rating,
            'review' => $request->review,
        ]);

        return back()->with('success', 'Rating review updated successfully.');
    }

    public function reviewsInfluencer($review = false)
    {
        $history = RatingReview::leftjoin('influencer_request_accepts',  'influencer_request_accepts.id', '=', 'rating_reviews.influencer_request_accept_id')
            ->leftjoin('influencer_request_details',  'influencer_request_details.id', '=', 'influencer_request_accepts.influencer_request_detail_id')
            ->leftjoin('influencer_details',  'influencer_details.id', '=', 'influencer_request_details.influencer_detail_id')
            ->select('rating_reviews.*')
            ->where('influencer_details.user_id', Auth::id())
            ->orderBy('rating_reviews.id', 'desc')
            ->get();

        return view('front-user.pages.reviews-influencer', compact('history', 'review'));
    }

    public function cashoutInfluencer()
    {
        if (Auth::user()->user_type == 'customer') {
            return response()->view('errors.' . '404', [], 404);
        }

        $inprogressAmount = InfluencerRequestDetail::whereHas('influencerdetails', function ($query) {
                $query->where('user_id', Auth::id());
            })
            ->where('payment_status', InfluencerRequestDetail::STATUS_NEW)
            ->whereNotNull('invoice_id')
            ->whereNull('refund_reason')
            ->whereNull('finish')
            ->where(function ($query) {
                $query->whereNull('review')
                    ->orWhere('review', 0);
            })->whereHas('invoices', function ($query) {
                $query->where('payment_status', 'paid');
                $query->where('is_refunded', 0);
            })->sum('influencer_amount');

        $pendingAmount = InfluencerRequestDetail::whereHas('influencerdetails', function ($query) {
            $query->where('user_id', Auth::id());
        })->whereHas('invoices', function ($query) {
            $query->where('payment_status', 'paid');
            $query->where('is_refunded', 0);
        })
            ->where('payment_status', InfluencerRequestDetail::STATUS_PENDING)
            ->whereNotNull('invoice_id')
            ->whereNull('refund_reason')
            ->whereNotNull('completed_at')
            ->where('is_cashed_out', 0)
            ->whereNull('cashed_out_at')
            ->where('review', 1)
            ->orderBy('completed_at', 'desc')
            ->sum('influencer_amount');


        $availableAmount = InfluencerRequestDetail::whereHas('influencerdetails', function ($query) {
            $query->where('user_id', Auth::id());
        })->whereHas('invoices', function ($query) {
            $query->where('payment_status', 'paid');
            $query->where('is_refunded', 0);
        })
            ->where('payment_status', InfluencerRequestDetail::STATUS_COMPLETED)
            ->whereNotNull('invoice_id')
            ->whereNull('refund_reason')
            ->whereNotNull('completed_at')
            ->where('is_cashed_out', 0)
            ->whereNull('cashed_out_at')
            ->where('review', 1)
            ->orderBy('completed_at', 'desc')
            ->sum('cash_out_amount');

        $availableAmount = $availableAmount ?? 0; // Convert null to 0 if necessary

        // Round down to 2 decimal places
        $availableAmount = floor($availableAmount * 100) / 100;

        $stripeAccount = StripeAccount::where('user_id', Auth::id())->first();

        return view('front-user.pages.payment-transaction', compact('inprogressAmount', 'pendingAmount', 'availableAmount', 'stripeAccount'));
    }

    /**
     * TODO Possible deprecation?
     * Can't find any usage anywhere
     */
    public function getPaidPayment(Request $request)
    {
        // try{
        $stripe_account = StripeAccount::where('user_id', Auth::id())->first();

        if ($stripe_account != null) {
            \Stripe\Stripe::setApiKey(config('settings.env.STRIPE_SECRET'));

            $transfer = \Stripe\Transfer::create([
                'amount' => intval($request->payment * 100),
                'currency' => 'EUR',
                'destination' => $stripe_account->stripe_user_id,
                'description' => 'Payment given to influencer for campaign  at ' . env('APP_NAME'),
            ]);

            \Log::info($transfer);

            if ($transfer) {
                TransferInfluencer::create(
                    [
                        'influencer_id' => $stripe_account->user_id,
                        'card_token' => $transfer->id,
                        'payment_status' => 'Completed',
                        'paid_amount' => $request->payment,
                        'description' => 'Transfer'
                    ]
                );
                $user = User::find($stripe_account->user_id);

                dispatch(new NewpaidToInfluencer($user, $request->payment));
                $user->notify(new paidToInfluencer($user, $request->payment));
            };
            return response()->json(['code' => '200', 'msg' => 'Payment send to connected account successfully!!']);
        } else {
            \Log::info('not connected');
            return response()->json(['code' => '400', 'msg' => 'Something went wrong !']);
        }
        // } catch (\Exception $e) {
        //     \Log::info($e);
        //     return response()->json(['code' => '400', 'msg' => 'Something went wrong !']);
        // }
    }

    public function reviewDispute(Request $request)
    {
        $this->validate($request, [
            'comment' => 'required',
        ], [
            "comment.required" => "Please enter your problem !",
        ]);

        try {
            $review = RatingReview::whereId($request->review_id)->first();

            if ($review) {

                $file = null;

                if ($request->hasFile('file')) {
                    // $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg'];
                    $file = $request->file('file');

                    $extension = $file->getClientOriginalExtension();

                    $file = $file->store('file');
                }

                $support = ContactSupport::create([
                    'user_id' => Auth::id(),
                    'review_id' => $request->review_id,
                    'comment' => $request->comment,
                    'file' => $file,
                ]);

                $review->is_dispute = 1;
                $review->save();

                $admin = User::whereId(Auth::id())->first();


                dispatch(new NewDisputeContactAdmin($admin, Auth::user(), $review->user, $support));
                $admin->notify(new DisputeContactAdmin(Auth::user(), $review->user, $support));


                // dispatch(new NewDisputeContactUser(Auth::user(), $review->user, $support));
                // $review->user->notify(new DisputeContactUser(Auth::user(), $review->user, $support));

                if ($request->page == 'history') {
                    $url = url('campaign-history/1');
                } else {
                    $url = url('campaign-history/1');
                }

                return redirect($url);
            } else {
                return redirect()->back()->with('error', 'Something went wrong.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Something went wrong.');
        }
    }

    /**
     * Marks a campaign's review phase as closed and notifies the customer if applicable.
     *
     * This method is typically called when a campaign's review phase should be marked as completed.
     * It checks if the campaign has a social post, is not paused, has not already been reviewed,
     * and does not have an in-progress complaint. If all conditions are met, it sets the review flag,
     * saves the campaign, and dispatches a notification job to the customer (ensuring the customer
     * is only notified once per session).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reviewClosed(Request $request)
    {
        // Get the list of users already notified in this session
        $email_sent_to = session()->has('email_sent_to') ? session()->get('email_sent_to') : [];

        // Fetch the campaign and its related influencer request accept ID
        $campaign = InfluencerRequestDetail::where('influencer_request_details.id', $request->rowId)
            ->leftjoin('influencer_request_accepts', 'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->select('influencer_request_details.*', 'influencer_request_accepts.id as influencer_request_accept_id')
            ->first();

        // Check if there is an in-progress complaint for this campaign
        $isComplained = Complaint::where('influencer_request_accept_id', $campaign->influencer_request_accept_id);

        // Only proceed if the campaign has a social post, is not paused, not already reviewed,
        // and there is no in-progress complaint
        if (
            $campaign->social_post_id != null &&
            $campaign->review == null &&
            $campaign->is_paused != 1 &&
            !(
                $isComplained->exists() &&
                $isComplained->first()->status == "Inprogress"
            )
        ) {
            // Mark the campaign as reviewed
            $campaign->review = 1;
            $campaign->save();

            // Notify the customer if not already notified in this session
            if (!in_array($campaign->user_id, $email_sent_to)) {
                $customer = User::where('id', $campaign->user_id)->first();
                dispatch(new NewReviewPhaseEnded($customer, $campaign));
                $email_sent_to[] = $campaign->user_id;
                session()->put('email_sent_to', $email_sent_to);
            }
        }

        // Return a JSON response indicating success
        return response()->json([
            'status' => 200,
            'message' => "Review phase has been updated sucessfully"
        ]);
    }

    public function contactSupport(Request $request)
    {
        $this->validate($request, [
            'comment' => 'required',
        ], [
            "comment.required" => "Please enter your problem !",
        ]);

        try {
            $row = InfluencerRequestDetail::whereId($request->influencer_request_detail_id)->first();

            if ($row) {

                $file = null;

                if ($request->hasFile('file')) {
                    // $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg'];
                    $file = $request->file('file');

                    $extension = $file->getClientOriginalExtension();

                    $file = $file->store('file');
                }

                $support = ContactSupport::create([
                    'user_id' => Auth::id(),
                    'influencer_request_detail_id' => $request->influencer_request_detail_id,
                    'comment' => $request->comment,
                    'file' => $file,
                ]);

                $admin = User::whereId(Auth::id())->first();


                dispatch(new NewSupportContactAdmin($admin, Auth::user(), $row->user, $support));
                $admin->notify(new SupportContactAdmin(Auth::user(), $row->user, $support));

                dispatch(new NewSupportContactUser(Auth::user(), $row->user, $support));
                $row->user->notify(new SupportContactUser(Auth::user(), $row->user, $support));
                $row->is_paused = 1;
                $row->save();
                return redirect('/active-campaigns/1');
            } else {
                return redirect()->back()->with('error', 'Something went wrong.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Something went wrong.');
        }
    }

    public function paymentDetail($id)
    {
        $influencerData = InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
            ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
            ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
            ->select('influencer_request_details.*', 'influencer_details.id as i_id', 'influencer_details.user_id as i_user_id', 'influencer_request_accepts.request', 'influencer_request_accepts.id as influencer_request_accept_id', DB::raw('COUNT(influencer_request_accepts.id) AS total_count'), DB::raw('SUM(influencer_request_accepts.request) AS accept_request'), DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count'))
            ->where('influencer_request_details.user_id', Auth::id())
            ->where('influencer_request_details.status', NULL)
            ->where('influencer_request_details.compaign_id', $id)
            ->groupBy('influencer_request_details.compaign_id')
            ->orderBy('influencer_request_details.id', 'desc');

        $row = $influencerData->first();
        // $row = InfluencerRequestDetail::whereCompaignId($id)->first();
        $AdminComission = AdminComission::first();
        $campaignRequestTime = CampaignRequestTime::first();


        $card = SavedCard::whereUserId(Auth::id())
            ->select(
                \DB::raw("card_number as payment_detail, 'card' as payment_type, card_number as last4"),
                'card_number', 'type', 'id', 'name_on_card', 'expiry_month', 'expiry_year', 'payment_method'
            )
            ->orderBy('id', 'desc')
            ->first();
        
        if (empty($card)) {
            return \Response::json(\View::make('front-user.modals.payment.payment-popup', compact('row', 'AdminComission', 'campaignRequestTime'))->render());
        }

        return \Response::json(\View::make('front-user.modals.payment.payment-popup', compact('row', 'AdminComission', 'campaignRequestTime', 'card'))->render());
    }




    public function statisticsOld()
    {
        if (Auth::user()->user_type == 'customer') {
            return response()->view('errors.' . '404', [], 404);
        } else {
            $requests =   InfluencerRequestDetail::join('influencer_details',  'influencer_request_details.influencer_detail_id', '=', 'influencer_details.id')
                ->leftjoin('users',  'users.id', '=', 'influencer_details.user_id')
                ->leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
                ->leftjoin('social_posts',  'social_posts.id', '=', 'influencer_request_details.social_post_id')
                ->leftjoin('rating_reviews',  'rating_reviews.influencer_request_accept_id', '=', 'influencer_request_accepts.id')
                ->leftjoin('complaints',  'complaints.influencer_request_accept_id', '=', 'influencer_request_accepts.id')
                ->select('influencer_request_details.*',  'social_posts.like', 'social_posts.comment', 'social_posts.view', 'social_posts.share', 'social_posts.friend', 'social_posts.favorite', 'influencer_request_accepts.request', 'rating_reviews.rating', 'complaints.status as complaints_status')
                ->where('influencer_details.user_id', Auth::id())
                ->groupBy('influencer_request_details.compaign_id')
                ->orderBy('influencer_request_details.id', 'desc')
                ->get();
            $results = AdminGamification::where('select_type', 'Point-Rules')->first();

            Statistic::where('user_id', Auth::id())->delete();
            $user = User::find(Auth::id());
            $points = 0;
            foreach ($requests as $request) {
                // <!-- points_five_star -->
                if (isset($request->rating) && $request->rating == '5.0'   && $request->refund_reason == null) {
                    // $points =$points + $results->points_five_star ;
                    Statistic::create([
                        'user_id' => Auth::id(),
                        'points' => $results->points_five_star,
                        'type' => '1',
                        'title' =>  '[' . $request->compaign_id . ']</br>' . $results->points_five_star . ' points gained for 5 star rating',
                        'date' => date('Y-m-d', strtotime($request->influencer_request_accepts->rating_reviews->created_at)),
                    ]);
                }

                // <!-- points_four_star -->
                if (isset($request->rating) && $request->rating == '4.0'   && $request->refund_reason == null) {
                    // $points =$points + $results->points_four_star ;
                    Statistic::create([
                        'user_id' => Auth::id(),
                        'points' =>  $results->points_four_star,
                        'type' => '1',
                        'title' => '[' . $request->compaign_id . ']</br>' . $results->points_four_star . ' points gained for 4 star rating',
                        'date' => date('Y-m-d', strtotime($request->influencer_request_accepts->rating_reviews->created_at)),
                    ]);
                }
                // <!-- points_completed_time  points_half_time  -->

                $created_at = strtotime($request->created_at);
                $updated_at = strtotime($request->updated_at);
                $datediff = $updated_at - $created_at;
                $days =  round($datediff / (60 * 60 * 24));
                if ($days < ($request->time) / 2  && $request->social_post_id != null && $request->refund_reason == null) {
                    // $points =$points + $results->points_completed_time ;
                    Statistic::create([
                        'user_id' => Auth::id(),
                        'points' => $results->points_completed_time,
                        'type' => '1',
                        'title' =>  '[' . $request->compaign_id . ']</br>' . $results->points_completed_time . ' points gained for submitting on time',
                        'date' => date('Y-m-d', strtotime($request->updated_at)),
                    ]);
                } elseif ($days <  $request->time  && $request->social_post_id != null   && $request->refund_reason == null) {
                    // $points =$points + $results->points_half_time ;
                    Statistic::create([
                        'user_id' => Auth::id(),
                        'points' => $results->points_half_time,
                        'type' => '1',
                        'title' =>  '[' . $request->compaign_id . ']</br>' . $results->points_half_time . '  points gained for submitting half the time',
                        'date' => date('Y-m-d', strtotime($request->updated_at)),
                    ]);
                }
                // <!-- quick_response -->

                $created_date = $request->created_at;
                $influencer_request_accepts = InfluencerRequestAccept::where('influencer_request_detail_id', $request->id)->where('user_id', Auth::id())->first();
                if ($influencer_request_accepts != '') {
                    $created_at = strtotime($request->created_at);
                    $updated_at = strtotime($influencer_request_accepts->created_at);
                    $datediff = $updated_at - $created_at;
                    $days =  round($datediff / (60 * 60 * 24));

                    if (isset($influencer_request_accepts) && $days <= 1     && $request->refund_reason == null) {
                        // $points =$points + $results->quick_response ;
                        Statistic::create([
                            'user_id' => Auth::id(),
                            'points' => $results->quick_response,
                            'type' => '1',
                            'title' =>    '[' . $request->compaign_id . ']</br>' . $results->quick_response . ' points gained for responding to a request very fast',
                            'date' => date('Y-m-d', strtotime($influencer_request_accepts->created_at)),
                        ]);
                    }



                    // <!-- points_deadlines -->
                    if (isset($influencer_request_accepts) && $days > 2   && $request->refund_reason == null) {
                        // $points =$points - $results->points_deadlines ;
                        Statistic::create([
                            'user_id' => Auth::id(),
                            'points' => $results->points_deadlines,
                            'type' => '0',
                            'title' => '[' . $request->compaign_id . ']</br>' . $results->points_deadlines . ' points lost for not submitting on time',
                            'date' =>  date('Y-m-d', strtotime($influencer_request_accepts->created_at)),
                        ]);
                    }
                }
                // <!-- points_one_star -->
                if (isset($request->rating) && $request->rating == '1.0') {
                    // $points =$points - $results->points_one_star ;

                    Statistic::create([
                        'user_id' => Auth::id(),
                        'points' => $results->points_one_star,
                        'type' => '0',
                        'title' => '[' . $request->compaign_id . ']</br>' . $results->points_one_star . ' points loses for 1 star rating',
                        'date' => date('Y-m-d', strtotime($request->influencer_request_accepts->rating_reviews->created_at)),
                    ]);
                }
                // <!-- points_two_star -->
                if (isset($request->rating) && $request->rating == '2.0') {
                    // $points =$points - $results->points_two_star ;

                    Statistic::create([
                        'user_id' => Auth::id(),
                        'points' => $results->points_two_star,
                        'type' => '0',
                        'title'  =>  '[' . $request->compaign_id . ']</br>' . $results->points_two_star . ' points loses for 2 star rating',
                        'date' => date('Y-m-d', strtotime($request->influencer_request_accepts->rating_reviews->created_at)),
                    ]);
                }


                // <!-- points_disputes -->
                if (isset($request->complaints_status) && $request->complaints_status == 'Confirmed') {
                    // $points =$points - $results->points_disputes ;

                    Statistic::create([
                        'user_id' => Auth::id(),
                        'points' => $results->points_disputes,
                        'type' => '0',
                        'title'   => '[' . $request->compaign_id . ']</br>' . $results->points_disputes . ' points lost because ' . $request->user->first_name . ' disputed the campaign successfully',
                        'date' => date('Y-m-d', strtotime($request->influencer_request_accepts->complaints->created_at)),
                    ]);
                }
            }
            if (isset($requests[0])) {
                $influencer_request_details = InfluencerRequestDetail::where('influencer_detail_id', $requests[0]->influencer_detail_id)->where('user_id', $requests[0]->user_id)->where('social_post_id', '!=', null)->count();


                $influencer_request_details_row = InfluencerRequestDetail::where('influencer_detail_id', $requests[0]->influencer_detail_id)->where('user_id', $requests[0]->user_id)->where('social_post_id', '!=', null)->first();


                if (isset($influencer_request_details) && $influencer_request_details > 0) {
                    // $points =$points + $results->repeat_bookings ;
                    Statistic::create([
                        'user_id' => Auth::id(),
                        'points' => $results->repeat_bookings,
                        'type' => '1',
                        'title' =>  $results->repeat_bookings . ' points gained for repeated booking from ' . $request->user->first_name . '',
                        'date' =>  date('Y-m-d', strtotime($influencer_request_details_row->created_at)),
                    ]);
                }
                // <!-- daily_login -->
                // $daily_count = UserDailyLogin::where('user_id',Auth::id())->where(DB::raw("(STR_TO_DATE(created_at,'%Y-%m-%d'))"), date('Y-m-d'))->count();
                // $user = User::where('id', Auth::id())->first();
                // $created_at = strtotime($user->created_at);
                // $updated_at = strtotime(date('Y-m-d'));
                // $datediff = $updated_at - $created_at;
                // $days =  (round($datediff / (60 * 60 * 24))+1);

                // if( intval($days) == $daily_count ) {
                //     $points =$points + $results->daily_login ;
                //          Statistic::create([
                //             'user_id' => Auth::id(),
                //             'type' => '1',
                //             'title' => $results->daily_login.' Score gained for every daily login',
                //             'date' => date('Y-m-d'),
                //         ]);
                // }

                // <!-- successfull_campaign -->

                // $influencer_request_details = InfluencerRequestDetail::where('influencer_detail_id',$requests[0]->influencer_detail_id)->where('user_id',$requests[0]->user_id)->where('finish',1)->count();

                // if(isset($influencer_request_details) && ($influencer_request_details%3) == 0 ) {
                //     $points =$points + $results->successfull_campaign ;
                //          Statistic::create([
                //             'user_id' => Auth::id(),
                //             'type' => '1',
                //             'title' => $results->successfull_campaign.' Score gained for every 3 successfull campaign',
                //             'date' => date('Y-m-d'),
                //         ]);
                // }

            }
            $trophy = '';

            $statistics_data = Statistic::where('user_id', Auth::id())->orderBy('date', 'asc')->get();
            $points = 0;
            foreach ($statistics_data as $stat) {
                if ($stat->type == 1) {
                    $points = $points + $stat->points;
                }
                if ($stat->type == 0) {
                    $points = $points - $stat->points;
                }
                if ($points < 0) {
                    $points = 0;
                }
            }

            $statistics = Statistic::where('user_id', Auth::id())->orderBy('date', 'desc')->get();
            $pricing = AdminGamification::where('select_type', 'Pricing & Rank')->get();
            foreach ($pricing as $price) {
                $trophy = 'Bronze';
                if ($points > ($price->requirement - 1)) {
                    $trophy = $price->type;
                }
                $user->update(['trophy' => $trophy]);
            }


            $user = User::find(Auth::id());




            return view('front-user.pages.statistics', compact('statistics', 'requests', 'results', 'user', 'pricing', 'points'));
        }
    }

    public function statistics()
    {
        if (Auth::user()->user_type == 'customer') {
            return response()->view('errors.' . '404', [], 404);
        } else {

            $trophy = '';
            $user = User::find(Auth::id());

            $statistics_data = Statistic::where('user_id', Auth::id())->orderBy('date', 'asc')->get();
            $points = 0;
            foreach ($statistics_data as $stat) {
                if ($stat->type == 1) {
                    $points = $points + $stat->points;
                }
                if ($stat->type == 0) {
                    $points = $points - $stat->points;
                }
                if ($points < 0) {
                    $points = 0;
                }
            }

            $statistics = Statistic::where('user_id', Auth::id())->orderBy('date', 'desc')->get();
            $pricing = AdminGamification::where('select_type', 'Pricing & Rank')->get();
            foreach ($pricing as $price) {
                $trophy = 'Bronze';
                if ($points > ($price->requirement - 1)) {
                    $trophy = $price->type;
                }
                $user->update(['trophy' => $trophy]);
            }

            return view('front-user.pages.statistics', compact('statistics', 'user', 'pricing', 'points'));
        }
    }

    public function getInfluencerTasks(Request $request)
    {
        $media = $request->media;

        // possible values: Boost me, Reaction video, Survey
        $type = $request->type;

        // possible values: Story, Story - Picture, Story - Video, Reel, Story - Picture, Story - Video
        $select_type = $request->select_type;

        // possible values: content, photo, video
        $post_type_content = $request->ptc;

        $tasks = CampaignHelpers::getCampaignTasks($media, $type, $select_type, $post_type_content);

        if (isset($request->influencer_request_id)) {
            $influencer_request_details = InfluencerRequestDetail::where('id', $request->influencer_request_id)->first();
        } else {
            $influencer_request_details = '';
        }

        return \Response::json(\View::make('front-user.pages.influencer-tasks', compact('tasks', 'influencer_request_details'))->render());
    }
}
