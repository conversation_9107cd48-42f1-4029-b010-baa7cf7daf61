<?php

namespace App\Console;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Log;
use Illuminate\Console\Command;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected $commands = [
        Commands\SocialConnectUpdate::class,
        Commands\SocialPostUpdate::class,
        Commands\CancelledTimeoutRequest::class,
    ];
    protected function schedule(Schedule $schedule)
    {
        // Built-in laravel queue worker command, processes queeud jobs, like background tasks
        $schedule->command('queue:work --stop-when-empty')->everyMinute()->withoutOverlapping();

        // Custom command to perform custom tasks on regular basis
        $schedule->command('connect:social')->daily();
        // TODO disabled for influencer submission testing
        // $schedule->command('update:social-posts')->cron('0 */4 * * *');
        $schedule->command('clear:cache')->everyMinute();
        $schedule->command('cancelled:request')->hourly();
        $schedule->command('cancelledinfluencer:submit')->hourly();
        $schedule->command('cancelledcustomer:request')->hourly();
        $schedule->command('timeoutcustomer:payment')->hourly();
        $schedule->command('paymentrefund:expire')->daily();
        $schedule->command('review:influencer')->hourly();
        // $schedule->command('trophy:calc')->daily();
        $schedule->command('reminder:submit')->daily();

        // Charge the platform commission from influencers
        $schedule->command('commission:charge')->hourly();
        // $schedule->command('register:user')->everyMinute();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
