<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RequestTask extends Model
{
    protected $fillable = ['user_id', 'influencer_request_detail_id', 'task_id', 'value', 'type'];

    // Accessor for campaign_id
    public function getCampaignIdAttribute()
    {
        return $this->influencer_request_detail_id;
    }

    // Mutator for campaign_id
    public function setCampaignIdAttribute($value)
    {
        $this->attributes['influencer_request_detail_id'] = $value;
    }

    public function taskDetail()
    {
        return $this->hasOne(Task::class,'id','task_id');
    }
}
