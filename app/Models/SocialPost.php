<?php

namespace App\Models;

use App\Helpers\Instagram\InsightsForTypePost;
use App\Helpers\Instagram\InsightsForTypeStory;
use Illuminate\Database\Eloquent\Model;

class SocialPost extends Model
{
    protected $fillable = [
        'influencer_request_accept_id',
        'user_id',
        'post_id',
        'media',
        'text',
        'link',
        'type',
        'thumbnail',
        'published_at',
        'insights'
    ];

    /**
     * Get the validation rules for the model.
     *
     * @return array
     */
    public static function rules($id = null)
    {
        return [
            'user_id' => 'required|string',
            'post_id' => 'required|string',
            'media' => 'required|string',
            // Composite unique validation for user_id + post_id + media - exclude current record if updating
            'post_id' => $id ?
                "unique:social_posts,post_id,{$id},id,user_id," . request('user_id') . ",media," . request('media') :
                'unique:social_posts,post_id,NULL,id,user_id,' . request('user_id') . ',media,' . request('media'),
        ];
    }

    protected $attributes = [
        'like' => 0,
        'comment' => 0,
        'view' => 0,
        'share' => 0,
        'friend' => 0,
        'favorite' => 0,
    ];

    protected $casts = [
        'insights' => 'array',
        'published_at' => 'datetime',
        'like' => 'integer',
        'comment' => 'integer',
        'view' => 'integer',
        'share' => 'integer',
        'friend' => 'integer',
        'favorite' => 'integer',
    ];

    public function influencer_request_accepts()
    {
        return $this->hasOne(InfluencerRequestAccept::class,'id', 'influencer_request_accept_id');
    }

    /**
     * TODO needs to be refactored.
     * There is some confusion in the naming and getting metrics name by advertising.
     * It would make more sense by campaign or content type?!?!?!
     */
    public function formatInsights($advertising = 'Story') {
        $formattedData = [];

        if ($advertising == 'Story') {
            // Use InsightsForTypeStory::METRICS values: ['views', 'reach', 'shares', 'total_interactions']
            $metrics = InsightsForTypeStory::METRICS;
        } else {
            // Use InsightsForTypePost::METRICS values: ['likes', 'comments', 'views', 'reach']
            $metrics = InsightsForTypePost::METRICS;
        }

        foreach ($metrics as $metric) {
            $formattedData[$metric] = $this->insights[$metric] ?? 0;
        }

        return (object) $formattedData;
    }

    /**
     * TODO needs to be refactored.
     * There is some confusion in the naming and getting metrics name by advertising.
     * It would make more sense by campaign or content type?!?!?!
     */
    public function getMetricsNameByCampaignType($campaignType) {
        if ($campaignType == Campaign::TYPE_BOOST_ME) {
            return InsightsForTypeStory::METRICS;
        } elseif ($campaignType == Campaign::TYPE_SURVEY) {
            return InsightsForTypeStory::METRICS;
        } elseif ($campaignType == Campaign::TYPE_REACTION_VIDEO) {
            return InsightsForTypePost::METRICS;
        }

        return [];
    }

    public function social_post_url() {
        return $this->thumbnail;
    }

    public function thumbnail() {
        if (stripos($this->link, 'http') === 0) {
            return $this->link;
        }

        return asset('storage/' . $this->link);
    }

    /**
     * Get the full URL for the social post link
     */
    public function getLinkUrlAttribute()
    {
        if (empty($this->link)) {
            return null;
        }

        // If it's already a full URL, return as is
        if (stripos($this->link, 'http') === 0) {
            return $this->link;
        }

        // Generate the proper storage URL
        return asset('storage/' . $this->link);
    }

    /**
     * Get likes count - prioritize insights data over direct attribute
     *
     * @return int
     */
    public function getLikeAttribute()
    {
        return $this->getInsightValueOrFallback('likes', 'like');
    }

    /**
     * Get comments count - prioritize insights data over direct attribute
     *
     * @return int
     */
    public function getCommentAttribute()
    {
        return $this->getInsightValueOrFallback('comments', 'comment');
    }

    /**
     * Get views count - prioritize insights data over direct attribute
     *
     * @return int
     */
    public function getViewAttribute()
    {
        return $this->getInsightValueOrFallback('views', 'view');
    }

    /**
     * Get shares count - prioritize insights data over direct attribute
     *
     * @return int
     */
    public function getShareAttribute()
    {
        return $this->getInsightValueOrFallback('shares', 'share');
    }

    /**
     * Get reach count - prioritize insights data over direct attribute
     *
     * @return int
     */
    public function getReachAttribute()
    {
        return $this->getInsightValueOrFallback('reach', 'reach');
    }

    /**
     * Get friend count - fallback to direct attribute (no insights equivalent)
     *
     * @return int
     */
    public function getFriendAttribute()
    {
        return $this->attributes['friend'] ?? 0;
    }

    /**
     * Get favorite count - fallback to direct attribute (no insights equivalent)
     *
     * @return int
     */
    public function getFavoriteAttribute()
    {
        return $this->attributes['favorite'] ?? 0;
    }

    /**
     * Helper method to get value from insights JSON or fallback to direct attribute
     *
     * @param string $insightKey The key in the insights JSON
     * @param string $attributeKey The direct attribute key
     * @return int
     */
    private function getInsightValueOrFallback($insightKey, $attributeKey)
    {
        // First, try to get from insights JSON
        if (!empty($this->insights) && is_array($this->insights)) {
            // Check if the insight key exists and has a value > 0
            if (isset($this->insights[$insightKey]) && $this->insights[$insightKey] > 0) {
                return (int) $this->insights[$insightKey];
            }
        }

        // Fallback to direct attribute
        return (int) ($this->attributes[$attributeKey] ?? 0);
    }

    public static function createFakeInstagramPost($influencerId)
    {
        return;
        $data = [
            [
                // "influencer_request_accept_id" => InfluencerRequestAccept::where('user_id', $influencerId)->latest('id')->value('id'),
                "influencer_request_accept_id" => "story",
                "user_id" => $influencerId,
                "post_id" => "18042587006415999",
                "media" => "instagram",
                "text" => "Fake instagram reel",
                "link" => "social_pics/18042587006415999_instagram.mp4",
                "type" => "video",
                "thumbnail" => "https://www.instagram.com/reel/DHuPqOVoEaz/",
                "created_at" => date('Y-m-d H:i:s', strtotime('-3 hours')),
                "updated_at" => date('Y-m-d H:i:s', strtotime('-3 hours')),
                "published_at" => date('Y-m-d H:i:s', strtotime('-2 hours')),
                "like" => random_int(5, 5000),
                "comment" => random_int(5, 5000),
                "view" => random_int(5, 5000),
                "share" => random_int(5, 5000),
                "friend" => null,
                "favorite" => null
            ],
            [
                "influencer_request_accept_id" => "story",
                "user_id" => $influencerId,
                "post_id" => "18142224715389498",
                "media" => "instagram",
                "text" => "Fake instagram video",
                "link" => "social_pics/18142224715389498_instagram.mp4",
                "type" => "video",
                "thumbnail" => "https://instagram.com/stories/motktest/3625275827780017182",
                "created_at" => date('Y-m-d H:i:s', strtotime('-4 hours')),
                "updated_at" => date('Y-m-d H:i:s', strtotime('-4 hours')),
                "published_at" => date('Y-m-d H:i:s', strtotime('-2 hours')),
                "like" => random_int(5, 5000),
                "comment" => random_int(5, 5000),
                "view" => random_int(5, 5000),
                "share" => random_int(5, 5000),
                "friend" => null,
                "favorite" => null
            ]
        ];

        foreach ($data as $item) {
            self::create($item);
        }
    }
}
