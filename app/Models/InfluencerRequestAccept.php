<?php

namespace App\Models;

use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * InfluencerRequestAccept Model
 *
 * This model serves as a bridge/junction table that tracks influencer responses to campaign requests.
 * It's a critical component in ClickItFame's campaign management system that manages the acceptance/rejection
 * workflow between brands and influencers.
 *
 * @purpose Campaign Response Tracking
 * - Tracks whether an influencer accepted or rejected a campaign request
 * - Links influencers to specific campaign requests
 * - Manages the status of influencer participation
 * - Handles time extension requests and approvals
 * - Serves as central hub for campaign-influencer relationships
 *
 * @database_fields
 * - user_id: Influencer's user ID who received the request
 * - influencer_request_detail_id: Campaign request ID being responded to
 * - status: Initial participation status (1=active, 0=inactive)
 * - request: Final acceptance decision (1=accepted, 0=rejected, null=pending)
 * - request_time: Time extension duration requested (in minutes, nullable)
 * - request_time_accept: Time extension approval status (1=approved, 0=rejected, null=pending)
 *
 * @business_flow
 * 1. Campaign Request Phase: Record created when influencer receives campaign request
 * 2. Accept/Reject Decision: 'request' field updated with influencer's decision
 * 3. Time Extension Workflow: Manage deadline extension requests and approvals
 * 4. Post-Campaign: Links to ratings, reviews, complaints, and social posts
 *
 * @relationships
 * - belongsTo: User (influencer), InfluencerRequestDetail (campaign request)
 * - hasOne: RatingReview (post-campaign feedback), Complaint (dispute management)
 * - connected_to: SocialPost (via influencer_request_accept_id)
 *
 * @usage_patterns
 * - Campaign Management: Track influencer participation and responses
 * - Business Logic: Determine completion status and payment eligibility
 * - Notifications: Trigger email updates for status changes
 * - Time Management: Handle deadline extensions and approvals
 *
 * @example_workflow
 * Brand creates campaign → InfluencerRequestDetail
 *                    ↓
 * Influencer receives request → InfluencerRequestAccept (status=1)
 *                    ↓
 * Influencer decides → request=1 (accepted) or request=0 (rejected)
 *                    ↓
 * If accepted → Creates content → SocialPost
 *                    ↓
 * Brand reviews → RatingReview → Payment processed
 *
 * @key_features
 * - Time Extension Management: Built-in accessors for extension status tracking
 * - Status Tracking: Multiple status fields for different workflow stages
 * - Notification Integration: Uses Notifiable trait for email communications
 * - Relationship Hub: Central connection point for campaign-related models
 * 
 * Field Meanings
 * --------------
 * Field	Purpose	Values
 * user_id	Influencer who received the request	User ID
 * influencer_request_detail_id	Campaign request being responded to	Campaign ID
 * status	Initial participation status	1 = Active, 0 = Inactive
 * request	Final acceptance decision	1 = Accepted, 0 = Rejected
 * request_time	Extension duration requested	Minutes (nullable)
 * request_time_accept	Extension approval status	1 = Approved, 0 = Rejected
 *
 * @see InfluencerRequestDetail For campaign request details
 * @see SocialPost For influencer deliverables
 * @see RatingReview For post-campaign feedback
 * @see Complaint For dispute management
 *
 * @package App\Models
 * <AUTHOR> Development Team
 * @since Campaign System v1.0
 */
class InfluencerRequestAccept extends Model
{
    use Notifiable;
    
    protected $fillable = ['user_id', 'influencer_request_detail_id', 'status', 'request_time', 'request_time_accept', 'request'];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'is_time_extension_requested',
        'is_time_extension_approved',
        'is_time_extension_rejected',
        'requested_extension_duration'
    ];

    /**
     * Determine if a time extension has been requested.
     */
    protected function isTimeExtensionRequested(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->request_time !== null && $this->request_time > 0,
        );
    }

    /**
     * Determine if a time extension has been approved.
     */
    protected function isTimeExtensionApproved(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->request_time_accept === '1' || $this->request_time_accept === 1,
        );
    }

    /**
     * Determine if a time extension has been rejected.
     */
    protected function isTimeExtensionRejected(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->request_time_accept === '0' || $this->request_time_accept === 0,
        );
    }

    /**
     * Get the requested extension duration in minutes.
     */
    protected function requestedExtensionDuration(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->request_time ?? 0,
        );
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
 
    public function influencer_request_details()
    {
        return $this->hasOne(InfluencerRequestDetail::class, 'id', 'influencer_request_detail_id');
    }

    public function rating_reviews()
    {
        return $this->hasOne(RatingReview::class, 'influencer_request_accept_id', 'id');
    }
    
    public function complaints()
    {
        return $this->hasOne(Complaint::class, 'influencer_request_accept_id', 'id');
    }
}
