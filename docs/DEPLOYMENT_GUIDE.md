# ClickItFame Deployment Guide

## Overview

This document provides a comprehensive guide for deploying ClickItFame application changes, including all infrastructural modifications and post-deployment tasks. The changes are organized chronologically to ensure proper deployment order.

## Table of Contents

1. [Pre-Deployment Checklist](#pre-deployment-checklist)
2. [Deployment Steps](#deployment-steps)
3. [Post-Deployment Tasks](#post-deployment-tasks)
4. [Verification Steps](#verification-steps)
5. [Rollback Procedures](#rollback-procedures)
6. [Environment-Specific Configurations](#environment-specific-configurations)

## Pre-Deployment Checklist

### Critical Backup Steps
- [ ] **Database Backup**: Create full database backup
- [ ] **File System Backup**: Backup storage directories
- [ ] **Configuration Backup**: Backup `.env` and config files
- [ ] **Code Backup**: Ensure code is committed and tagged

```bash
# Database backup
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# Storage backup
tar -czf storage_backup_$(date +%Y%m%d_%H%M%S).tar.gz storage/

# Configuration backup
cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
```

### Environment Preparation
- [ ] **Staging Testing**: Verify all changes work in staging
- [ ] **Maintenance Mode**: Plan maintenance window if needed
- [ ] **Dependencies**: Check for any new package requirements
- [ ] **Server Resources**: Ensure adequate disk space and memory

## Deployment Steps

### Step 1: Code Deployment
```bash
# Pull latest code
git pull origin main

# Install/update dependencies
composer install --no-dev --optimize-autoloader
npm install --production
npm run production
```

### Step 2: Database Migrations
```bash
# Apply database migrations
php artisan migrate --force
```

### Step 3: Configuration Updates
```bash
# Clear all caches
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## Post-Deployment Tasks

### Phase 1: Storage Path Fixes (Priority: High)

#### 1.1 Fix Storage Paths and File Migration
```bash
# Run storage path fix command
php artisan storage:fix-paths
```

**What this does:**
- Creates `social_pics` and `files` directories in public storage
- Migrates files from old locations (`public/storage/app/`) to new locations (`storage/app/public/`)
- Preserves directory structure and handles subdirectories
- Provides detailed feedback on migration progress
- Asks for confirmation before removing old directories

**Expected Output:**
```
Starting storage path fix...
Created social_pics directory in public storage.
Created files directory in public storage.
Found old social_pics directory at: /path/to/public/storage/app/social_pics
Moved 150 files from social_pics to new location.
Found old files directory at: /path/to/public/storage/app/files
Moved 75 files from files to new location.
Do you want to remove the old social_pics directory? (yes/no)
Do you want to remove the old files directory? (yes/no)
Storage link verified.
Storage path fix completed successfully!
```

#### 1.2 Update Social Media URLs
```bash
# Update social media URLs in database
php artisan social:update-urls
```

**Purpose:**
- Updates all social media picture URLs in the database
- Converts old URL formats to new Laravel storage URLs
- Handles batch processing for large datasets

#### 1.3 Create Storage Link
```bash
# Create symbolic link for public access
php artisan storage:link
```

**Purpose:**
- Creates symbolic link from `public/storage` to `storage/app/public`
- Enables public access to stored files
- Required for proper file serving

### Phase 2: Timezone Configuration (Priority: Medium)

#### 2.1 Environment Variables Setup
Add to `.env` file:
```env
# Timezone Configuration
APP_TIMEZONE=UTC
DB_TIMEZONE=+00:00
```

#### 2.2 Clear Configuration Cache
```bash
# Clear config cache to load new timezone settings
php artisan config:clear
php artisan config:cache
```

#### 2.3 Verify Timezone Configuration
```bash
# Test timezone configuration
php artisan tinker --execute="
echo 'App Timezone: ' . config('app.timezone') . PHP_EOL;
echo 'Current Time: ' . now() . PHP_EOL;
echo 'Timezone Object: ' . now()->timezone . PHP_EOL;
"
```

**Expected Output:**
```
App Timezone: UTC
Current Time: 2025-01-27 15:50:23
Timezone Object: UTC
```

### Phase 3: Campaign Phase Duration Configuration (Priority: Low)

#### 3.1 Environment Variables Setup
Add to `.env` file:
```env
# Campaign Phase Durations (in minutes)
REQUEST_PHASE_DURATION=4320   # 3 days
PAYMENT_PHASE_DURATION=4320   # 3 days
SUBMIT_PHASE_DURATION=14400   # 10 days
REVIEW_PHASE_DURATION=10080   # 7 days
```

#### 3.2 Clear Configuration Cache
```bash
# Clear config cache to load new phase durations
php artisan config:clear
php artisan config:cache
```

### Phase 4: Package Installations and Updates

#### 4.1 Laravel Impersonate Package
```bash
# Install impersonation package
composer require lab404/laravel-impersonate

# Publish configuration
php artisan vendor:publish --tag=impersonate

# Clear caches
php artisan config:clear
php artisan route:clear
```

#### 4.2 Log Viewer Package
```bash
# Install log viewer
composer require rap2hpoutre/laravel-log-viewer

# No additional configuration needed
```

#### 4.3 Sentry Error Tracking
```bash
# Install Sentry
composer require sentry/sentry-laravel

# Publish configuration
php artisan vendor:publish --provider="Sentry\Laravel\ServiceProvider"
```

Add to `.env`:
```env
SENTRY_LARAVEL_DSN=your_sentry_dsn_here
```

## Verification Steps

### 1. Storage Path Verification
```bash
# Check storage structure
ls -la storage/app/public/social_pics/
ls -la storage/app/public/files/

# Verify symbolic link
ls -la public/storage

# Test file access
curl -I https://yourdomain.com/storage/social_pics/test-file.jpg
```

### 2. Timezone Verification
```bash
# Test timezone functionality
php artisan tinker --execute="
\$user = App\Models\User::first();
if(\$user) {
    echo 'User created_at: ' . \$user->created_at . PHP_EOL;
    echo 'Timezone: ' . \$user->created_at->timezone . PHP_EOL;
    echo 'Local time: ' . \$user->created_at->setTimezone('Europe/Berlin') . PHP_EOL;
}
"
```

### 3. Campaign Phase Duration Verification
```bash
# Test phase duration configuration
php artisan tinker --execute="
\$durations = config('app.campaign_phases_duration');
foreach(\$durations as \$phase => \$duration) {
    echo \$phase . ': ' . \$duration . ' minutes (' . round(\$duration/1440, 1) . ' days)' . PHP_EOL;
}
"
```

### 4. Application Health Check
- [ ] **Homepage loads correctly**
- [ ] **User registration/login works**
- [ ] **File uploads work (profile images, social media content)**
- [ ] **Campaign creation and management functions**
- [ ] **Timer displays show correct times**
- [ ] **Admin panel accessible (if applicable)**

## Rollback Procedures

### Emergency Rollback
If critical issues occur:

```bash
# 1. Enable maintenance mode
php artisan down

# 2. Restore database
mysql -u username -p database_name < backup_YYYYMMDD_HHMMSS.sql

# 3. Restore storage files
tar -xzf storage_backup_YYYYMMDD_HHMMSS.tar.gz

# 4. Restore configuration
cp .env.backup.YYYYMMDD_HHMMSS .env

# 5. Clear caches
php artisan config:clear
php artisan cache:clear

# 6. Disable maintenance mode
php artisan up
```

### Partial Rollback Options

#### Storage Path Rollback
```bash
# Remove new storage structure
rm -rf storage/app/public/social_pics
rm -rf storage/app/public/files

# Restore from backup
tar -xzf storage_backup_YYYYMMDD_HHMMSS.tar.gz
```

#### Timezone Rollback
```env
# Revert .env to previous timezone
APP_TIMEZONE=Europe/Berlin
# Remove DB_TIMEZONE line
```

## Environment-Specific Configurations

### Development Environment
```env
# Development settings
APP_ENV=local
APP_DEBUG=true
APP_TIMEZONE=UTC
DB_TIMEZONE=+00:00

# Shorter campaign durations for testing
REQUEST_PHASE_DURATION=60     # 1 hour
PAYMENT_PHASE_DURATION=60     # 1 hour
SUBMIT_PHASE_DURATION=1440    # 1 day
REVIEW_PHASE_DURATION=720     # 12 hours
```

### Production Environment
```env
# Production settings
APP_ENV=production
APP_DEBUG=false
APP_TIMEZONE=UTC
DB_TIMEZONE=+00:00

# Standard business durations
REQUEST_PHASE_DURATION=4320   # 3 days
PAYMENT_PHASE_DURATION=4320   # 3 days
SUBMIT_PHASE_DURATION=14400   # 10 days
REVIEW_PHASE_DURATION=10080   # 7 days

# Sentry for error tracking
SENTRY_LARAVEL_DSN=your_production_sentry_dsn
```

## Monitoring and Maintenance

### Post-Deployment Monitoring
- [ ] **Application Logs**: Monitor for errors in `storage/logs/`
- [ ] **Server Resources**: Check CPU, memory, and disk usage
- [ ] **Database Performance**: Monitor query performance
- [ ] **File Storage**: Verify file uploads and access
- [ ] **User Feedback**: Monitor for user-reported issues

### Regular Maintenance Tasks
```bash
# Weekly maintenance
php artisan queue:restart
php artisan cache:clear
php artisan view:clear

# Monthly maintenance
php artisan storage:link  # Verify link still exists
php artisan config:cache  # Refresh cached config
```

## Support and Troubleshooting

### Common Issues and Solutions

| Issue | Symptom | Solution |
|-------|---------|----------|
| Files not accessible | 404 errors on file URLs | Check storage link: `php artisan storage:link` |
| Wrong timezone display | Times off by hours | Verify APP_TIMEZONE=UTC in .env |
| Campaign timers wrong | Incorrect countdown | Check phase duration configuration |
| Upload failures | File upload errors | Check storage permissions: `chmod -R 755 storage/` |
| Database errors | Connection issues | Verify DB_TIMEZONE setting |

### Emergency Contacts
- **Development Team**: [contact information]
- **System Administrator**: [contact information]
- **Database Administrator**: [contact information]

### Useful Commands
```bash
# Quick health check
php artisan tinker --execute="echo 'App Status: OK - ' . now();"

# Storage verification
ls -la storage/app/public/ && ls -la public/storage

# Configuration check
php artisan config:show app.timezone
php artisan config:show app.campaign_phases_duration
```

---

**Last Updated**: 2025-01-27  
**Version**: 1.0  
**Maintainer**: Development Team
