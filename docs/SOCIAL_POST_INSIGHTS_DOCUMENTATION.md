# SocialPost Model Insights Documentation

## Overview

The `SocialPost` model in ClickItFame handles social media content metrics through a sophisticated dual-layer system that prioritizes real-time Instagram API insights over direct database attributes. This documentation explains how the insights system works, the data structure, and how metrics are calculated.

## Table of Contents

1. [Model Structure](#model-structure)
2. [Insights Data Format](#insights-data-format)
3. [Metrics Priority System](#metrics-priority-system)
4. [Campaign Type Integration](#campaign-type-integration)
5. [API Integration](#api-integration)
6. [Usage Examples](#usage-examples)
7. [Troubleshooting](#troubleshooting)

## Model Structure

### Database Attributes

The `SocialPost` model contains both direct attributes and JSON insights data:

```php
// Direct database attributes (fallback values)
protected $attributes = [
    'like' => 0,
    'comment' => 0,
    'view' => 0,
    'share' => 0,
    'friend' => 0,
    'favorite' => 0,
];

// JSON insights from Instagram API
'insights' => 'array', // Cast to array automatically
```

### Fillable Fields

```php
protected $fillable = [
    'influencer_request_accept_id',
    'user_id',
    'post_id',
    'media',
    'text',
    'link',
    'type',
    'thumbnail',
    'published_at',
    'insights'  // JSON field containing Instagram API data
];
```

## Insights Data Format

### Raw Instagram API Response Structure

The `insights` field stores comprehensive Instagram API data in the following format:

```json
{
  "complete__2025_07_30_13_20_49": {
    "data": [
      {
        "name": "likes",
        "period": "lifetime",
        "values": [{"value": 3}],
        "title": "„Gefällt mir"-Angaben",
        "description": "So oft wurde dein Reel mit „Gefällt mir" markiert.",
        "id": "18052177574169668/insights/likes/lifetime"
      },
      {
        "name": "comments",
        "period": "lifetime", 
        "values": [{"value": 1}],
        "title": "Kommentare",
        "description": "So oft wurde dein Reel kommentiert.",
        "id": "18052177574169668/insights/comments/lifetime"
      },
      {
        "name": "views",
        "period": "lifetime",
        "values": [{"value": 22}],
        "title": "Aufrufe", 
        "description": "So oft wurde dein Reel abgespielt oder angezeigt.",
        "id": "18052177574169668/insights/views/lifetime"
      },
      {
        "name": "reach",
        "period": "lifetime",
        "values": [{"value": 5}],
        "title": "Erreichte Konten",
        "description": "So viele individuelle Konten haben dieses Reel mindestens einmal gesehen.",
        "id": "18052177574169668/insights/reach/lifetime"
      }
    ]
  },
  "likes": 3,
  "comments": 1,
  "views": 22,
  "reach": 5
}
```

### Processed Data Structure

The insights are processed and stored with both:
1. **Complete API Response**: Timestamped with `complete__YYYY_MM_DD_HH_MM_SS` key
2. **Flattened Metrics**: Direct key-value pairs for easy access

## Naming Convention Strategy

### Dual Accessor System

To maintain backward compatibility while aligning with Instagram API naming conventions, the model provides two sets of accessors:

1. **Backward Compatible (Singular)**: `like`, `comment`, `view`, `share`, `reach`
2. **Instagram API Consistent (Plural)**: `likes`, `comments`, `views`, `shares`, `total_interactions`

### Instagram API Naming Alignment

Instagram Graph API uses mostly plural names for metrics:
- ✅ `likes` (plural)
- ✅ `comments` (plural)
- ✅ `views` (plural)
- ✅ `shares` (plural)
- ✅ `total_interactions` (compound)
- ✅ `reach` (already singular in API)

### Migration Strategy

**For New Development:**
- Use Instagram API consistent names (`likes`, `comments`, `views`, etc.)
- Provides better alignment with Instagram documentation
- More intuitive for developers familiar with Instagram API

**For Legacy Code:**
- Existing code continues to work with singular names
- No breaking changes required
- Gradual migration possible

## Metrics Priority System

### Laravel Accessors with Priority Logic

The model uses Laravel accessors to implement a priority system:

**Priority Order:**
1. **Instagram API Insights** (if value > 0)
2. **Direct Database Attributes** (fallback)

```php
/**
 * Get likes count - prioritize insights data over direct attribute
 */
public function getLikeAttribute()
{
    return $this->getInsightValueOrFallback('likes', 'like');
}

/**
 * Helper method to get value from insights JSON or fallback to direct attribute
 */
private function getInsightValueOrFallback($insightKey, $attributeKey)
{
    // First, try to get from insights JSON
    if (!empty($this->insights) && is_array($this->insights)) {
        // Check if the insight key exists and has a value > 0
        if (isset($this->insights[$insightKey]) && $this->insights[$insightKey] > 0) {
            return (int) $this->insights[$insightKey];
        }
    }

    // Fallback to direct attribute
    return (int) ($this->attributes[$attributeKey] ?? 0);
}
```

### Available Metrics

#### Backward Compatible Accessors (Singular Names)

| Accessor Method | Insights Key | Fallback Attribute | Description |
|----------------|--------------|-------------------|-------------|
| `getLikeAttribute()` | `likes` | `like` | Number of likes/reactions |
| `getCommentAttribute()` | `comments` | `comment` | Number of comments |
| `getViewAttribute()` | `views` | `view` | Number of views/impressions |
| `getShareAttribute()` | `shares` | `share` | Number of shares |
| `getReachAttribute()` | `reach` | `reach` | Unique accounts reached |
| `getFriendAttribute()` | N/A | `friend` | Friend-related metric (direct only) |
| `getFavoriteAttribute()` | N/A | `favorite` | Favorite count (direct only) |

#### Instagram API Consistent Accessors (Plural Names)

| Accessor Method | Insights Key | Fallback Attribute | Description |
|----------------|--------------|-------------------|-------------|
| `getLikesAttribute()` | `likes` | `like` | Number of likes/reactions (Instagram naming) |
| `getCommentsAttribute()` | `comments` | `comment` | Number of comments (Instagram naming) |
| `getViewsAttribute()` | `views` | `view` | Number of views/impressions (Instagram naming) |
| `getSharesAttribute()` | `shares` | `share` | Number of shares (Instagram naming) |
| `getTotalInteractionsAttribute()` | `total_interactions` | `friend` | Total interactions (Instagram naming) |
| `getReachAttribute()` | `reach` | `reach` | Unique accounts reached (already singular in Instagram API) |

## Campaign Type Integration

### Campaign Types and Metrics

The system supports different campaign types with specific metrics:

```php
// Campaign type constants
const TYPE_BOOST_ME = 'Boost me';
const TYPE_REACTION_VIDEO = 'Reaction video'; 
const TYPE_SURVEY = 'Survey';
```

### Metrics by Campaign Type

| Campaign Type | Metrics Used | Helper Class |
|---------------|-------------|--------------|
| **Boost Me** | `views`, `reach`, `shares`, `total_interactions` | `InsightsForTypeStory` |
| **Survey** | `views`, `reach`, `shares`, `total_interactions` | `InsightsForTypeStory` |
| **Reaction Video** | `likes`, `comments`, `views`, `reach` | `InsightsForTypePost` |

```php
public function getMetricsNameByCampaignType($campaignType) {
    if ($campaignType == Campaign::TYPE_BOOST_ME) {
        return InsightsForTypeStory::METRICS;
    } elseif ($campaignType == Campaign::TYPE_SURVEY) {
        return InsightsForTypeStory::METRICS;
    } elseif ($campaignType == Campaign::TYPE_REACTION_VIDEO) {
        return InsightsForTypePost::METRICS;
    }

    return [];
}
```

## API Integration

### Instagram API Endpoints

The system fetches data from Instagram Graph API:

**For Posts/Reels:**
```
https://graph.facebook.com/v18.0/{user_id}/media
https://graph.facebook.com/v18.0/{post_id}/insights
```

**For Stories:**
```
https://graph.facebook.com/v18.0/{user_id}/stories
https://graph.facebook.com/v18.0/{story_id}/insights
```

### Time-Based Filtering

API calls include time restrictions based on campaign creation:

```php
$since = Carbon::parse($this->influencerRequestDetail->created_at)->timestamp;
$until = Carbon::now()->timestamp;
```

### Data Processing Flow

1. **Fetch Posts/Stories** from Instagram API
2. **Get Insights** for each post/story
3. **Process and Store** both complete API response and flattened metrics
4. **Download Media Files** to local storage
5. **Update/Create** SocialPost records using `updateOrCreate()`

## Usage Examples

### Basic Usage

```php
// Get a social post
$socialPost = SocialPost::find(1);

// Backward compatible access (singular names)
echo $socialPost->like;     // Uses insights data if available, fallback to direct attribute
echo $socialPost->comment;  // Same priority logic
echo $socialPost->view;     // Same priority logic
echo $socialPost->reach;    // Same priority logic

// Instagram API consistent access (plural names)
echo $socialPost->likes;    // Uses insights['likes'], fallback to 'like' attribute
echo $socialPost->comments; // Uses insights['comments'], fallback to 'comment' attribute
echo $socialPost->views;    // Uses insights['views'], fallback to 'view' attribute
echo $socialPost->shares;   // Uses insights['shares'], fallback to 'share' attribute
echo $socialPost->total_interactions; // Uses insights['total_interactions'], fallback to 'friend' attribute
```

### Campaign-Specific Metrics

```php
// Format insights for specific campaign type
$insights = $socialPost->formatInsights('Story'); // Uses InsightsForTypeStory::METRICS
$insights = $socialPost->formatInsights('Post');  // Uses InsightsForTypePost::METRICS

// Access formatted data
echo $insights->views;
echo $insights->reach;
echo $insights->likes;
```

### Raw Insights Access

```php
// Access complete Instagram API response
$completeData = $socialPost->insights['complete__2025_07_30_13_20_49'];

// Access individual metrics directly
$likes = $socialPost->insights['likes'];
$comments = $socialPost->insights['comments'];
$views = $socialPost->insights['views'];
$reach = $socialPost->insights['reach'];
```

### Blade Template Usage

```blade
{{-- Display metrics using backward compatible names --}}
<div class="social-metrics-legacy">
    <span class="likes">{{ $socialPost->like }} likes</span>
    <span class="comments">{{ $socialPost->comment }} comments</span>
    <span class="views">{{ $socialPost->view }} views</span>
    <span class="reach">{{ $socialPost->reach }} reach</span>
</div>

{{-- Display metrics using Instagram API consistent names --}}
<div class="social-metrics-instagram">
    <span class="likes">{{ $socialPost->likes }} likes</span>
    <span class="comments">{{ $socialPost->comments }} comments</span>
    <span class="views">{{ $socialPost->views }} views</span>
    <span class="shares">{{ $socialPost->shares }} shares</span>
    <span class="reach">{{ $socialPost->reach }} reach</span>
    <span class="interactions">{{ $socialPost->total_interactions }} interactions</span>
</div>

{{-- Check if insights data exists --}}
@if(!empty($socialPost->insights))
    <div class="insights-available">
        Real-time data from Instagram
    </div>
@else
    <div class="fallback-data">
        Using stored metrics
    </div>
@endif
```

## Data Flow Architecture

```
Instagram API → Helper Classes → SocialPost Model → Laravel Accessors → Application
     ↓              ↓                ↓                    ↓              ↓
Raw Insights → Processing → JSON Storage → Priority Logic → Final Values
```

### Helper Classes

1. **InsightsForTypePost**: Handles posts/reels metrics
2. **InsightsForTypeStory**: Handles stories metrics

Both classes:
- Fetch data from Instagram API
- Process and format insights
- Store media files locally
- Create/update SocialPost records

## Benefits of This System

1. **Real-time Accuracy**: Prioritizes fresh Instagram API data
2. **Fallback Reliability**: Always has backup data if API fails
3. **Flexible Metrics**: Different metrics for different campaign types
4. **Complete Data Preservation**: Stores full API response for future use
5. **Performance**: Cached in database, no need for repeated API calls
6. **Transparency**: Clear priority system for data sources

## Troubleshooting

### Common Issues

1. **Missing Insights Data**
   - Check Instagram API token validity
   - Verify post/story is within time range
   - Check API rate limits

2. **Zero Values in Metrics**
   - Instagram may return 0 for new posts
   - System correctly falls back to direct attributes
   - Check if post meets minimum age requirements

3. **Inconsistent Data**
   - Instagram API data updates with delay
   - Direct attributes may be manually set
   - Priority system ensures most accurate data is used

### Debugging

```php
// Check insights data structure
dd($socialPost->insights);

// Check which value is being used
$socialPost->getInsightValueOrFallback('likes', 'like');

// Verify API data freshness
$timestamp = array_keys($socialPost->insights)[0];
echo "Data fetched at: " . str_replace(['complete__', '_'], ['', ' '], $timestamp);
```

## Future Enhancements

1. **Multi-platform Support**: Extend to TikTok, YouTube, etc.
2. **Historical Tracking**: Store insights over time
3. **Advanced Analytics**: Engagement rates, growth metrics
4. **Real-time Updates**: Webhook integration for instant updates
5. **Caching Strategy**: Redis caching for frequently accessed metrics

---

**Last Updated**: 2025-01-27  
**Version**: 1.0  
**Maintainer**: Development Team
