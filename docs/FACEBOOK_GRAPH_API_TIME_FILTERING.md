# Facebook Graph API Time Filtering Guide

## 🕒 **Time-Based Filtering Options**

Facebook's Graph API supports several ways to filter Instagram posts and stories by time:

## 🎯 **Method 1: Using `since` and `until` Parameters (Recommended)**

### **Basic Implementation**
```php
// Get posts from last 24 hours
$since = Carbon::now()->subHours(24)->timestamp;
$until = Carbon::now()->timestamp;

$apiUrl = "https://graph.facebook.com/v18.0/{$instagramUserId}/media"
    . "?fields=id,caption,media_type,thumbnail_url,media_url,permalink,timestamp"
    . "&since={$since}"
    . "&until={$until}"
    . "&access_token={$accessToken}";
```

### **Flexible Time Range Examples**

```php
// Last 24 hours
$since = Carbon::now()->subHours(24)->timestamp;
$until = Carbon::now()->timestamp;

// Last 7 days
$since = Carbon::now()->subDays(7)->timestamp;
$until = Carbon::now()->timestamp;

// Last 30 days
$since = Carbon::now()->subDays(30)->timestamp;
$until = Carbon::now()->timestamp;

// Since campaign started
$since = Carbon::parse($campaignStartDate)->timestamp;
$until = Carbon::now()->timestamp;

// Custom date range
$since = Carbon::parse('2024-01-01')->timestamp;
$until = Carbon::parse('2024-01-31')->timestamp;
```

## 🎯 **Method 2: Using `limit` Parameter**

```php
// Get only the most recent 25 posts (default is 25, max is 100)
$apiUrl = "https://graph.facebook.com/v18.0/{$instagramUserId}/media"
    . "?fields=id,caption,media_type,thumbnail_url,media_url,permalink,timestamp"
    . "&limit=10"
    . "&access_token={$accessToken}";
```

## 🎯 **Method 3: Combining Multiple Parameters**

```php
// Get last 10 posts from the past 7 days
$since = Carbon::now()->subDays(7)->timestamp;
$until = Carbon::now()->timestamp;

$apiUrl = "https://graph.facebook.com/v18.0/{$instagramUserId}/media"
    . "?fields=id,caption,media_type,thumbnail_url,media_url,permalink,timestamp"
    . "&since={$since}"
    . "&until={$until}"
    . "&limit=10"
    . "&access_token={$accessToken}";
```

## 🔧 **Enhanced Helper Class Implementation**

### **Configurable Time Range Helper**

```php
class InsightsForTypePost
{
    // Add method to set custom time range
    public function importSocialMediaPostWithTimeRange($hoursBack = 24): void
    {
        $since = Carbon::now()->subHours($hoursBack)->timestamp;
        $until = Carbon::now()->timestamp;
        
        $baseApiUrl = 'https://graph.facebook.com/v18.0/' . $this->socialConnect->token_secret . '/media?&access_token=' . $this->socialConnect->token;
        $callApiUrl = $baseApiUrl . '&fields=' . implode(',', self::FIELDS) . '&since=' . $since . '&until=' . $until;
        
        // Rest of the implementation...
    }
    
    // Method for campaign-specific time range
    public function importSocialMediaPostSinceCampaign(): void
    {
        $since = Carbon::parse($this->influencerRequestDetail->created_at)->timestamp;
        $until = Carbon::now()->timestamp;
        
        $baseApiUrl = 'https://graph.facebook.com/v18.0/' . $this->socialConnect->token_secret . '/media?&access_token=' . $this->socialConnect->token;
        $callApiUrl = $baseApiUrl . '&fields=' . implode(',', self::FIELDS) . '&since=' . $since . '&until=' . $until;
        
        // Rest of the implementation...
    }
}
```

## 📊 **Performance Benefits**

### **API Efficiency**
- ✅ **Reduced Data Transfer**: Only fetch relevant posts
- ✅ **Faster Response Times**: Less data to process
- ✅ **Lower API Usage**: Fewer API calls needed
- ✅ **Better Rate Limiting**: Stay within Facebook's limits

### **Application Performance**
- ✅ **Reduced Database Operations**: Less data to store/update
- ✅ **Faster Processing**: Less data to filter in PHP
- ✅ **Lower Memory Usage**: Smaller datasets
- ✅ **Better User Experience**: Faster loading times

## ⚠️ **Important Notes**

### **Timestamp Format**
- Facebook Graph API expects **Unix timestamps** (seconds since epoch)
- Use `Carbon::timestamp` or `strtotime()` to convert dates

### **Time Zone Considerations**
```php
// Always use UTC for API calls
$since = Carbon::now('UTC')->subHours(24)->timestamp;
$until = Carbon::now('UTC')->timestamp;
```

### **API Limitations**
- **Stories**: Only available for 24 hours after posting
- **Posts**: Available indefinitely (unless deleted)
- **Rate Limits**: Facebook has API call limits per hour
- **Permissions**: Requires appropriate Instagram permissions

## 🚀 **Recommended Implementation Strategy**

### **For Cron Jobs (Hourly Updates)**
```php
// Only fetch posts from last 2 hours to avoid duplicates
$since = Carbon::now()->subHours(2)->timestamp;
$until = Carbon::now()->timestamp;
```

### **For Frontend Submissions**
```php
// Fetch posts since campaign started
$since = Carbon::parse($campaignStartDate)->timestamp;
$until = Carbon::now()->timestamp;
```

### **For Initial Data Import**
```php
// Fetch posts from last 30 days
$since = Carbon::now()->subDays(30)->timestamp;
$until = Carbon::now()->timestamp;
```

## 🔍 **Example API URLs**

### **Posts from Last 24 Hours**
```
https://graph.facebook.com/v18.0/INSTAGRAM_USER_ID/media?fields=id,caption,media_type,thumbnail_url,media_url,permalink,timestamp&since=1706745600&until=1706832000&access_token=ACCESS_TOKEN
```

### **Stories from Last 24 Hours**
```
https://graph.facebook.com/v18.0/INSTAGRAM_USER_ID/stories?fields=id,caption,media_type,thumbnail_url,media_url,permalink,timestamp&since=1706745600&until=1706832000&access_token=ACCESS_TOKEN
```

## 🧪 **Testing Time Filters**

### **Debug Logging**
```php
Log::info('Instagram API call with time filter', [
    'since_timestamp' => $since,
    'until_timestamp' => $until,
    'since_date' => Carbon::createFromTimestamp($since)->toDateTimeString(),
    'until_date' => Carbon::createFromTimestamp($until)->toDateTimeString(),
    'api_url' => $callApiUrl
]);
```

### **Validate Results**
```php
foreach ($posts as $post) {
    $postTime = Carbon::parse($post['timestamp']);
    $sinceTime = Carbon::createFromTimestamp($since);
    $untilTime = Carbon::createFromTimestamp($until);
    
    if (!$postTime->between($sinceTime, $untilTime)) {
        Log::warning('Post outside expected time range', [
            'post_time' => $postTime->toDateTimeString(),
            'expected_since' => $sinceTime->toDateTimeString(),
            'expected_until' => $untilTime->toDateTimeString()
        ]);
    }
}
```

This approach will significantly improve performance and reduce unnecessary API calls! 🎉
