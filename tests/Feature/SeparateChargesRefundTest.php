<?php

namespace Tests\Feature;

use App\Models\InfluencerRequestDetail;
use App\Models\User;
use App\Models\Invoice;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

/**
 * Test suite for the new Separate Charges Architecture
 * 
 * This test validates that individual influencer refunds work correctly
 * without affecting other influencers or platform fees.
 */
class SeparateChargesRefundTest extends TestCase
{
    /**
     * Test that individual influencer refunds don't affect other influencers
     *
     * @return void
     */
    public function testIndividualInfluencerRefundIsolation()
    {
        // Find a multi-influencer campaign for testing
        $campaignId = $this->findMultiInfluencerCampaign();
        
        if (!$campaignId) {
            $this->markTestSkipped('No multi-influencer campaign found for testing. Create a campaign with multiple influencers first.');
        }

        // Get all influencers for this campaign
        $influencers = InfluencerRequestDetail::where('compaign_id', $campaignId)
            ->whereNotNull('refund_txn_id')
            ->get();

        $this->assertGreaterThan(1, $influencers->count(), 'Need at least 2 influencers to test refund isolation');

        // Verify each influencer has a unique refund_txn_id (charge ID)
        $chargeIds = $influencers->pluck('refund_txn_id')->toArray();
        $uniqueChargeIds = array_unique($chargeIds);
        
        $this->assertEquals(
            count($chargeIds), 
            count($uniqueChargeIds), 
            'Each influencer should have a unique charge ID for separate refunds'
        );

        echo "\n✅ SEPARATE CHARGES ARCHITECTURE VALIDATION:\n";
        echo "Campaign ID: {$campaignId}\n";
        echo "Total Influencers: " . $influencers->count() . "\n";
        echo "Unique Charge IDs: " . count($uniqueChargeIds) . "\n\n";

        // Test each influencer's refund isolation
        foreach ($influencers as $index => $influencer) {
            echo "Influencer #" . ($index + 1) . " (ID: {$influencer->id}):\n";
            echo "- Charge ID: {$influencer->refund_txn_id}\n";
            echo "- Amount: " . number_format($influencer->discount_price, 2) . " EUR\n";
            
            // Verify the charge ID is unique to this influencer
            $otherInfluencersWithSameCharge = $influencers->where('refund_txn_id', $influencer->refund_txn_id)
                ->where('id', '!=', $influencer->id)
                ->count();
            
            $this->assertEquals(0, $otherInfluencersWithSameCharge, 
                "Influencer {$influencer->id} should have a unique charge ID");
            
            echo "- Charge ID Unique: ✅\n";
            
            // Check if invoice exists and has correct charge ID
            $invoice = Invoice::where('influencer_request_detail_id', $influencer->id)->first();
            if ($invoice) {
                echo "- Invoice Charge ID: {$invoice->charge_id}\n";
                $this->assertEquals($influencer->refund_txn_id, $invoice->charge_id, 
                    "Invoice should use the same individual charge ID");
                echo "- Invoice Charge ID Match: ✅\n";
            }
            
            echo "\n";
        }

        $this->assertTrue(true, 'All influencers have unique charge IDs for isolated refunds');
    }

    /**
     * Test the refund process for a single influencer
     *
     * @return void
     */
    public function testSingleInfluencerRefundProcess()
    {
        // Find an influencer with a refund_txn_id
        $influencer = InfluencerRequestDetail::whereNotNull('refund_txn_id')
            ->whereIn('payment_status', [
                InfluencerRequestDetail::STATUS_NEW,
                InfluencerRequestDetail::STATUS_ACCEPTED
            ])
            ->first();

        if (!$influencer) {
            $this->markTestSkipped('No suitable influencer found for refund testing.');
        }

        echo "\n🔄 REFUND PROCESS TEST:\n";
        echo "Influencer ID: {$influencer->id}\n";
        echo "Campaign ID: {$influencer->compaign_id}\n";
        echo "Charge ID: {$influencer->refund_txn_id}\n";
        echo "Amount: " . number_format($influencer->discount_price, 2) . " EUR\n";

        // Get other influencers in the same campaign
        $otherInfluencers = InfluencerRequestDetail::where('compaign_id', $influencer->compaign_id)
            ->where('id', '!=', $influencer->id)
            ->get();

        echo "Other Influencers in Campaign: " . $otherInfluencers->count() . "\n\n";

        // Store original states
        $originalStates = [];
        foreach ($otherInfluencers as $other) {
            $originalStates[$other->id] = [
                'payment_status' => $other->payment_status,
                'refund_txn_id' => $other->refund_txn_id
            ];
        }

        // Test the cancelCampaign method (without actually processing Stripe refund in test)
        try {
            $result = $influencer->cancelCampaign('test_refund_isolation', [
                'process_refund' => false, // Don't actually process Stripe refund in test
                'adjust_price' => false,
                'send_notifications' => false,
                'metadata' => [
                    'test_type' => 'refund_isolation_test',
                    'automated' => true
                ]
            ]);

            echo "Refund Process Result:\n";
            echo "- Success: " . ($result['success'] ? 'Yes' : 'No') . "\n";
            echo "- Message: " . $result['message'] . "\n\n";

            // Verify other influencers are unaffected
            foreach ($otherInfluencers as $other) {
                $other->refresh(); // Reload from database
                
                $this->assertEquals(
                    $originalStates[$other->id]['payment_status'], 
                    $other->payment_status,
                    "Other influencer {$other->id} payment status should be unchanged"
                );
                
                $this->assertEquals(
                    $originalStates[$other->id]['refund_txn_id'], 
                    $other->refund_txn_id,
                    "Other influencer {$other->id} charge ID should be unchanged"
                );
                
                echo "✅ Influencer {$other->id} unaffected by refund\n";
            }

        } catch (\Exception $e) {
            echo "❌ Error during refund test: " . $e->getMessage() . "\n";
            $this->fail("Refund process should not throw exceptions: " . $e->getMessage());
        }

        $this->assertTrue(true, 'Individual refund process works without affecting other influencers');
    }

    /**
     * Find a campaign with multiple influencers for testing
     *
     * @return string|null
     */
    private function findMultiInfluencerCampaign(): ?string
    {
        $campaigns = InfluencerRequestDetail::select('compaign_id')
            ->whereNotNull('refund_txn_id')
            ->groupBy('compaign_id')
            ->havingRaw('COUNT(*) > 1')
            ->pluck('compaign_id');

        return $campaigns->first();
    }

    /**
     * Test platform fee isolation from influencer refunds
     *
     * @return void
     */
    public function testPlatformFeeIsolation()
    {
        // Find a campaign with platform fee invoice
        $platformInvoice = Invoice::where('payment_type', 'Platform_Payment')
            ->whereNotNull('campaign_id')
            ->first();

        if (!$platformInvoice) {
            $this->markTestSkipped('No platform fee invoice found for testing.');
        }

        echo "\n🏛️ PLATFORM FEE ISOLATION TEST:\n";
        echo "Campaign ID: {$platformInvoice->campaign_id}\n";
        echo "Platform Fee Amount: " . number_format($platformInvoice->payment_amount, 2) . " EUR\n";
        echo "Platform Fee Charge ID: {$platformInvoice->charge_id}\n";

        // Get influencer invoices for the same campaign
        $influencerInvoices = Invoice::where('payment_type', 'Influencer_Payment')
            ->where('campaign_id', $platformInvoice->campaign_id)
            ->get();

        echo "Influencer Invoices: " . $influencerInvoices->count() . "\n\n";

        // Verify platform fee has different charge ID from all influencers
        foreach ($influencerInvoices as $invoice) {
            $this->assertNotEquals(
                $platformInvoice->charge_id,
                $invoice->charge_id,
                "Platform fee should have different charge ID from influencer {$invoice->influencer_request_detail_id}"
            );
            
            echo "✅ Platform fee isolated from influencer {$invoice->influencer_request_detail_id}\n";
        }

        // Verify platform fee is marked as non-refundable in description
        $this->assertStringContainsString(
            'Non-refundable',
            $platformInvoice->description,
            'Platform fee should be marked as non-refundable'
        );

        echo "✅ Platform fee marked as non-refundable\n";

        $this->assertTrue(true, 'Platform fee is properly isolated from influencer refunds');
    }
}
