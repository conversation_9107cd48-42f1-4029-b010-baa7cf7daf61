{{-- Cancel Request Modal Component --}}
<div class="modal fade" id="cancelRequest{{ $influencerCampaignDetail->id }}" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="cancelRequest{{ $influencerCampaignDetail->id }}Label" target="popup"
    data-bs-toggle="modal" data-bs-target="#cancelRequest{{ $influencerCampaignDetail->id }}Label"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <div class="text-center errorBox">
                    <div class="text-center chkImage">
                        <img src="{{ asset('/assets/front-end/images/icons/icon-high-demand.svg') }}" alt="">
                    </div>
                    <div class="manreq-title" style="font-size: 16px;line-height: normal;">
                        Are you sure you want to cancel the campaign?
                    </div>
                    <div class="widthBtnPopup d-flex">
                        <input class="et-submit accept mx-1 ds" type="button" value="Yes" onclick="requestCancelbutton('{{ $influencerCampaignDetail->campaign_id }}')">
                        <button class="et-submit red-btn reject mx-1 ds" type="button" data-bs-dismiss="modal" aria-label="Close">No</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    #cancelRequest{{ $influencerCampaignDetail->id }} {
        display: none !important;
    }
    
    #cancelRequest{{ $influencerCampaignDetail->id }}.show {
        display: block !important;
    }
</style>
