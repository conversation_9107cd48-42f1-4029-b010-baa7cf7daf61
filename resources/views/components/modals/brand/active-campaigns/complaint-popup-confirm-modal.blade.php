<!--modal complaintPopupConfirm form -->
<div class="modal fade wewPopup influencer requesttime"
    id="complaintPopupConfirm{{ $influencerRequestDetail->id }}" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="complaintPopupConfirmLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body complaint-popup">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"><img
                        src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}"
                        alt=""></button>
                <div class="wizardHeading">Are you sure to complaint?</div>
                <div class="wizardForm">
                    @csrf
                    <input type="hidden" name="influencer_request_accept_id"
                        id="influencer_request_accept_id{{ $influencerRequestDetail->id }}"
                        value="{{ $influencerRequestDetail->influencer_request_accepts->id }}">
                    <div class="col-xl-12 col-md-12 col-12">
                        <div class="widthBtnPopup">

                            <div class="col-xl-12 col-md-12 col-12">
                                <div class="font-size-16  text-center">
                                    The complaint is immediately forwarded to the
                                    influencer concerned, who is then given seven days
                                    to respond. After we have received and analysed
                                    their feedback, we make a final decision.
                                </div>
                            </div>

                        </div>
                    </div>
                    <!-- Selected users //-->
                </div>
                <div class="widthBtnPopup d-flex mt-4 mb-2 pt-2">
                    <a href="#"
                        class="et-submit red-btn mx-4 complant-btn popBackBtn"
                        onclick="closeComplaintModal({{ $influencerRequestDetail->id }})">Cancel</a>
                    <input class="et-submit mx-4 popProcess" type="button"
                        name="proceed"
                        data-bs-target="#complaintSubmit{{ $influencerRequestDetail->id }}"
                        data-bs-toggle="modal" data-bs-dismiss="modal"
                        value="Yes">
                </div>
            </div>
        </div>
    </div>
</div>
<!--end  modal complaintPopupConfirm form -->
