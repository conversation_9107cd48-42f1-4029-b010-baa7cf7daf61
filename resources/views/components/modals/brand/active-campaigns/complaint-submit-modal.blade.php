<!--modal complaintSubmit form -->
<div class="modal fade confirm-content influencer"
    id="complaintSubmit{{ $influencerRequestDetail->id }}" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1" aria-labelledby="requestSubmit5Label"
    aria-modal="true" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"><img
                        src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}"
                        alt=""></button>
                <div class="wizardHeading">Complaint</div>
                <div class="contact-support">
                    <div class="text-center wizardHeading-subheading">
                        Please explain why you are not satisfied with the influencer's work:
                    </div>
                    <form action="{{ url('submit-complaint') }}"
                        method="Post" enctype="multipart/form-data"
                        id="complaintForm{{ $influencerRequestDetail->id }}"
                        data-parsley-validate="" novalidate=""
                    >
                        @csrf
                        <input type="hidden" name="influencer_request_accept_id"
                            id="influencer_request_accept_id{{ $influencerRequestDetail->id }}"
                            value="{{ $influencerRequestDetail->influencer_request_accepts->id }}">
                        <input type="hidden" name="page" value="campaign">
                        <div class="form-group">
                            <label for="" class="form-label"></label>
                            <textarea class="form-control" name="comment" id="comment{{ $influencerRequestDetail->id }}" rows="3"
                                placeholder="Please describe your problem" required=""
                                data-parsley-required-message="Please enter problem." spellcheck="false" data-ms-editor="true"></textarea>
                        </div>
                        <div class="form-group uploadFile">
                            <div class="custom-file-picker">
                                <div class="picture-container form-group">
                                    <div class="picture">
                                        <span class="icon" id="icon">
                                            <div class="smaltext">Browse</div>
                                            <div class="bigtext">Or Drag and Drop to
                                                Upload</div>
                                        </span>
                                        <input type="file" class="wizard-file ds"
                                            name="file"
                                            id="complaintFile{{ $influencerRequestDetail->id }}">
                                        <svg version="1.1"
                                            xmlns="http://www.w3.org/2000/svg"
                                            xmlns:xlink="http://www.w3.org/1999/xlink"
                                            x="0px" y="0px" viewBox="0 0 37 37"
                                            xml:space="preserve">
                                            <path class="circ path"
                                                style="fill:none;stroke:#77d27b;stroke-width:3;stroke-linejoin:round;stroke-miterlimit:10;"
                                                d="M30.5,6.5L30.5,6.5c6.6,6.6,6.6,17.4,0,24l0,0c-6.6,6.6-17.4,6.6-24,0l0,0c-6.6-6.6-6.6-17.4,0-24l0,0C13.1-0.2,23.9-0.2,30.5,6.5z">
                                            </path>
                                            <polyline class="tick path"
                                                style="fill:none;stroke:#77d27b;stroke-width:3;stroke-linejoin:round;stroke-miterlimit:10;"
                                                points="11.6,20 15.9,24.2 26.4,13.8 ">
                                            </polyline>
                                        </svg>
                                        <div
                                            class="popover-container text-center show-file">
                                            <p data-toggle="popover"
                                                data-id="complaintFile{{ $influencerRequestDetail->id }}"
                                                class="btn-popover"
                                                data-original-title=""
                                                title=""
                                                data-bs-original-title="">
                                                <span class="file-total-viewer">0</span>
                                                Files Selected <br />
                                                <input
                                                    type="button" value="view"
                                                    href="javascript:void(0)"
                                                    class="btn btn-success btn-xs btn-file-view ds"
                                                >
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="extra-time-content-bs">
                            <a href="#"
                                class="et-submit red-btn mx-4 complant-btn popBackBtn"
                                data-bs-dismiss="modal"
                                aria-label="Close">Cancel</a>
                            <input type="submit" class="et-submit ds" name="confirm" value="Send complaint">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<!--end  modal complaintSubmit form -->
