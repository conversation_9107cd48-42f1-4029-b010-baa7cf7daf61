<!--modal complaintPopup form -->
<div class="modal fade influencer" id="complaintPopup{{ $influencerRequestDetail->id }}"
    data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="reviewRatingPopupLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button"
                    class="btn-close"
                    data-bs-dismiss="modal"
                    aria-label="Close">
                    <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" alt="">
                </button>
                <div class="wizardHeading">Are you sure to complaint?</div>
                <div class="bold-text text-center">
                    Are you sure, that the influencer did not finish his tasks correctly?
                </div>
                <div class="wizardForm">
                    @csrf
                    <input type="hidden"
                        name="influencer_request_accept_id"
                        id="influencer_request_accept_id"
                        value="{{ $influencerRequestDetail->influencer_request_accepts->id }}"
                    >
                    <div class="col-xl-12 col-md-12 col-12">
                        <div class="widthBtnPopup">
                            <div class="col-xl-12 col-md-12 col-12">
                                <div class="bold-text">
                                    Please confirm, if the influencer has not completed your tasks.
                                </div>
                            </div>
                            <div class="tasklists{{ $influencerRequestDetail->id }}">
                                @include('front-user.pages.influencer-tasks')
                            </div>
                        </div>
                    </div>
                    <!-- Selected users //-->
                </div>
                <div class="allBtn d-flex">
                    <a href="#" class="popBackBtn" data-bs-dismiss="modal" aria-label="Close">Back</a>
                    <input class="popProcess" type="submit" name="proceed"
                        value="Proceed" target="popup" data-bs-toggle="modal"
                        data-bs-target="#complaintPopupConfirm{{ $influencerRequestDetail->id }}">

                </div>
            </div>
        </div>
    </div>
</div>
<!--end  modal complaintPopup form -->
