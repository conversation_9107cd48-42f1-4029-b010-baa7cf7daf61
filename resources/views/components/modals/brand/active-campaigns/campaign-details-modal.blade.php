<!-- Campaign Details Modal -->
<div class="modal fade influencer wewPopup request-popup"
    id="show-campaign-details-{{ $influencerCampaignDetail->id }}" data-bs-backdrop="static"
    data-bs-keyboard="false" tabindex="-1" aria-labelledby="show-campaign-details-label"
    aria-hidden="true">
    <div class="modal-dialog default-width modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close">
                    <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" alt="">
                </button>
                <div class="popup-title">{{ $influencerCampaignDetail->compaign_title }}</div>
                <div class="popup-title-id">Campaign ID: {{ $influencerCampaignDetail->compaign_id }}</div>
                <form method="post" id="show-campaign-details-submit-{{ $influencerCampaignDetail->id }}"
                    action="{{ url('/request-form') }}" data-parsley-validate>
                    @csrf
                    <input type="hidden" name="influencer_request_detail_id"
                        id="influencer_request_detail_id"
                        value="{{ isset($influencerCampaignDetail->id) ? $influencerCampaignDetail->id : '' }}">
                    <ul class="nav nav-tabs ordertab" id="myTab{{ $influencerCampaignDetail->id }}"
                        role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active"
                                id="general-information-tab{{ $influencerCampaignDetail->id }}"
                                data-bs-toggle="tab"
                                data-bs-target="#general-information{{ $influencerCampaignDetail->id }}"
                                type="button" role="tab"
                                aria-controls="general-information{{ $influencerCampaignDetail->id }}"
                                aria-selected="true">
                                General Information
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link"
                                id="order-detail-tab{{ $influencerCampaignDetail->id }}"
                                data-bs-toggle="tab"
                                data-bs-target="#order-detail{{ $influencerCampaignDetail->id }}"
                                type="button" role="tab"
                                aria-controls="order-detail{{ $influencerCampaignDetail->id }}"
                                aria-selected="false">Influencer Tasks
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="myTabContent">
                        {{-- General Information Tab --}}
                        <div class="tab-pane fade show active"
                            id="general-information{{ $influencerCampaignDetail->id }}" role="tabpanel"
                            aria-labelledby="general-information-tab{{ $influencerCampaignDetail->id }}">
                            <div class="inside-table request-content">
                                <div class="inside-table-row">
                                    <span class="type-label"> Company Name</span>
                                    <span class="type-image">
                                        <img src="{{ asset('/assets/front-end/images/icons/icon-user-black.svg') }}" class="" alt="">
                                    </span>
                                    <span class="type-content">{{ $influencerCampaignDetail->user->first_name }} {{ $influencerCampaignDetail->user->last_name }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Request date</span>
                                    <span class="type-image">
                                        <img src="{{ asset('/assets/front-end/images/icons/icon-calender-black.svg') }}" class="" alt="">
                                    </span>
                                    <span class="type-content">{{ date('d.m.Y', strtotime($influencerCampaignDetail->created_at)) }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Total cost</span>
                                    <span class="type-image">
                                        <img src="{{ asset('/assets/front-end/images/icons/req-money.svg') }}" class="" alt="">
                                    </span>
                                    <span class="type-content">{{ number_format($influencerCampaignDetail->total_amount, 2) }} €</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Social Media</span>
                                    <span class="type-image">
                                        <img src="{{ asset('/assets/front-end/images/icons/icon-cb-' . $influencerCampaignDetail->media . '.svg') }}" class="" alt="">
                                    </span>
                                    <span class="type-content">{{ ucfirst($influencerCampaignDetail->media) }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Brand name</span>
                                    <span class="type-image">
                                        <img src="{{ asset('/assets/front-end/images/icons/icon-brandname-black.svg') }}" class="" alt="">
                                    </span>
                                    <span class="type-content">{{ $influencerCampaignDetail->name }}</span>
                                </div>
                                <div class="inside-table-row">
                                    <span class="type-label">Campaign type</span>
                                    <span class="type-image">
                                        <img src="{{ asset('/assets/front-end/images/icons/icon-boostme-black.svg') }}" class="" alt="">
                                    </span>
                                    <span class="type-content">{{ $influencerCampaignDetail->post_type }}</span>
                                </div>
                                @if (isset($influencerCampaignDetail->category))
                                    <div class="inside-table-row">
                                        <span class="type-label">Category</span>
                                        <span class="type-image"><img src="{{ asset('/assets/front-end/images/icons/icon-category-black.svg') }}" class="" alt=""></span>
                                        <span class="type-content">{{ $influencerCampaignDetail->category->name }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                        
                        {{-- Influencer Tasks Tab --}}
                        <div class="tab-pane fade" id="order-detail{{ $influencerCampaignDetail->id }}"
                            role="tabpanel"
                            aria-labelledby="order-detail-tab{{ $influencerCampaignDetail->id }}">
                            <div class="request-content-data icon-before">
                                @if (isset($influencerCampaignDetail->tasks))
                                    {{-- Social Media Name Tasks --}}
                                    @foreach ($influencerCampaignDetail->tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'SocialMediaName')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    {{ $task->value }}
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach

                                    {{-- Link Tasks --}}
                                    @foreach ($influencerCampaignDetail->tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'Link')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    <div class="order-link">
                                                        <div class="link"
                                                            id="myInput{{ $task->id }}">
                                                            {{ $task->value }}</div>
                                                        <div class="copy-link">
                                                            <a class="copy_text"
                                                                id="jjhu"
                                                                data-toggle="tooltip"
                                                                title="Copy to Clipboard"
                                                                href="{{ $task->value }}">
                                                                <span
                                                                    class="">COPY</span>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach

                                    {{-- Upload Content Tasks --}}
                                    @foreach ($influencerCampaignDetail->tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'UploadContent')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    <a class="table-btn"
                                                        href="{{ asset('storage/' . $task->value) }}"
                                                        download
                                                        style="color: black!important; width: 70%; height: 40px; box-shadow: none!important;">Download
                                                    </a>
                                                    @if ($influencerCampaignDetail->post_content_type == 'video')
                                                        <img src="{{ asset('/assets/front-end/icons/video_placeholder.png') }}" width="40">
                                                    @else
                                                        <img src="{{ asset('/assets/front-end/icons/image_placholder.png') }}" width="40">
                                                    @endif
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach

                                    {{-- Hashtag Tasks --}}
                                    @foreach ($influencerCampaignDetail->tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'Hashtag')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                                <div class="order-content">
                                                    <?php $tags = explode(',', $task->value); ?>
                                                    @foreach ($tags as $tag)
                                                        @if ($tag)
                                                            <div class="order-hash-tag">
                                                                <img src="{{ asset('/assets/front-end/images/icon-hash.png') }}"
                                                                    alt="">
                                                                {{ $tag }}
                                                            </div>
                                                        @endif
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach

                                    {{-- Info Tasks --}}
                                    @foreach ($influencerCampaignDetail->tasks as $task)
                                        @if (isset($task->taskDetail) && $task->type == 'Info')
                                            <div class="inside-table-row">
                                                <div class="order-titles">
                                                    {{ $task->taskDetail->task }}
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                @endif
                            </div>
                        </div>
                    </div>
                    <!-- Selected users //-->
                    <div class="popup2btns d-flex">
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
