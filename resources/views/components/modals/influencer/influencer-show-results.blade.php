<div class="modal fade influencer-show-results-modal"
    id="influencer-show-results-{{ $influencerRequestDetail->id }}"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
    tabindex="-1"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body pt-4">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" alt="">
                </button>
                <div class="wizardForm">
                    <div class="form-border">
                        <div class="form-group">
                            <div class="imageNvideo">
                                @php
                                    $thumbnailUrl = $influencerRequestDetail->social_posts && $influencerRequestDetail->social_posts->thumbnail()
                                        ? $influencerRequestDetail->social_posts->thumbnail()
                                        : '';
                                    $isVideo = Str::endsWith(strtolower($thumbnailUrl), '.mp4');
                                @endphp

                                @if ($thumbnailUrl)
                                    @if ($isVideo)
                                        <video controls id="content_url_video_tag">
                                            <source src="{{ $thumbnailUrl }}" type="video/mp4" id="content_url_video">
                                        </video>
                                    @else
                                        <a href="{{ $thumbnailUrl }}" target="_blank">
                                            <img src="{{ $thumbnailUrl }}" id="content_url_img">
                                        </a>
                                    @endif
                                @endif
                            </div>
                        </div>
                        <div class="ssn d-flex">
                            <div class="socialdetail">
                                <div class="socialimage">
                                    <img src="{{ asset('/assets/front-end/images/icons/campaigns-instagram.svg') }}" alt="Instagram" id="media_img">
                                </div>
                                <div class="socialcontent">
                                    <div class="socialcontent-posttype" style="max-width: 100%;">
                                        <span class="m-0">Share Content from Brand - Post/Story</span>
                                    </div>
                                    <div class="socialcontent-postlink" style="max-width: 100%;">
                                        <a target="_blank"
                                            href="{{ $influencerRequestDetail->social_posts && $influencerRequestDetail->social_posts->social_post_url() ?
                                                $influencerRequestDetail->social_posts->social_post_url() : '' }}"
                                            class="view-link"
                                            id="viewLink">
                                            {{ $influencerRequestDetail->social_posts && $influencerRequestDetail->social_posts->social_post_url() ?
                                                $influencerRequestDetail->social_posts->social_post_url() : ''
                                            }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <x-insight-stats :influencer-request-detail="$influencerRequestDetail" />
                    </div>

                    @if ($influencerRequestDetail->post_type == 'Survey')
                        <div class="form-border">
                            <div class="row">
                                <div class="col-sm-8" style="display: flex; align-items: center;">
                                    You can download your survey results here:
                                </div>
                                <div class="col-sm-4">
                                    <button
                                        class="btn btn-show-survey-results"
                                        onclick="downloadImage('{{ url('/storage/app') . '/' . $influencerRequestDetail->insight }}')"
                                        style="width: auto; padding: .375rem .75rem; height: auto; border-radius: 5px;">
                                        <i class="bi bi-download"></i> Survey Results
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    function downloadImage(imageUrl) {
        const link = document.createElement("a");
        link.href = imageUrl;
        let extension = imageUrl.split('.').pop().split(/\#|\?/)[0];
        link.download = `survey_results.${extension}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
</script>