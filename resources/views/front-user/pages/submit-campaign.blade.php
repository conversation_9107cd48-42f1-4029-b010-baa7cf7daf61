@extends('front-user.layouts.master_user_dashboard')

@section('content')
<section id="campaignForm">
    <div class="step_form_custom customerSer">
        <h1 class="dashHeading"><span>Submit Campaign</span></h1> 
        <div class="form-group"> 
            <table class="campHistory" border="0" cellpedding="0" cellspacing="0">
                <tbody>                               
                    <tr class="campningDiv">
                        <td class="soclDetail">
                            <span class="handelpletform">
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-twitter.png" alt=""> 
                            </span>
                        </td>
                        <td class="soclDetail">
                            <span class="productImage">
                                <img src="{{ asset('/') }}/assets/front-end/images/bg-image.jpg" alt=""> 
                            </span>
                        </td>
                        <td class="firDaat">
                            Text or post content will display here
                        </td>
                        <td class="custDetail">
                            Download
                        </td>
                        <td class="timer">
                            <a href="javascript:void(0)" class="cunformBtn" data-bs-toggle="modal" data-bs-target="#confirmpopup">Confirm</a>
                        </td>
                    </tr>
                    <tr class="campningDiv">
                        <td class="soclDetail">
                            <span class="handelpletform">
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-twitter.png" alt=""> 
                            </span>
                        </td>
                        <td class="soclDetail">
                            <span class="productImage">
                                <img src="{{ asset('/') }}/assets/front-end/images/bg-image.jpg" alt=""> 
                            </span>
                        </td>
                        <td class="firDaat">
                            Text or post content will display here
                        </td>
                        <td class="custDetail">
                            Download
                        </td>
                        <td class="timer">
                            <a href="javascript:void(0)" class="cunformBtn" data-bs-toggle="modal" data-bs-target="#confirmpopup">Confirm</a>
                        </td>
                    </tr>
                    <tr class="campningDiv">
                        <td class="soclDetail">
                            <span class="handelpletform">
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-twitter.png" alt=""> 
                            </span>
                        </td>
                        <td class="soclDetail">
                            <span class="productImage">
                                <img src="{{ asset('/') }}/assets/front-end/images/bg-image.jpg" alt=""> 
                            </span>
                        </td>
                        <td class="firDaat">
                            Text or post content will display here
                        </td>
                        <td class="custDetail">
                            Download
                        </td>
                        <td class="timer">
                            <a href="javascript:void(0)" class="cunformBtn" data-bs-toggle="modal" data-bs-target="#confirmpopup">Confirm</a>
                        </td>
                    </tr>
                    <tr class="campningDiv">
                        <td class="soclDetail">
                            <span class="handelpletform">
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-twitter.png" alt=""> 
                            </span>
                        </td>
                        <td class="soclDetail">
                            <span class="productImage">
                                <img src="{{ asset('/') }}/assets/front-end/images/bg-image.jpg" alt=""> 
                            </span>
                        </td>
                        <td class="firDaat">
                            Text or post content will display here
                        </td>
                        <td class="custDetail">
                            Download
                        </td>
                        <td class="timer">
                            <a href="javascript:void(0)" class="cunformBtn" data-bs-toggle="modal" data-bs-target="#confirmpopup">Confirm</a>
                        </td>
                    </tr>
                    <tr class="campningDiv">
                        <td class="soclDetail">
                            <span class="handelpletform">
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-twitter.png" alt=""> 
                            </span>
                        </td>
                        <td class="soclDetail">
                            <span class="productImage">
                                <img src="{{ asset('/') }}/assets/front-end/images/bg-image.jpg" alt=""> 
                            </span>
                        </td>
                        <td class="firDaat">
                            Text or post content will display here
                        </td>
                        <td class="custDetail">
                            Download
                        </td>
                        <td class="timer">
                            <a href="javascript:void(0)" class="cunformBtn" data-bs-toggle="modal" data-bs-target="#confirmpopup">Confirm</a>
                        </td>
                    </tr>
                    <tr class="campningDiv">
                        <td class="soclDetail">
                            <span class="handelpletform">
                                <img src="{{ asset('/') }}/assets/front-end/images/icons/campaigns-twitter.png" alt=""> 
                            </span>
                        </td>
                        <td class="soclDetail">
                            <span class="productImage">
                                <img src="{{ asset('/') }}/assets/front-end/images/bg-image.jpg" alt=""> 
                            </span>
                        </td>
                        <td class="firDaat">
                            Text or post content will display here
                        </td>
                        <td class="custDetail">
                            Download
                        </td>
                        <td class="timer">
                            <a href="javascript:void(0)" class="cunformBtn" data-bs-toggle="modal" data-bs-target="#confirmpopup">Confirm</a>
                        </td>
                    </tr>
                </tbody>
            </table>
            <!--modal timeRequest   form -->
            <div class="modal fade confirm-submit influencer" id="confirmpopup" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="reviewRatingLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-body">
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                            <div class="wizardHeading">Confirm Submission</div>
                            <div class="wizardForm"> 
                                <div class="col-xl-12 col-md-12 col-12"> 
                                    <div class="soclIn">
                                        <img src="{{ asset('/assets/front-end/images/confirmImage.png') }}" alt="">
                                    </div> 
                                    <div class="socialInformatin">
                                        <div class="soclIn">
                                            <img src="{{ asset('/assets/front-end/images/icons/campaigns-twitter.png') }}" alt="">
                                        </div>
                                        <div class="bold-text">
                                            https://www.facebook.com/photo/?fbid=741...
                                            <div class="connectType">
                                                Video On Demand
                                                <span>Download</span>
                                            </div>
                                        </div>                                        
                                    </div>
                                    <div class="bold-text mt-4">Are you sure, that this content was made for the request?</div>
                                </div>
                                <!-- Selected users //-->
                                <div class="widthBtnPopup d-flex">                                    
                                    <input type="submit" name="confirm"   value="Yes, Confirm" disabled id="review"   target="popup" data-bs-toggle="modal" data-bs-target="#reviewRatingPopup" >                                    
                                    <button type="submit" name="cancel" disabled id="complaint"  target="popup" data-bs-toggle="modal" data-bs-target="#complaintPopup">Cancel</button>
                                </div> 
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end  modal timeRequest   form --> 
            <div class="padeextra text-center">
                <p>Your Content does not show up?</p>
                <a href="#" class="cunformBtn">Contact support</a>
            </div>
        </div>
    </div>
</section>

@endsection

@section('script_links')
<script type="text/javascript" src="https://cdn.datatables.net/v/bs5/dt-1.13.1/datatables.min.js"></script>

@endsection

@section('script_codes')
<script>
  $(document).ready(function () {
    $('#venueTable').DataTable();
  });
</script>
@endsection