<style>
    .input-error {
        border: 2px solid red;
    }
</style>

<p class="caution-message">
    <strong>Caution:</strong> In the case of incorrect inputs, the influencer is not obliged to fulfill the task affected by them.
</p>
<div class="form-shadow-box half-box">
    <div class="new-titles">Campaign title <span class="color-red">*</span></div>
    <div class="form-group">
        <input
            type="text"
            class="form-control"
            name="campaign_title"
            id="campaign_title"
            aria-describedby="helpId"
            placeholder="Your campaign title"
            data-parsley-errors-container="#error-title"
            data-parsley-error-message="Please enter campaign title." required>
    </div>
    <span id="error-title"></span>
</div>

<div class="form-shadow-box half-box">
    <div class="new-titles">Brand name <span class="color-red">*</span></div>
    <div class="form-group">
        <input
            type="text"
            class="form-control"
            name="name"
            aria-describedby="helpId"
            placeholder="The advertised brand name"
            data-parsley-errors-container="#error-brand-name"
            data-parsley-error-message="Please enter brand name" required>
    </div>
    <span id="error-brand-name"></span>
</div>

@if(isset($tasks))  
    @foreach($tasks as $task)
        @if($task->task_type == 'required' &&  $task->input_type == 'SocialMediaName') 
            <div class="form-shadow-box half-box">
                <div class="new-titles">{{ $task->input_title }} <span class="color-red">*</span></div>
                <div class="form-group">
                    <div class="input-group">
                        <input
                            type="text"
                            class="form-control iconlast"
                            name="mentions{{ $task->id }}"
                            aria-describedby="helpId"
                            placeholder="{{ $task->input_placeholder }}"
                            data-parsley-errors-container="#error-profilename{{ $task->id }}"
                            data-parsley-error-message="Please enter profile name"
                            @if(isset($task->count) && $task->count != "") maxlength="{{ $task->count }}" @endif required>
                    </div>
                </div>
                <span id="error-profilename{{ $task->id }}"></span>
            </div>
        @endif
    @endforeach 

    @foreach($tasks as $task)
        @if($task->task_type == 'required' &&  $task->input_type == 'Link') 
            <div class="form-shadow-box half-box">
                <div class="new-titles">{{ $task->input_title }} <span class="color-red">*</span></div>
                <div class="form-group">
                    <div class="input-group">
                        <input
                            type="text"
                            class="form-control iconlast"
                            name="site{{ $task->id }}"
                            aria-describedby="helpId"
                            placeholder="{{ $task->input_placeholder }}"
                            data-parsley-errors-container="#{{ $task->id }}{{ $task->id }}"
                            data-parsley-error-message="Please enter your post link."
                            @if(isset($task->count) && $task->count != "") maxlength="{{ $task->count }}" @endif required>
                        <span class="input-group-text"><img src="{{ asset('/assets/front-end/images/icons/icon-form-link.svg') }}" class="" alt=""></span>
                    </div>
                </div>
                <span id="{{ $task->id }}{{ $task->id }}"></span>
            </div>
        @endif
    @endforeach

    @foreach($tasks as $task)
        @if($task->task_type == 'required' &&  $task->input_type == 'UploadContent') 
            <div class="form-shadow-box half-box upload-task-content">
                <div class="new-titles">{{ $task->input_title }} <span class="color-red">*</span></div>
                <div class="form-group">
                    <input
                        type="file"
                        name="filepond{{ $task->id }}"
                        data-parsley-errors-container="#error-upload-content{{ $task->id }}"
                        data-parsley-error-message="Please upload file."
                        id="selected-files-{{ $task->id }}"
                        placeholder="{{ $task->input_placeholder }}"
                        @if($task->media == "Instagram") data-type="{{ $task->select_type }}" class="upload_content" @endif required>
                </div>
                <span id="error-upload-content{{ $task->id }}"></span>
            </div>
        @endif
    @endforeach 
  
    @foreach($tasks as $task)
        @if($task->task_type == 'required' &&  $task->input_type == 'Hashtag') 
            <div class="form-shadow-box half-box requirhashtags">
                <div class="new-titles">{{ $task->input_title }} <span class="color-red">*</span></div>
                <div class="form-group">
                    <input type="hidden" id="hashtag_arr" class="hashtag_arr" value="{{ implode(',', $hashtag_arr) }}">
                    <input
                        name='hashtags{{ $task->id }}[]'
                        id="sds{{ $task->id }}"
                        class='taskList1 tagify--outside'
                        value='@if(isset($hashtag[0])) {{$hashtag[0]->tags}} @endif'
                        required
                        data-hasTagNumber=""
                        data-placeholder="{{ $task->input_placeholder}}">
                    <div class="required_message" id="error_category">This value is required.</div>
                </div>
                <span id="tagsError"></span>
                <span id="error-hashrags"></span>
            </div>  
            <script>
                var maxChars = "{{ $task->count ?? '45' }}";
                var maxTags = "{{ (isset($task->tag_count) && $task->tag_count!='')?$task->tag_count:$adminHashtag->hashtag }}";
                var hasTagPlaceholder = $(this).attr("data-placeholder");
                var input = document.querySelector("#sds{{ $task->id }}");
                
                // init Tagify script on the above inputs
                var tagify = new Tagify(input, {
                    maxTags: {{ (isset($task->tag_count) && $task->tag_count!='')?$task->tag_count:$adminHashtag->hashtag}},
                    pattern: new RegExp(`^.{1,${maxChars}}$`), 
                    dropdown: {
                        position: "input",
                        enabled : 0 // always opens dropdown when input gets focus
                    }
                });

                tagify.DOM.input.setAttribute('data-placeholder', "{{ $task->input_placeholder}}");

                $(input).data('tagify', tagify);

                @if(isset($task->count) && $task->count!= '')
                    tagify.on('invalid', function(e) {
                        var errorMsg;
                        if (e.detail.data && e.detail.data.value.length > maxChars) {
                            errorMsg = "Tag must be no more than " + maxChars + " characters long.";
                        } else if (tagify.value.length >= maxTags) {
                            errorMsg = "Cannot add more than " + maxTags + " tags.";
                        } else {
                            errorMsg = "Invalid OR Duplicate Tag.";
                        }

                        toastr.error(errorMsg);
                    });
                @endif
            </script>
        @endif
    @endforeach 
    
    {{-- Additional tasks --}}

    @if(isset($task_additional))
        @foreach($task_additional as $additional)
            @foreach($tasks as $task)
                @if($task->task_type == 'additional' &&  $task->input_type == 'SocialMediaName' && $task->id == $additional) 
                    <div class="form-shadow-box half-box">
                        <div class="new-titles">{{ $task->input_title }} <span class="color-red">*</span></div>
                        <div class="form-group">
                            <div class="input-group">
                                <input
                                    type="text"
                                    class="form-control iconlast"
                                    name="mentions{{ $task->id }}"
                                    aria-describedby="helpId"
                                    placeholder="{{ $task->input_placeholder }}"
                                    data-parsley-errors-container="#error-profilename{{ $task->id }}"
                                    data-parsley-error-message="Please enter profile name"
                                    @if(isset($task->count) && $task->count!="") maxlength="{{ $task->count }}" @endif required>
                            </div>
                        </div>
                        <span id="error-profilename{{ $task->id }}"></span>
                    </div>
                @endif
            @endforeach 

            @foreach($tasks as $task)
                @if($task->task_type == 'additional' &&  $task->input_type == 'Link' && $task->id == $additional)
                    <div class="form-shadow-box half-box">
                        <div class="new-titles">{{ $task->input_title }} <span class="color-red">*</span></div>
                        <div class="form-group">
                            <div class="input-group">
                                <input
                                    type="text"
                                    class="form-control iconlast"
                                    name="site{{ $task->id }}"
                                    aria-describedby="helpId"
                                    placeholder="{{ $task->input_placeholder }}"
                                    data-parsley-errors-container="#error-link-post{{ $task->id }}"
                                    data-parsley-error-message="Please enter your post link."
                                    @if(isset($task->count) && $task->count!="") maxlength="{{ $task->count }}" @endif required>
                                <span class="input-group-text">
                                    <img src="{{ asset('/assets/front-end/images/icons/icon-form-link.svg') }}" alt="">
                                </span>
                            </div>
                        </div>
                        <span id="error-link-post{{ $task->id }}"></span>
                    </div>
                @endif
            @endforeach

            @foreach($tasks as $task)
                @if($task->task_type == 'additional' &&  $task->input_type == 'UploadContent' && $task->id == $additional) 
                    <div class="form-shadow-box half-box upload-task-content">
                        <div class="new-titles">{{ $task->input_title }} <span class="color-red">*</span></div>
                        <div class="form-group">
                            <input
                                type="file"
                                name="filepond{{ $task->id }}"
                                id="selected-files-{{ $task->id }}"
                                @if($task->media=="Instagram") data-type="{{ $task->select_type }}" class="upload_content" @endif
                                data-parsley-errors-container="#error-upload-content1{{ $task->id }}"
                                data-parsley-error-message="Please upload file.">
                        </div>
                        <span id="error-upload-content1"></span>
                    </div>
                @endif
            @endforeach 
            
            @foreach($tasks as $task)
                @if($task->task_type == 'additional' &&  $task->input_type == 'Hashtag' && $task->id == $additional)
                    <div class="form-shadow-box half-box hashtags">
                        <div class="new-titles">{{ $task->input_title }} <span class="color-red">*</span></div>
                        <div class="form-group">
                            <input type="hidden" id="hashtag_arr" class="hashtag_arr" value="{{ implode(',',$hashtag_arr) }}">
                            <input
                                name='hashtags{{ $task->id }}[]'
                                id="sae{{ $task->id }}"
                                class='taskList tagify--outside'
                                data-hasTagNumber="{{ (isset($task->count) && $task->count!='') ? $task->count : $adminHashtag->hashtag}}"
                                value='@if(isset($hashtag[0])) {{$hashtag[0]->tags}} @endif'
                                required data-placeholder="{{ $task->input_placeholder}}">
                            <div class="required_message" id="error_category">This value is required.</div>
                        </div>
                        <span id="tagsError"></span>
                        <span id="error-hashrags"></span>
                    </div>  
                    <script>
                        var hasTagNumber = $(this).attr("data-hasTagNumber")
                        var hasTagPlaceholder = $(this).attr("data-placeholder")
                        var input = document.querySelector("#sae{{ $task->id }}")
                        var tagify = new Tagify(input, {
                            maxTags: {{ (isset($task->count) && $task->count!='')?$task->count:$adminHashtag->hashtag}},
                            dropdown: {
                                position: "input",
                                enabled : 0 // always opens dropdown when input gets focus
                            }
                        })
                        tagify.DOM.input.setAttribute('data-placeholder', "{{ $task->input_placeholder}}")
                    </script>
                @endif
            @endforeach
        @endforeach
    @endif
@endif
<script type="text/javascript">
    $(document).ready(function() {
        $('.upload_content').on('change', function(e) {
            let type = $(this).data('type');
            let fieldId  = $(this).attr('id');
            if (type == "Photo - Story - picture") {
                var files = e.target.files;
                var allowedExtensions = /(\.png|\.jpg)$/i;
                var maxSizeMB = 30;
                var allFilesValid = true;

                for (var i = 0; i < files.length; i++) {
                    var file = files[i];

                    // Check file extension
                    if (!allowedExtensions.exec(file.name)) {
                        allFilesValid = false;
                        toastr.error('Only .PNG and .JPG files are allowed.')
                        $(this).val(''); // Clear the selected files
                        break;
                    }

                    // Check file size
                    if (file.size > maxSizeMB * 1024 * 1024) {
                        allFilesValid = false;
                        toastr.error('File size must be less than '+maxSizeMB+'MB.')
                        $(this).val(''); // Clear the selected files
                        break;
                    }
                }
            } else if(type=="Video - Story - video" || type=="Video - Post - video" || type=="Video - Reel") {
                var files = e.target.files;
                var allowedExtensions = /(\.mp4|\.mov)$/i;
                var maxSizeMB = 500;
                var allFilesValid = true;

                for (var i = 0; i < files.length; i++) {
                    var file = files[i];

                    // Check file extension
                    if (!allowedExtensions.exec(file.name)) {
                        allFilesValid = false;
                        toastr.error('Only .MP4 and .MOV files are allowed.')
                        $(this).val(''); // Clear the selected files
                        break;
                    }

                    // Check file size
                    if (file.size > maxSizeMB * 1024 * 1024) {
                        allFilesValid = false;
                        toastr.error('File size must be less than 500MB.')
                        $(this).val(''); // Clear the selected files
                        break;
                    }

                    // Check video duration
                    if (file.type.match('video.*')) {
                        var video = document.createElement('video');
                        video.preload = 'metadata';
                        video.onloadedmetadata = function() {
                            window.URL.revokeObjectURL(video.src);
                            var duration = video.duration;
                            if(type=="Video - Story - video") {
                                if (duration < 3 || duration > 60) {
                                    toastr.error('Video duration must be between 3 and 60 seconds.')
                                    $('#'+fieldId).val('');// Clear the selected files
                                    allFilesValid = false;
                                }
                            } else if(type=="Video - Post - video") {
                                if (duration < 3 || duration > 60*15) {
                                    toastr.error('Video duration must be between 3 seconds and 15 minutes.')
                                    $('#'+fieldId).val('');// Clear the selected files
                                    allFilesValid = false;
                                }
                            } else if(type=="Video - Reel") {
                                if (duration < 3 || duration > 60*15) {
                                    toastr.error('Video duration must be between 3 seconds and 15 minutes.')
                                    $('#'+fieldId).val('');// Clear the selected files
                                    allFilesValid = false;
                                }
                            }

                            if (allFilesValid) {
                                console.log('All files are valid. Proceed with upload.');
                            }
                        };

                        video.src = URL.createObjectURL(file);
                    }
                }
            }
        });
    });
</script>
