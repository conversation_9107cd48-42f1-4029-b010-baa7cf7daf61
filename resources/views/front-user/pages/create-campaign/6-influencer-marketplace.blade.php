{{-- <div class="page_tab new_steps" id="new_steps3"> --}}
<div class="page_tab new_steps sub-step-marketplace-select-influencer d-none" id="create-campaign-step-6">
    <div class="mobile-step-detail">
        <div class="steps-cont">STEP 04</div>
        <div class="steps-which">Marketplace</div>
    </div>
    <div id="influencer-marketplace-container" style="padding-bottom: 20px;">
        {{-- Data will be loaded from ajax --}}
        {{-- View will be generated from this template --}}
        {{-- @include('front-user.pages.create-campaign.campaign-marketplace-influencers') --}}
        <div style="width: 100%; max-width: 200px; height: 200px; display: flex; align-items: center; justify-content: center;">
            <img src="{{ asset('/assets/front-end/images/loading.gif') }}" alt="Loading..." style="width: 50px; height: 50px;">
        </div>
    </div>
    <div class="step-nevigationbutton">
        <div class="nav-left" data-from-step="">
            <img src="{{ asset('/assets/front-end/images/icons/step-left.svg') }}" alt="">
        </div>
        <div class="backtop" id="backtop">
            <img src="{{ asset('/assets/front-end/images/icons/warenkorb_button.png') }}" alt="">
        </div>
        <div class="influencer-cart">
            <span class="influencer-cart-count">0</span>
            <img src="{{ asset('/assets/front-end/images/icons/influencer-cart.svg') }}" alt="">
        </div>
        <div class="selected-influencer-box">
            <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" class="close_inf_box" alt="">
            <div class="selected-influencer-in-cart"></div>
            <div class="selected-influencer-data">
                <div class="data-detail">
                    <div class="uj">
                        <img src="{{ asset('/assets/front-end/images/icons/req-money.svg') }}" alt="">
                    </div>
                    Subtotal: <span id="subtotal" data-subtotal="0">0.00</span> €
                </div>
                <div class="data-detail">
                    <div class="uj">
                        <img src="{{ asset('/assets/front-end/images/icons/icon-eye.svg') }}" alt="">
                    </div>
                    <span id="follower" data-followers="0">0</span> follower
                </div>
            </div>
        </div>
        <div class="nav-right">
            <img src="{{ asset('/assets/front-end/images/icons/step-right.svg') }}" alt="">
        </div>
    </div>
    <div class="filter-overlay"></div>
</div>
@push('step-scripts')
<script>
    $('#create-campaign-step-6 .nav-left').click(function() {
        prevStep = currentStep;
        currentStep -= 1;

        // Reset max selected influencer to 0
        // maxInfluencerCounter = 0;

        // Remove generated html of selected influencers
        $('#influencer-marketplace-container').html(`<img src="{{ asset('/assets/front-end/images/loading.gif') }}" alt="Loading...">`);
        $('#create-campaign-step-7 #influencers-row').html("");

        // Reset selected influencer tracker
        // selectedInfluencers = {};

        hideStepContent(prevStep);

        // We will use here useCached = true, so that we keep the tasks input fields
        // content in case user entered something, as we are going back here
        // from influencer marketplace to campaign tasks details step
        showStepContent(currentStep, true);
    });

    $('#create-campaign-step-6 .nav-right').click(function() {
        // Now we add the influencer list if there are already selected any
        if (getSelectedInfluencersCount() == 0) {
            toastr.error("Please select at least one influencer to continue.");
            return false;
        }

        prevStep = currentStep;
        currentStep += 1;

        console.log('******** step 6: influencer market place');
        console.log('currentStep: ' + currentStep);
        console.log('prevStep: ' + prevStep);
        console.log('formData: ');
        console.log(formData);

        var latestFormData = $('#form-create-campaign').serializeArray();
        $.each(latestFormData, function(i, field) {
            if (!formData[field.name]) {
                formData[field.name] = field.value;   
            }
        });

        hideStepContent(prevStep);
        showStepContent(currentStep);

        updateTotals();
    });

    // Select influencers on marketplace
    $(document).on("click", ".influencer-detail button.select-button", function(event) {
        if (getSelectedInfluencersCount() >= MAX_INFLUENCER_IN_CAMPAIGN) {
            toastr.info("Maximum number of influencer limit reached. ");
            return false;
        }

        const $influencerDetail = $(this).closest(".influencer-detail");
        const get_influencer_id = $influencerDetail.attr("data-id");
        const get_influencer_image = $influencerDetail.find(".profile-pic").html();
        const get_influencer_image_new = $influencerDetail.find(".profile-pic img").attr("src");
        const get_influencer_username = $influencerDetail.find(".user_name").text();
        const is_small_business_owner = Boolean(parseInt($influencerDetail.find("#is_small_business_owner").text() || "0"));
        const get_influencer_follower = $influencerDetail.find(".follower-count-val").data("followers") || 0;
        const get_influencer_price = $influencerDetail.find(".user_price-val").data("price") || 0;

        const influencer_id = get_influencer_id.replace(/influencer_id/, '');

        // Add to influencer list
        let selectedInfluencer;
        if (!selectedInfluencers.hasOwnProperty(influencer_id)) {
            selectedInfluencer = new InfluencerData({
                influencerId: influencer_id,
                followers: get_influencer_follower,
                price: get_influencer_price,
                vat: !is_small_business_owner ? (get_influencer_price * VAT_RATE) : 0,
                isSmallBusinessOwner: is_small_business_owner,
                influencerImage: get_influencer_image,
                influencerImageNew: get_influencer_image_new,
                influencerUsername: get_influencer_username
            });
            selectedInfluencers[influencer_id] = selectedInfluencer;
        } else {
            selectedInfluencer = selectedInfluencers[influencer_id];
        }

        // Add to selected influencers to the cart view
        var influencerDiv = "<div class='influencer-detail card-container text-center mx-auto mt-4' data-id='" + get_influencer_id + "'>" + $influencerDetail.html() + "</div>";
        $(".selected-influencer-in-cart").append(influencerDiv);
        $("span.influencer-cart-count").text(getSelectedInfluencersCount());
        $(".selected-influencer-in-cart .influencer-detail button").text("Remove").removeClass("select-button").addClass("remove-button");
        $influencerDetail.addClass("selected");

        $(this).text("UNSELECT").removeClass("select-button").addClass("unselect-button");

        const toggleImage = document.getElementById(`card_clickitfame_logo_${get_influencer_id}`);
        if (toggleImage) {
            toggleImage.src = window.Laravel.assets.icons.clickitfameWhite;
        }

        console.log('*****************');
        console.log('Selected influencers: ');
        console.log(selectedInfluencers);
        console.log('influencerVAT: ' + influencerVAT);
        console.log('get_influencer_price: ' + selectedInfluencer.price);
        console.log('get_influencer_follower: ' + selectedInfluencer.followers);

        updateTotals();
    });

    // Unselect influencers on marketplace
    $(document).on("click", ".influencer-detail button.unselect-button", function() {
        const influencer_id = $(this).closest(".influencer-detail").attr("data-id").replace(/influencer_id/, '');
        
        if (!selectedInfluencers.hasOwnProperty(influencer_id)) {
            toastr.error('Something went wrong. Please refresh the page and try again.');
            return false;
        }
        
        $('.selected-influencer-in-cart div[data-id="influencer_id' + influencer_id + '"]').remove();

        $('.influencer-marketplace.influencer-list div[data-id="influencer_id' + influencer_id + '"]').removeClass("selected");
        $("span.influencer-cart-count").text($(".selected-influencer-in-cart .influencer-detail").length);

        const toggleImage = document.getElementById(`card_clickitfame_logo_influencer_id${influencer_id}`);
        if (toggleImage) {
            toggleImage.src = window.Laravel.assets.icons.clickitfameLogo;
        }

        $('.influencer-marketplace.influencer-list div[data-id="influencer_id' + influencer_id + '"] button.unselect-button')
            .text("SELECT")
            .removeClass("unselect-button")
            .addClass("select-button");

        delete selectedInfluencers[influencer_id];

        updateTotals();
        
        // TODO from which view this is removing what???
        // $(".influencer_id" + influencer_id).remove();
    });

    // Show influencer marketplace filter
    $(document).on("click", ".button-open-filter", function() {
        $(".filter-button-outer").hide()
        $(".filter-box-outer").show()
        $("select.filter-by").select2();
        $("select").select2();
    });

    // Reset all influencer marketplace filter
    $(document).on('click', '.resetFilter', function(e) {
        $('#sort_by option[value=""]').attr('selected', 'selected');
        $('#target_age option[value=""]').attr('selected', 'selected');
        $('#language option[value=""]').attr('selected', 'selected');
        $('#content_attracts option[value=""]').attr('selected', 'selected');
        $('#hashtags').val(null).trigger('change');
        $('#influencer_type option[value=""]').attr('selected', 'selected');
        $('#gender option[value=""]').attr('selected', 'selected');
        $('#target_gender option[value=""]').attr('selected', 'selected');
        $('#rank option[value=""]').attr('selected', 'selected');

        $('#followers option[value=""]').attr('selected', 'selected');
        $('#amount-followers').val('0');
        $('#amount-price').val('0');
        getFilteredInfluencers();
    });

    $(document).on("click", ".button-close-filter", function() {
        $(".filter-button-outer").show()
        $(".filter-box-outer").hide()
    });

    // Open the influencer shopping cart
    $(document).on("click", ".step-nevigationbutton > div.influencer-cart", function() {
        $(".selected-influencer-box").toggleClass("open")
        $(".filter-overlay").toggleClass("open")
    });

    // Close the influencer shopping cart
    $(document).on("click", ".step-nevigationbutton > div > img.close_inf_box", function() {
        $(".selected-influencer-box").toggleClass("open")
        $(".filter-overlay").toggleClass("open")
    });

    // This remove button belongs to the influencer cart
    $(document).on("click", ".influencer-detail button.remove-button", function() {
        var get_influencer_id = $(this).closest(".influencer-detail").attr("data-id");
        const influencer_id = get_influencer_id.replace(/influencer_id/, '');
        
        if (!selectedInfluencers.hasOwnProperty(influencer_id)) {
            toastr.error('Something went wrong. Please refresh the page and try again.');
            return false;
        }

        var selectedInfluencer = selectedInfluencers[influencer_id];
        
        $(this).closest(".influencer-detail").remove();

        $('.influencer-marketplace.influencer-list div[data-id="influencer_id' + influencer_id + '"]').removeClass("selected");

        const toggleImage = document.getElementById(`card_clickitfame_logo_influencer_id${influencer_id}`);
        if (toggleImage) {
            toggleImage.src = window.Laravel.assets.icons.clickitfameLogo;
        }

        $('.influencer-marketplace.influencer-list div[data-id="influencer_id' + influencer_id + '"] button.unselect-button')
            .text("SELECT")
            .removeClass("unselect-button")
            .addClass("select-button");

        // Remove selected influencerIds
        delete selectedInfluencers[influencer_id];
        $("span.influencer-cart-count").text(getSelectedInfluencersCount());
        
        // Update totals
        updateTotals();
        
        // TODO From which view this removing what???
        // $(".influencer_id" + influencer_id).remove();
    });
</script>
@endpush