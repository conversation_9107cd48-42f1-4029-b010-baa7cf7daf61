<div class="influencer-filter filter-button-outer">
    <button type="button" class="button-open-filter">
        (0) Filter <img src="{{ asset('/assets/front-end/images/icons/filter-icon.svg') }}" alt="">
    </button>
    <img src="{{ asset('/assets/front-end/images/icons/new_social_media_' . $formData['social_media_name']. '.png') }}" alt="">
</div>
<div class="influencer-filter filter-box-outer">
    <div class="filter-div">
        <div class="filter-buttons">
            <button type="button" class="button-close-filter">
                (0) Filter <img src="{{ asset('/assets/front-end/images/icons/filter-icon.svg') }}" alt="">
            </button>
            <select class="filter-by sort_by checkFilter" name="sort_by" id="sort_by">
                <option value="">Sort by</option>
                <option value="followers-DESC" @if (isset($formData['sort_by']) && $formData['sort_by'] == 'followers-DESC') selected @endif>Follower High to low</option>
                <option value="followers-ASC" @if (isset($formData['sort_by']) && $formData['sort_by'] == 'followers-ASC') selected @endif>Follower Low to High</option>
                <option value="price-DESC" @if (isset($formData['sort_by']) && $formData['sort_by'] == 'price-DESC') selected @endif>Price High to low</option>
                <option value="price-ASC" @if (isset($formData['sort_by']) && $formData['sort_by'] == 'price-ASC') selected @endif>Price Low to High</option>
            </select>
            <button type="button" class="button-reset-filter resetFilter">
                Reset <img src="{{ asset('/assets/front-end/images/icons/icon-reset-filter.svg') }}" alt="">
            </button>
        </div>
        <div class="filter-form">
            <div class="form-group">
                <label>Target Age</label>
                <select class="select form-control checkFilter" name="target_age" id="target_age">
                    <option value="">Select</option>
                    @php $ages = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @php array_push($ages,$row->ages);@endphp
                        @endforeach
                    @endif

                    @php $ages= array_unique($ages); @endphp
                    @foreach ($ages as $row)
                        <option @if (isset($formData['target_age']) && $formData['target_age'] == $row) selected @endif>{{ $row }}</option>
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label>Target Gender</label>
                <select class="select form-control checkFilter" name="content_attracts" id="target_gender">
                    <option value="">Select</option>
                    @php $content_attracts = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @php array_push($content_attracts, $row->content_attracts);@endphp
                        @endforeach
                    @endif
                    @php $content_attracts = array_unique($content_attracts); @endphp
                    @foreach ($content_attracts as $row)
                        @if ($row != '')
                            <option @if (isset($formData['content_attracts']) && $formData['content_attracts'] == $row) selected @endif>{{ $row }}</option>
                        @endif
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label>Influencer type</label>
                <select class="select form-control checkFilter" name="influencer_type" id="influencer_type">
                    <option value="">Select</option>
                    @php $influencer_type = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @php  array_push($influencer_type,$row->influencer_type);@endphp
                        @endforeach
                    @endif
                    @php  $influencer_type= array_unique($influencer_type); @endphp
                    @foreach ($influencer_type as $row)
                        @if ($row != '')
                            <option @if (isset($formData['influencer_type']) && $formData['influencer_type'] == $row) selected @endif>{{ $row }}</option>
                        @endif
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label>Minimum Followers</label>
                <select name="followers" class="form-control checkFilter" id="minimum-followers">
                    <option value="">Select</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '4') Selected @endif value="4">5</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '500') Selected @endif value="500">500</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '1000') Selected @endif value="1000">1000</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '10000') Selected @endif value="10000">10000</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '50000') Selected @endif value="50000">50000</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '100000') Selected @endif value="100000">100000</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '500000') Selected @endif value="500000">500000</option>
                    <option @if (isset($formData['followers']) && $formData['followers'] == '1000000') Selected @endif value="1000000">1000000+</option>
                </select>
            </div>
            <div class="form-group">
                <label>Rank</label>
                <select class="form-control checkFilter" name="rank" id="rank">
                    <option value="">Select</option>

                    @php $pricing = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @php  array_push($pricing,$row->pricing);@endphp
                        @endforeach
                    @endif
                    @php  $pricing= array_unique($pricing); @endphp
                    @foreach ($pricing as $row)
                        @if ($row != '')
                            <option @if (isset($formData['rank']) && $formData['rank'] == $row) selected @endif>{{ $row }}</option>
                        @endif
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label>Target Language</label>
                <select class="select form-control checkFilter" name="language" id="language">
                    <option value="">Select</option>
                    @php $language = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @php  array_push($language,$row->content_language);@endphp
                        @endforeach
                    @endif
                    @php  $language= array_unique($language); @endphp
                    @foreach ($language as $row)
                        @if ($row != '')
                            <option @if (isset($formData['language']) && $formData['language'] == $row) selected @endif>{{ $row }}</option>
                        @endif
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label>Influencer category</label>
                <select class="form-control  checkFilter" id="selectCategory" name="category_id[]" multiple>
                    @if ($category != '')
                        @foreach ($category as $row)
                            <option value="{{ $row->id }}">{{ $row->name }}</option>
                        @endforeach
                    @endif
                </select>
            </div>
            <div class="form-group">
                <label>Influencer hashtag</label>
                <select class="select form-control multiple floating-input checkFilter" multiple="multiple" name="hashtag[]" id="hashtags">
                    @php $hashtags = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @if (isset($row->tags))
                                @foreach ($row->tags as $tags)
                                    @php  array_push($hashtags,$tags->tags);@endphp
                                @endforeach
                            @endif
                        @endforeach
                    @endif
                    @php $hashtags= array_unique($hashtags); @endphp
                    @if ($hashtags != '')
                        @foreach ($hashtags as $row)
                            @if ($row != '')
                                <option @if (isset($formData['hashtags']) && in_array($row, $formData['hashtags'])) selected @endif>{{ $row }}</option>
                            @endif
                        @endforeach
                    @endif
                </select>
            </div>
            <div class="form-group">
                <label>Influencer gender</label>
                <select class="select form-control checkFilter" name="gender" id="gender">
                    <option value="">Select</option>
                    @php $gender = []; @endphp
                    @if (isset($influencerDetails))
                        @foreach ($influencerDetails as $row)
                            @php array_push($gender,$row->gender); @endphp
                        @endforeach
                    @endif
                    @php  $gender= array_unique($gender); @endphp
                    @foreach ($gender as $row)
                        @if ($row != '')
                            <option @if (isset($formData['gender']) && $formData['gender'] == $row) selected @endif>{{ $row }}</option>
                        @endif
                    @endforeach
                </select>
            </div>
            <div class="form-group">
                <label>Price (maximum)</label>
                <input type="text" name="price" class="checkFilter" id="amount-price"
                    value="{{ isset($formData['price']) ? $formData['price'] : 0 }}"
                    oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');">
            </div>
        </div>
    </div>
    <img src="{{ asset('/assets/front-end/images/icons/new_social_media_' . $formData['social_media_name'] . '.png') }}" alt="">
</div>