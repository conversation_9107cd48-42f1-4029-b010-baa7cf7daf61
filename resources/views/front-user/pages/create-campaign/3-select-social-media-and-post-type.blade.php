<div class="page_tab sub-step-select-social-media d-none" id="create-campaign-step-3">
    <div class="mobile-step-detail">
        <div class="steps-cont">STEP 01</div>
        <div class="steps-which">Social Media</div>
    </div>

    <span id="error-select-social-media" style="color: red; font-size: 14px; display: block; margin-top: 5px;"></span>
    <span id="error-post-type" style="color: red; font-size: 14px; display: block; margin-top: 5px;"></span>

    <div class="social_media d-flex align-items-center justify-content-between flex-wrap">
        {{-- Social media radio group for instagram --}}
        <div class="social_media_radio instagram" style="pointer-events: auto; filter: blur(0);">
            <span class="media_platform">
                <input type="radio"
                    name="social_media_name"
                    data-social-media="instagram"
                    data-parsley-errors-container="#error-select-social-media"
                    data-parsley-error-message="Please select social media."
                    required
                    value="instagram">
                <label>
                    <img src="{{ asset('/assets/front-end/images/icons/new_social_media_instagram.png') }}" class="mp-socialmedia-icon" alt=""> Instagram
                </label>
            </span>
            <span class="media_platform_post_type">
                <div class="get_type social-media-instagram boost-me content d-none" id="boost-me-content-post-content-type">
                    <div class="form-group">
                        <input type="radio" name="post_type" value="Story" class="post-type-radio">
                        <label>Story</label>
                    </div>
                </div>
                <div class="get_type social-media-instagram boost-me photo d-none" id="boost-me-photo-post-content-type">
                    <div class="form-group">
                        <input type="radio" name="post_type" value="Story - Picture" class="post-type-radio">
                        <label>Story - Picture</label>
                    </div>
                </div>
                <div class="get_type social-media-instagram boost-me video d-none" id="boost-me-video-post-content-type">
                    <div class="form-group">
                        <input type="radio" name="post_type" value="Story - Video" class="post-type-radio">
                        <label>Story - Video</label>
                    </div>
                </div>
                <div class="get_type social-media-instagram reaction-video d-none" id="reaction-video-post-content-type">
                    <div class="form-group">
                        <input type="radio" name="post_type" value="Reel" class="post-type-radio">
                        <label>Reel</label>
                    </div>
                </div>
                <div class="get_type social-media-instagram survey d-none" id="survey-post-content-type">
                    <div class="form-group">
                        <input type="radio" name="post_type" value="Story - Picture" class="post-type-radio">
                        <label>Story - Picture</label>
                    </div>
                    <div class="form-group">
                        <input type="radio" name="post_type" value="Story - Video" class="post-type-radio">
                        <label>Story - Video</label>
                    </div>
                </div>
            </span>
        </div>

        {{-- Social media radio group for facebook --}}
        <div class="social_media_radio facebook" style="pointer-events: none; filter: blur(2px);">
            <span class="media_platform">
                <input type="radio" name="social_media_name" data-social-media="facebook" value="facebook" disabled>
                <label>
                    <img src="{{ asset('/assets/front-end/images/icons/new_social_media_facebook.png') }}" class="mp-socialmedia-icon" alt=""> Facebook
                </label>
            </span>
        </div>

        {{-- Social media radio group for youtube --}}
        <div class="social_media_radio youtube" style="pointer-events: none; filter: blur(2px);">
            <span class="media_platform">
                <input type="radio" name="social_media_name" data-social-media="youtube" value="youtube" disabled>
                <label>
                    <img src="{{ asset('/assets/front-end/images/icons/new_social_media_youtube.png') }}" class="mp-socialmedia-icon" alt=""> YouTube
                </label>
            </span>
        </div>

        {{-- Social media radio group for tiktok --}}
        <div class="social_media_radio tiktok" style="pointer-events: none; filter: blur(2px);">
            <span class="media_platform">
                <input type="radio" name="social_media_name" data-social-media="tiktok" value="tiktok" disabled>
                <label>
                    <img src="{{ asset('/assets/front-end/images/icons/new_social_media_tiktok.png') }}" class="mp-socialmedia-icon" alt=""> TikTok
                </label>
            </span>
        </div>
    </div>

    <div class="step-nevigationbutton create-campaign-steps">
        <div class="nav-left" data-from-step="">
            <img src="{{ asset('/assets/front-end/images/icons/step-left.svg') }}" alt="">
        </div>
        <div class="nav-right">
            <img src="{{ asset('/assets/front-end/images/icons/step-right.svg') }}" alt="">
        </div>
    </div>
</div>

@push('step-scripts')
<script>
    $('#create-campaign-step-3 .nav-right').click(function() {
        if (!formData.social_media_name) {
            toastr.error('Please select social media for your campaign.');
            return false;
        }

        if (!formData.post_type) {
            toastr.error('Please select post type for your campaign.');
            return false;
        }

        prevStep = currentStep;
        currentStep += 1;

        console.log('******** step 3: select social media and post type');
        console.log('currentStep: ' + currentStep);
        console.log('prevStep: ' + prevStep);
        console.log('formData: ');
        console.log(formData);

        hideStepContent(prevStep);
        showStepContent(currentStep);
    });

    $('#create-campaign-step-3 .nav-left').click(function() {
        prevStep = currentStep;

        if(formData.campaign_type == CAMPAIGN_TYPE_BOOST_ME) {
            currentStep -= 1;
        } else {
            // Here, prevStep is getting -1 more because we have extra step (step 2)
            // for Boost me only, where user choose post type content.
            // For other campaign types, we directly go to step 3
            currentStep -= 2;
        }

        // Reset all social_media_name radio buttons
        $('input[name="social_media_name"]').prop('checked', false);
        // Reset all post_type radio buttons
        $('input[name="post_type"]').prop('checked', false);

        // Deactivate all social media radio group boxes
        $(".social_media_radio").removeClass("active");

        // Hide all the post_type radio group containers
        $(".media_platform_post_type").hide();

        $('#create-campaign-step-3 .get_type').removeClass('d-block').addClass('d-none');

        console.log('******** step 2: select boost me post type');
        console.log('currentStep: ' + currentStep);
        console.log('prevStep: ' + prevStep);
        console.log('formData: ');
        console.log(formData);

        hideStepContent(prevStep);
        showStepContent(currentStep);
    });

    $('input[name="social_media_name"]').click(function() {
        if ($(this).is(':checked')) {
            var socialmediaName = $(this).val();
            formData.social_media_name = socialmediaName;

            // Deactivate all social media radio group boxes
            $(".social_media_radio").removeClass("active");

            // Enable only the clicked one
            $(".social_media_radio." + socialmediaName).addClass("active");

            // Hide all the post_type radio group containers
            $(".media_platform_post_type").hide();

            // Now display only for the spcific social media one
            $(".social_media_radio." + socialmediaName + " .media_platform_post_type").css('display', 'flex');
            
            // Now we show the post_type based on campaign_type, boost_me_content_type and social_media_name
            var postTypeSelector = '.social-media-' + formData.social_media_name;
            if (formData.campaign_type == 'Boost me') {
                postTypeSelector += '.boost-me';
                postTypeSelector += '.' + formData.boost_me_content_type;
            } else if (formData.campaign_type == 'Reaction video') {
                postTypeSelector += '.reaction-video';
            } if (formData.campaign_type == 'Survey') {
                postTypeSelector += '.survey';
            } else {
                // Raise error
            }            
            $(postTypeSelector).removeClass('d-none').addClass('d-block');

            // Show the post type options for this social media
            // var postTypeContainer = $(this).closest(".media_platform").next(".media_platform_post_type");
            // postTypeContainer.css("display", "flex");
        }
    });

    $('input[name="post_type"]').click(function() {
        if ($(this).is(':checked')) {
            var postType = $(this).val();
            formData.post_type = postType;
        }
    });
</script>
@endpush