<style>
    @media screen and (max-width: 768px) {
        /* The influencer list in the payment page */
        .campaign-list {
            font-size: 7px;
        }
    }
</style>
<div class="page_tab new_steps sub-step-payment-details d-none" id="create-campaign-step-7">
{{-- <div class="page_tab new_steps" id="new_steps4"> --}}
    <div class="mobile-step-detail">
        <div class="steps-cont">STEP 05</div>
        <div class="steps-which">Payment details</div>
    </div>
    <div class="marketplace-finish">
        <div id="influencers-row">
            {{-- Will be filled up by ajax --}}
        </div>
        <div class="order-summary">
            <div class="summary-column" style="width:auto;">
                <div class="summary-item">
                    <span class="label">Subtotal:</span>&nbsp;
                    <span class="value">
                        <span id="follower_in_payment_page" data-followers="0">0</span> Followers
                    </span>
                </div>
            </div>
            <div class="summary-column" style="display: flex; flex-direction:column; width:auto;">
                <div class="summary-item">
                    <span class="label">Price:</span>&nbsp;
                    <span class="value">€ <span id="subtotal_in_payment_page" data-subtotal="0">0</span> (VAT excluded)</span>
                </div>
                <div class="summary-item">
                    <span class="label">Platform Fee (5%): &nbsp;</span>
                    <span class="value">€ <span id="fee_in_payment_page" data-fee="0">0</span> (VAT excluded)</span>
                </div>
                <div class="summary-item">
                    <span class="label">VAT:</span>
                    <span class="value">€ <span id="vat_in_payment_page" data-vat="0">0</span></span>
                </div>
            </div>
        </div>
        <div class="total-container">
            <div class="total-item">
                <span class="total-label">Order Total:</span>
                <span class="total-value" style="text-align: end;">
                    € <span id="total_in_payment_page" data-total="0">0</span> (VAT included)
                </span>
            </div>
        </div>
        <div class="info-text">
            <p>** You are starting the request phase. Influencer can accept or reject your request. Depending on
                this, your order total will change. After the request phase you will be able to pay and start the
                campaign. **
            </p>
        </div>
        <div class="terms-div middle-box text-center" style="display: none;">
            ** You are starting the request phase. Influencer can accept or reject your request. Depending on this,
            your order total will change. After the request phase you will be able to pay and start the campaign. **
        </div>
        <div class="step-nevigationbutton" style="position: relative; bottom:0;">
            <div class="nav-left" data-from-step="">
                <img src="{{ asset('/assets/front-end/images/icons/step-left.svg') }}" alt="">
            </div>
            <div class="submit-button-infl">
                <button type="submit" class="button-Request">Request</button>
            </div>
            <div class="nav-right opacity-0 pe-none">
                <img src="{{ asset('/assets/front-end/images/icons/step-right.svg') }}" alt="">
            </div>
        </div>
    </div>
</div>

@push('step-scripts')
<script>
    $(document).on("click", "#create-campaign-step-7 .campaign-item .remove-icon img", function() {
        const get_influencer_id = $(this).data("delete");
        const influencer_id = get_influencer_id.replace(/influencer_id/, '');
        
        // Remove the influencer from the payment page view
        $("#influencers-row .campaign-list.influencer_id" + influencer_id).remove();
        
        // Remove the influencer from the cart view
        $('.selected-influencer-in-cart div[data-id="influencer_id' + influencer_id + '"]').remove();
        
        // Remove the influencer from the marketplace view
        $('.influencer-marketplace.influencer-list div[data-id="influencer_id' + influencer_id + '"]').removeClass("selected");
        
        // Reset the influencer select button state in the marketplace view
        $('.influencer-marketplace.influencer-list div[data-id="influencer_id' + influencer_id + '"] button.unselect-button')
            .text("SELECT")
            .removeClass("unselect-button")
            .addClass("select-button");
        
        // Update the logo image
        const toggleImage = document.getElementById(`card_clickitfame_logo_influencer_id${influencer_id}`);
        if (toggleImage) {
            toggleImage.src = window.Laravel.assets.icons.clickitfameLogo;
        }

        delete selectedInfluencers[influencer_id];

        // Update the selected influencers count in the cart
        $(".influencer-cart span.influencer-cart-count").text(getSelectedInfluencersCount());

        // Update totals
        updateTotals();
    });

    $(document).on("click", "#create-campaign-step-7 .button-Request", function() {
        if (getSelectedInfluencersCount() == 0) {
            toastr.error("Please select at least one influencer before proceeding.");
            return false;
        }

        $("#pageLoader").show();

        // Ensure form is valid before submission
        const $form = $('#form-create-campaign');

        var totalAmount = 0;
        $.each(selectedInfluencers, function(key, influencer) {
            if (influencer && influencer.price) {
                totalAmount += influencer.price;
            }
        });
        const totalAmountInput = $('<input>')
            .attr('type', 'hidden')
            .attr('name', 'total_amount')
            .val(totalAmount);

        $form.append(totalAmountInput);

        // Append selectedInfluencers to the form as a hidden input
        const influencersInput = $('<input>')
            .attr('type', 'hidden')
            .attr('name', 'selectedInfluencers')
            .val(JSON.stringify(selectedInfluencers));

        $form.append(influencersInput);
        
        // if ($form.length && typeof $form.parsley === 'function') {
        //     return $form.parsley().validate();
        // }

        // $form.submit();
    });

    $('#create-campaign-step-7 .nav-left').click(function() {
        prevStep = currentStep;
        currentStep -= 1;

        hideStepContent(prevStep);
        showStepContent(currentStep);
    });
</script>
@endpush