{{-- <div class="page_tab new_steps" id="new_steps2"> --}}
<div class="page_tab new_steps sub-step-campaign-details d-none" id="create-campaign-step-5">
    <div class="mobile-step-detail">
        <div class="steps-cont">STEP 03</div>
        <div class="steps-which">Campaign details</div>
    </div>
    <div class="task-options new" id="campaign-detail-with-additional-tasks-input">
        <div style="width: 100%; max-width: 200px; height: 200px; display: flex; align-items: center; justify-content: center;">
            <img src="{{ asset('/assets/front-end/images/loading.gif') }}" alt="Loading..." style="width: 50px; height: 50px;">
        </div>
    </div>
    <div class="step-nevigationbutton">
        <div class="nav-left" data-from-step="">
            <img src="{{ asset('/assets/front-end/images/icons/step-left.svg') }}" alt="">
        </div>
        <div class="nav-right">
            <img src="{{ asset('/assets/front-end/images/icons/step-right.svg') }}" alt="">
        </div>
    </div>
</div>
@push('step-scripts')
<script>
    $('#create-campaign-step-5 .nav-left').click(function() {
        prevStep = currentStep;
        currentStep -= 1;

        // $('#campaign-detail-with-additional-tasks-input').find('input[type="text"]').val("");

        hideStepContent(prevStep);
        
        // We will use useCached = true here, because we are going back and want to keep
        // the previously selected additional tasks intact
        showStepContent(currentStep, true);
    });

    $('#create-campaign-step-5 .nav-right').click(function() {
        // Validate all text inputs are filled
        var hasEmptyFields = false;
        $('#campaign-detail-with-additional-tasks-input').find('input[type="text"], input[type="file"]').each(function() {
            if ($(this).attr('type') === 'text' && $(this).val().trim() === '') {
                hasEmptyFields = true;
                return false; // Break out of the loop
            }
            if ($(this).attr('type') === 'file' && $(this)[0].files.length === 0) {
                hasEmptyFields = true;
                return false; // Break out of the loop
            }
        });

        if (hasEmptyFields) {
            toastr.error("Please fill up all the fields to continue.");
            return false;
        }

        prevStep = currentStep;
        currentStep += 1;

        console.log('******** step 5: campaign info');
        console.log('currentStep: ' + currentStep);
        console.log('prevStep: ' + prevStep);
        console.log('formData: ');
        console.log(formData);

        var latestFormData = $('#form-create-campaign').serializeArray();
        $.each(latestFormData, function(i, field) {
            if (!formData[field.name]) {
                formData[field.name] = field.value;   
            }
        });

        console.log('******** step 5: adding campaign info');
        console.log('currentStep: ' + currentStep);
        console.log('prevStep: ' + prevStep);
        console.log('formData: ');
        console.log(formData);

        hideStepContent(prevStep);
        showStepContent(currentStep);
    });
</script>
@endpush
