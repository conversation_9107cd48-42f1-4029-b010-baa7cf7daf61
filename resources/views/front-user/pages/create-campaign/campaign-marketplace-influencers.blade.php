<style>
    .card-container {
        position: relative;
        background: white;
        width: 250px;
        padding: 8px;
        border-radius: 15px;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .card-container::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 35px;
        background: #AD80FF;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
        z-index: 1;
    }

    .custom-card {
        width: 100%;
        background-color: #fff;
        border-radius: 15px;
        position: relative;
        font-family: 'Outfit';
        z-index: 2;
    }

    .star-icon {
        font-size: 1.5rem;
        color: #AD80FF;
    }

    .profile-pic {
        width: 80px;
        height: 80px;
        border: 3px solid #AD80FF;
        padding: 3px;
        border-radius: 50%;
        overflow: hidden;
    }

    .profile-pic img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    h3 {
        font-size: 1em;
    }

    .view-profile {
        color: #AD80FF;
        font-size: 0.8em;
    }

    .followers {
        font-size: 1em;
        font-weight: 700;
        margin-bottom: 0.3em;
    }

    .price {
        font-size: 2em !important;
        color: #000;
        font-weight: 700;
    }

    .country {
        font-size: 1.2em;
    }

    .select-btn {
        font-size: 0.7em;
        color: #AD80FF;
        border: 1px solid #AD80FF;
        border-radius: 20px;
        transition: background-color 0.3s;
        padding: .275rem 3rem;
    }

    .select-btn:hover {
        background-color: #fff;
    }

    .influencer-detail.selected.card-container {
        background-color: #AD80FF;
    }

    .influencer-detail.selected.card-container::after {
        background-color: #fff;
    }

    .influencer-detail.selected .custom-card {
        background-color: #AD80FF;
    }

    .influencer-detail.selected .profile-pic {
        border: 3px solid white;
    }

    .influencer-detail.selected h3 {
        color: white;
    }

    .influencer-detail.selected .view-profile {
        color: white;
    }

    .influencer-detail.selected .followers {
        color: white !important;
    }

    .influencer-detail.selected .price {
        color: white !important;
    }

    .influencer-detail.selected .select-btn {
        color: black !important;
        background-color: white;
    }
</style>

@include('front-user.pages.create-campaign.influencer-marketplace-filter')

<div class="influencer-marketplace influencer-list">
    @if (isset($influencerDetails))
        @foreach ($influencerDetails as $influencerDetail)
            @php
                $req_count = App\Models\InfluencerRequestDetail::where('influencer_detail_id', $influencerDetail->i_id)
                    ->where(function ($query) {
                        $query->where('review', '=', null)->orWhere('review', '=', 0);
                    })
                    ->where('refund_reason', null)
                    ->where('finish', null)
                    ->count();
                $user = App\Models\User::where('id', $influencerDetail->user_id)->first();
            @endphp

            @if ($req_count < 5 && (isset($user) && $user->status == 1))
                @php
                    $media = \App\Models\SocialConnect::where('user_id', $influencerDetail->user_id)
                        ->where('media', $influencerDetail->media)
                        ->first();
                    $userData = \App\Models\User::where('id', $influencerDetail->user_id)->first();
                    if (
                        $userData != null &&
                        $userData->count() > 0 &&
                        ($userData->trophy != '' && $userData->trophy != null)
                    ) {
                        $trophy = $userData->trophy;
                    } else {
                        $trophy = 'Bronze';
                    }
                @endphp
                @if (isset($media->picture))
                    <div class="influencer-detail card-container text-center mx-auto mt-4" data-id="influencer_id{{ $influencerDetail->i_id }}">
                        <div class="influencer-content card custom-card text-center p-0 mx-auto my-0" style="box-shadow: none; border:none;">
                            <div class="star-icon position-absolute top-0 start-0 mx-2 my-1">
                                <img id="card_clickitfame_logo_influencer_id{{ $influencerDetail->i_id }}"
                                    src="{{ asset('/assets/front-end/images/icons/clickitfame_logo.svg') }}"
                                    width="25" height="25" alt="">
                            </div>
                            <div class="profile-pic mx-auto">
                                <img src="{{ asset('storage/' . $media->picture) }}" alt="Profile Picture" class="rounded-circle">
                            </div>
                            <h3 class="mt-3 mb-0 user_name" style="font-size: 1.3em; font-weight: 600;">{{ '@' . $influencerDetail->username }}</h3>

                            {{-- hidden is_small _business_owner check for payment details page --}}
                            <span style="display: none;" id="is_small_business_owner">{{$userData->is_small_business_owner}}</span>

                            <span href="#" data-bs-toggle="modal"
                                data-bs-target="#influncerdetailpopup{{ $influencerDetail->id }}"
                                class="view-profile text-decoration-underline d-block mb-2" style="cursor: pointer;">View
                                Profile
                            </span>
                            <p class="followers">
                                <span class="follower-count-val" data-followers="{{ $influencerDetail->followers }}">{{ $influencerDetail->followers }}</span> Followers
                            </p>
                            <p class="price fs-1 mb-0">
                                € <span class="user_price-val" data-price="{{ $influencerDetail->type_price }}">{{ number_format($influencerDetail->type_price, 2) }}</span>
                            </p>
                            <p class="mb-0">
                                <img src="{{ asset('/assets/front-end/images/icons/germany-straight-flag.svg') }}" width="20" height="20" alt="">
                                <img src="{{ asset('/assets/front-end/images/icons/trofy-' . $trophy . '.svg') }}" width="20" height="20" alt="">
                            </p>
                            <div class="mb-3">
                                <button type="button" class="select-button btn select-btn my-3">SELECT</button>
                            </div>
                        </div>

                        <!--Influncer profile popup -->
                        @include('front-user.pages.create-campaign.influencer-marketplace-profile-modal')
                    </div>
                @endif
            @endif
        @endforeach
    @endif

</div>
