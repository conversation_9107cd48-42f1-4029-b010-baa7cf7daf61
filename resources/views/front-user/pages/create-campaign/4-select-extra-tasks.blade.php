<div class="page_tab new_steps sub-step-add-extra-tasks d-none" id="create-campaign-step-4">
    <div class="mobile-step-detail">
        <div class="steps-cont">STEP 02</div>
        <div class="steps-which">Task</div>
    </div>
    <div class="task-options">
        <div class="outer-task" id="campaign-tasks">
            <img src="{{ asset('/assets/front-end/images/loading.gif') }}" alt="Loading...">
            {{-- Content will be loaded using Ajax from this template --}}
            {{-- @include('front-user.pages.create-campaign.campaign-additional-tasks-select') --}}
        </div>
    </div>
    <div class="step-nevigationbutton">
        <div class="nav-left" data-from-step="">
            <img src="{{ asset('/assets/front-end/images/icons/step-left.svg') }}" alt="">
        </div>
        <div class="nav-right">
            <img src="{{ asset('/assets/front-end/images/icons/step-right.svg') }}" alt="">
        </div>
    </div>
</div>
@push('step-scripts')
<script>
    $('#create-campaign-step-4 .nav-left').click(function() {
        prevStep = currentStep;
        currentStep -= 1;

        // formData.additional_tasks = new Array();

        // Unchecked all additional tasks radio buttons
        // $('input.task_additional[type="radio"]').prop('checked', false);

        // if (Array.isArray(formData.additional_tasks)) {
        //     formData.additional_tasks.forEach(function(val) {
        //         $('.task_additional[type="checkbox"][value="' + val + '"]').prop('checked', true);
        //     });
        // }

        hideStepContent(prevStep);
        showStepContent(currentStep);
    });

    $('#create-campaign-step-4 .task_additional[type="checkbox"]').on('change', function() {
        var val = $(this).val();
        if ($(this).is(':checked')) {
            if (!formData.additional_tasks.includes(val)) {
                formData.additional_tasks.push(val);
            }
        } else {
            formData.additional_tasks = formData.additional_tasks.filter(function(item) {
                return item !== val;
            });
        }
    });

    $('#create-campaign-step-4 .nav-right').click(function() {
        prevStep = currentStep;
        currentStep += 1;

        var additionalTasks = new Array();
        $(".task_additional:checked").each(function() {
            additionalTasks.push($(this).val());
        });
        formData.additional_tasks = additionalTasks;

        console.log('******** step 4: select additional tasks');
        console.log('currentStep: ' + currentStep);
        console.log('prevStep: ' + prevStep);
        console.log('formData: ');
        console.log(formData);

        hideStepContent(prevStep);
        showStepContent(currentStep);
    });
</script>
@endpush