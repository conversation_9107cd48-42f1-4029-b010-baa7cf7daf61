@extends('front-user.layouts.master_user_dashboard')

@section('content')
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/bs5/dt-1.13.1/datatables.min.css"/>
<section id="campaignForm">
    <div class="step_form_custom">
        <h1 class="dashHeading"><span>Your reviews</span></h1> 
        <div class="form-group"> 
            @if(count($history)>0)
                <table id="venueTable" class="table customTable" style="width:100%">
                    <thead>
                    <tr>
                        <th>Influencer Name</th> 
                        <th>Date</th>
                        <th>Campaign Id</th>
                        <th class="text-center">Rating</th> 
                        <th>Action</th> 
                    </tr>
                    </thead>
                    <tbody> 
                    @foreach ($history as $row) 
                    @if(isset($row->influencer_request_accepts->influencer_request_details))
                        <tr> 
                            <td>
                                @php 
                                    $socialLink = App\Models\SocialConnect::where('user_id',$row->influencer_request_accepts->influencer_request_details->influencerdetails->user->id)->where('media',$row->influencer_request_accepts->influencer_request_details->media)->first();
                                @endphp 
                                <a href="{{isset($socialLink->url)?$socialLink->url:''}}" target="_blank">@  {{isset($socialLink->name)?$socialLink->name:$row->influencer_request_accepts->influencer_request_details->influencerdetails->user->first_name}}</a>
                            </td>
                            <td>
                                 @php  
                                $msg_time = \DateTime::createFromFormat('Y-m-d H:i:s',$row->created_at,new DateTimeZone('UTC'));
                                $msg_time->setTimeZone(new DateTimeZone(@Session::get('timezone')?Session::get('timezone'):'UTC')); 
                                $msg_time = $msg_time->format('M d,Y H:i'); 
                                @endphp
                                {{$msg_time}} 
                            </td> 
                            <td><a class="campaignId" href="{{url('campaign-history')}}">
                            {{$row->influencer_request_accepts->influencer_request_details->compaign_id}}
                            </a></td>
                            <td class="text-center">
                                <div class="table rate"> 
                                    {{-- @for($i=5;$i>0;$i--) 
                                        <input type="radio" class="radioName" id="star{{$i}}{{$row->id}}{{$row->id}}" name="rating" value="{{$i}}" data-parsley-multiple="rating" @if($row->rating == $i) checked @endif readonly>
                                        <label for="star{{$i}}{{$row->id}}{{$row->id}}" title="text"></label>
                                    @endfor --}}

                                    @if($row->rating == 0) 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                    @endif
                                    @if($row->rating == 1) 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                    @endif
                                    @if($row->rating == 2) 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                    @endif
                                    @if($row->rating == 3) 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                    @endif
                                    @if($row->rating == 4) 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                    @endif
                                    @if($row->rating == 5)
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                    @endif
                                </div>
                            </td>  
                            <td>
                            <a class="campaignId" href="#" title="Edit"  target="popup" data-bs-toggle="modal" data-bs-target="#reviewRatingPopup{{$row->id}}"><i class="fa fa-edit fa-fw fa-lg"></i></a>

                                <!--modal reviewRatingPopup   form -->
                                <div class="modal fade ratingPopup influencer" id="reviewRatingPopup{{$row->id}}" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="reviewRatingPopupLabel" aria-hidden="true">
                                    <div class="modal-dialog modal-lg" style="max-width:800px;">
                                        <div class="modal-content">
                                            <div class="modal-body">
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                                                <div class="wizardHeading text-transform-normal">Write a Review to Influencer</div>
                                                <div class="wizardForm mt-4">
                                                    <form method="post"   action="{{ url('/update-review')}}"  data-parsley-validate>
                                                    @csrf 
                                                        <input type="hidden" name="review_id" value="{{$row->id}}"  > 
                                                        <div class="col-xl-12 col-md-12 col-12">  
                                                            <div class="form-group d-inline-block w-100">
                                                                <label>Overall rating</label>  
                                                                <div class="rate"> 
                                                                    @for($i=5;$i>0;$i--) 
                                                                    <input type="radio" class="radioName" id="star{{$i}}{{$row->id}}fi" name="rating" value="{{$i}}" data-parsley-multiple="rating" @if($row->rating == $i) checked @endif >
                                                                    <label for="star{{$i}}{{$row->id}}fi" title="text"></label>
                                                                    @endfor     
                                                                </div> 
                                                            </div> 
                                                            <div class="col-xl-12 col-md-12 col-12">
                                                                <div class="form-group"> 
                                                                    <!-- Feedback -->
                                                                    <textarea id="review" name="review">{{$row->review}}</textarea>
                                                                </div> 
                                                            </div> 
                                                        </div>
                                                        <!-- Selected users //-->
                                                        <div class="popup2btns d-flex"> 
                                                            <input type="submit" class="popupbtn bg-green color-white" name="complete" value="Update">
                                                            
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--end  modal reviewRatingPopup   form --> 
                            </td>
                        </tr> 

                    @endif    
                    @endforeach 
                    </tbody> 
                </table>
            @else
                <div class="campningDiv text-center">No data found!</div>
            @endif
        </div>
    </div>
</section>

@endsection

@section('script_links')
<script type="text/javascript" src="https://cdn.datatables.net/v/bs5/dt-1.13.1/datatables.min.js"></script>

@endsection

@section('script_codes')
<script>
  $(document).ready(function () {
    $('#venueTable').DataTable();
  });
</script>
@endsection