@extends('front-user.layouts.master_user_dashboard')

@section('content')
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/bs5/dt-1.13.1/datatables.min.css"/>
<section id="campaignForm">
    <div class="step_form_custom">
        <h1 class="dashHeading"><span>Reviews and Ratings</span></h1>
        <div class="form-group"> 
            @if(count($history)>0)
                <table id="venueTable" class="table customTable" style="width:100%">
                    <thead>
                    <tr>
                        <th>Brand Name</th>  
                        <th>Date</th>
                        <th>Campaign Id</th>
                        <th class="text-center">Rating</th> 
                        <th>Action</th> 
                    </tr>
                    </thead>
                    <tbody> 
                    @foreach ($history as $row) 
                        <tr> 
                            <td>{{$row->user->first_name}} {{$row->user->last_name}}</td>
                            <td>
                                
                                 @php  
                                $msg_time = \DateTime::createFromFormat('Y-m-d H:i:s',$row->created_at,new DateTimeZone('UTC'));
                                $msg_time->setTimeZone(new DateTimeZone(@Session::get('timezone')?Session::get('timezone'):'UTC')); 
                                $msg_time = $msg_time->format('M d,Y H:i'); 
                                @endphp
                                {{$msg_time}} 
                            </td> 
                            <td><a class="campaignId" href="{{url('campaign-history')}}">
                            {{$row->influencer_request_accepts->influencer_request_details->compaign_id}}
                            </a></td>
                            <td class="text-center">
                                <div class="table rate"> 
                                    @if($row->rating == 0) 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                    @endif
                                    @if($row->rating == 1) 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                    @endif
                                    @if($row->rating == 2) 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                    @endif
                                    @if($row->rating == 3) 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                    @endif
                                    @if($row->rating == 4) 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-blank.png" alt=""> 
                                    @endif
                                    @if($row->rating == 5)
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                        <img src="{{ asset('/') }}/assets/front-end/images/star-fill.png" alt=""> 
                                    @endif
                                    {{-- @for($i=5;$i>0;$i--) 
                                        <input type="radio" class="radioName" id="star{{$i}}-{{$row->influencer_request_accepts->influencer_request_details->compaign_id}}" name="rating" value="{{$i}}" data-parsley-multiple="rating" @if($row->rating == $i) checked @endif  readonly>
                                        <label for="star{{$i}}-{{$row->influencer_request_accepts->influencer_request_details->compaign_id}}" title="text"></label>
                                    @endfor   --}}
                                </div> 
                            </td>  
                            <td>
                            <a href="#" title="Edit" class="reviewEdit" target="popup" data-bs-toggle="modal" data-bs-target="#reviewRatingPopup{{$row->id}}"><i class="fa fa-eye fa-fw fa-lg"></i></a> 

                            <!--modal reviewRatingPopup   form -->
                            <div class="modal fade influencer ratingPopup" id="reviewRatingPopup{{$row->id}}" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="reviewRatingPopupLabel" aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-body">
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                                            <div class="wizardHeading"> Review Details</div>
                                            <div class="wizardForm mt-4">  
                                                <form method="post"   action="{{ url('/update-review')}}"  data-parsley-validate>
                                                @csrf 
                                                    <input type="hidden" name="review_id" value="{{$row->id}}"  > 
                                                    <div class="col-xl-12 col-md-12 col-12">  
                                                        <div class="form-group d-inline-block w-100">
                                                            <label>Overall rating</label>
                                                            <div class="rate infl"> 
                                                                @for($i=5;$i>0;$i--) 
                                                                    <input type="radio" class="radioName" id="star{{$i}}-dinc-1" name="rating" value="{{$i}}" data-parsley-multiple="rating" @if($row->rating == $i) checked @endif disabled readonly>
                                                                    <label for="star{{$i}}-dinc-1" title="text"></label>
                                                                @endfor     
                                                            </div> 
                                                        </div> 
                                                        <div class="col-xl-12 col-md-12 col-12">
                                                            <div class="form-group"> 
                                                                <!-- Feedback -->
                                                                <textarea id="review" placeholder="Type your review here..." name="review" readonly>{{$row->review}}</textarea>
                                                            </div> 
                                                        </div> 
                                                    </div>
                                                    <!--  
                                                    <div class="popup2btns d-flex"> 
                                                        <input type="submit" name="complete"   value="Update"   >
                                                        
                                                    </div> -->
                                                    <div class="text-center button-text">
                                                        <p>Do you want to dispute this review?</p>
                                                        @if($row->is_dispute == 0)   
                                                        <a href="#" class="light-red-btn support-btn" data-bs-target="#contact-support{{$row->id}}" data-bs-toggle="modal" data-bs-dismiss="modal">Contact support</a> 
                                                        @else
                                                        <a href="#" style="pointer-events:none" >You have contacted support for dispute this review</a> 
                                                        @endif
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--end  modal reviewRatingPopup   form --> 
                            <!--start modal for request Submit -->
                            <div class="modal fade confirm-content influencer" id="contact-support{{$row->id}}" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="requestSubmit{{$row->id}}Label" aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-body">
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button>
                                            <div class="wizardHeading">Contact support</div>
                                            <div class="contact-support">   
                                                <div class="text-center wizardHeading-subheading">Please explain why this rating is not appropriate</div>
                                                <form action="{{url('/review-dispute')}}" method="Post" enctype="multipart/form-data" data-parsley-validate>
                                                        @csrf
                                                        <input type="hidden" name="review_id" value="{{$row->id}}"> 
                                                        <input type="hidden" name="page" value="review"> 

                                                        <div class="form-group">
                                                        <label for="" class="form-label"></label>
                                                        <textarea class="form-control" name="comment" id="comment" rows="3" placeholder="Please describe your problem" required data-parsley-required-message="Please enter problem."></textarea>
                                                        </div>
                                                        <div class="form-group uploadFile">
                                                            <div class="custom-file-picker"> 
                                                                <div class="picture-container form-group">
                                                                    <div class="picture">
                                                                        <span class="icon" id="icon">
                                                                            <div class="smaltext">Browse</div>
                                                                            <div class="bigtext">Or Drag and Drop to Upload</div>
                                                                        </span>
                                                                        <input type="file" class="wizard-file" id="supportFiles" name="file">
                                                                        <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 37 37" xml:space="preserve">
                                                                            <path class="circ path" style="fill:none;stroke:#77d27b;stroke-width:3;stroke-linejoin:round;stroke-miterlimit:10;" d="M30.5,6.5L30.5,6.5c6.6,6.6,6.6,17.4,0,24l0,0c-6.6,6.6-17.4,6.6-24,0l0,0c-6.6-6.6-6.6-17.4,0-24l0,0C13.1-0.2,23.9-0.2,30.5,6.5z"></path>
                                                                            <polyline class="tick path" style="fill:none;stroke:#77d27b;stroke-width:3;stroke-linejoin:round;stroke-miterlimit:10;" points="11.6,20 15.9,24.2 26.4,13.8 "></polyline>
                                                                        </svg>
                                                                        <div class="popover-container text-center show-file">
                                                                            <p data-toggle="popover" data-id="a8755cf0-f4d1-6376-ee21-a6defd1e7c08" class="btn-popover" data-original-title="" title="">
                                                                                <span class="file-total-viewer">0</span> Files Selected <br/><input type="button" value="view" href="javascript:void(0)" class="btn btn-success btn-xs btn-file-view">
                                                                            </p>
                                                                        </div>
                                                                    </div>     
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="extra-time-content-bs">
                                                            <input type="submit" class="et-submit ds" name="confirm" value="Submit">
                                                        </div>
                                                    </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--end  modal for request time -->
                            </td>
                        </tr> 
                    @endforeach 
                    </tbody> 
                </table>
            @else
                <div class="campningDiv text-center">No data found!</div>
            @endif
        </div>
    </div>
    <div class="modal fade complaint-confirm-popup influencer" id="thankYouContact" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="reviewRatingPopupLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                <a href="{{url('reviews-influencer')}}"> <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt=""></button></a>
                    
                    <div class="complaint-confirm text-center">
                        <img src="{{ asset('/') }}/assets/front-end/images/icons/close-one.svg" alt="" class="complaint-confirm-image">
                        <p class="thank-title1">
                            Thank you for contacting us!
                        </p>
                        <p class="thank-title2">
                            We will be in touch with you soon
                        </p>
                        <a href="{{url('reviews-influencer')}}" class="et-submit mx-4 complant-btn" aria-label="Close">Confirm</a>
                    </div>

                </div>
            </div>
        </div>
    </div>
</section>

@endsection

@section('script_links')
<script type="text/javascript" src="https://cdn.datatables.net/v/bs5/dt-1.13.1/datatables.min.js"></script>

@endsection

@section('script_codes')
<script>
    $(document).ready(function () {
        var review = @json($review);
        if(review == 1)
        {
            console.log(review)
            $('#thankYouContact').modal('show');
        }
    });

    $(document).ready(function () {
        $('#venueTable').DataTable();
    });
</script>
@endsection