@if (isset($socialPost))
    <div class="form-border">
        <style>
            .socialComp {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 16px;
                margin-top: 30px!important;
            }

            .socialComp > div {
                flex: 1 1 0;
                text-align: center;
            }
        </style>
        <div class="row">
            <div class="col-xl-12 col-md-12 col-12">
                <div class="form-group">
                    <div class="imageNvideo">
                        @if (str_contains($socialPost->link, 'http'))
                            <a class="view-link" href="{{ $socialPost->link }}" target="_blank">View</a>
                        @else
                            @if ($socialPost->type == 'photo')
                                <a href="{{ asset('storage/' . $socialPost->link) }}" target="_blank">
                                    <img src="{{ asset('storage/' . $socialPost->link) }}" alt="Social post photo" style="max-width:100%; height:auto;">
                                </a>
                            @elseif($socialPost->type == 'video')
                                <video controls style="max-width:100%; height:auto;">
                                    <source src="{{ asset('storage/' . $socialPost->link) }}" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
            <div class="col-xl-12 col-md-12 col-12">
                <div class="ssn">
                    <div class="socialdetail">
                        <div class="socialimage">
                            <img src="{{ asset('assets/front-end/images/icons/campaigns-' . $socialPost->media . '.svg') }}" alt="{{ $socialPost->media }} icon">
                        </div>
                        <div class="socialcontent">
                            <div class="socialcontent-posttype">
                                <span class="m-0">Share Content from Brand - Post/Story</span>
                            </div>
                            <div class="socialcontent-postlink">
                                @if (str_contains($socialPost->link, 'http'))
                                    <a class="view-link" style="width:auto;" href="{{ $socialPost->link }}" target="_blank">{{ $socialPost->link }}</a>
                                @else
                                    <a class="view-link" style="width:auto;" href="{{ $socialPost->thumbnail }}" target="_blank">{{ $socialPost->thumbnail }}</a>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="socialComp" style="margin-right: 0!important;">
                        @if (in_array('like', $metricsToShow) || in_array('likes', $metricsToShow))
                            <div id="likediv"
                                data-toggle="tooltip"
                                title=""
                                style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                                <i class="fas fa-thumbs-up" aria-hidden="true"></i>
                                <span id="like">{{ $socialPost->like ?? 0 }}</span>
                            </div>
                        @endif
                        @if (in_array('share', $metricsToShow) || in_array('shares', $metricsToShow))
                            <div id="sharediv"
                                data-toggle="tooltip"
                                title="Total number of shares."
                                style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                                <i class="fas fa-share-alt-square" aria-hidden="true"></i>
                                <span id="share">{{ $socialPost->share ?? 0 }}</span>
                            </div>
                        @endif
                        @if (in_array('view', $metricsToShow) || in_array('views', $metricsToShow))
                            <div id="viewdiv"
                                data-toggle="tooltip"
                                title="Total number of times the content has been seen."
                                style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                                <i class="fas fa-eye" aria-hidden="true"></i>
                                <span id="view">{{ $socialPost->view ?? 0 }}</span>
                            </div>
                        @endif
                        @if (in_array('comment', $metricsToShow) || in_array('comments', $metricsToShow))
                            <div id="commentdiv"
                                data-toggle="tooltip"
                                title="Total number of comments."
                                style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                                <i class="fas fa-comments" aria-hidden="true"></i>
                                <span id="commentSet">{{ $socialPost->comment ?? 0 }}</span>
                            </div>
                        @endif
                        @if (in_array('favorite', $metricsToShow) || in_array('favorites', $metricsToShow))
                            <div><i class="fas fa-heart" aria-hidden="true"></i>{{ $socialPost->favorite ?? 0 }}</div>
                        @endif
                        @if (in_array('friend', $metricsToShow) || in_array('friends', $metricsToShow))
                            {{-- TODO Do we still need this metrics section? --}}
                            <div id="frienddiv"
                                data-toggle="tooltip"
                                title="Number of unique Instagram users that have seen the content at least once;"
                                style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                                <i class="fas fa-users" aria-hidden="true"></i>
                                <span id="friend">{{ $socialPost->friend ?? 0 }}</span>
                            </div>
                        @endif
                        @if (in_array('reach', $metricsToShow) || in_array('reaches', $metricsToShow))
                            <div id="reachdiv"
                                data-toggle="tooltip"
                                title="Number of unique Instagram users that have seen the content at least once;"
                                style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                                <i class="fas fa-users" aria-hidden="true"></i>
                                <span id="reach">{{ $socialPost->reach ?? 0 }}</span>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
