@extends('front-user.layouts.master_user_dashboard')

@section('content')
    <form data-parsley-validate id="form-create-campaign" enctype="multipart/form-data" method="post" >
        @csrf
        <div class="marketplace-imports campaign-type d-block">
            @include('front-user.pages.create-campaign-step-0-1-campaign-type')
        </div>
        <div class="marketplace-imports boost-me d-none">
            @include('front-user.pages.create-campaign-step-0-2-boost-me-post-type-content')
        </div>
        <div class="marketplace-imports shoutout d-none">
            @include('front-user.pages.create-campaign-step-0-3-enter-all-campaign-details')
        </div>
    </form>
@endsection

@section('script_links')
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.css" rel="stylesheet" type="text/css" />
@endsection

@section('script_codes')
<script>
    // Step tracking variables
    var preliminary_step_number = 0; // For first-level steps (0: campaign type, 1: boost type)
    console.log('preliminary_step_number:', preliminary_step_number);

    var step_number = 0; // For second-level steps (0-4: social media, tasks, details, marketplace, payment)
    console.log('step_number:', step_number);

    // TODO this variable globalStepNumberTracker should be used,
    // step_number will be deprecated and removed in the future version
    var globalStepNumberTracker = step_number;
    console.log('globalStepNumberTracker:', globalStepNumberTracker);

    // Initialize first-level steps (main campaign type selection)
    var mp_step = $('.steps-section .steps-point')
    console.log('mp_step:', mp_step);

    var iEra, thumbLi = $('.steps-section .steps-point');
    console.log('iEra:', iEra);
    console.log('thumbLi:', thumbLi);

    for (iEra = 0; iEra < mp_step.length; iEra++) {
        console.log('iEra (in loop):', iEra);
        thumbLi.eq(iEra).attr("id", 'steps-point' + iEra);
    }

    // Initialize second-level steps (detailed multi-step process)
    var new_steps = $('.this-steps .new_steps')
    console.log('new_steps:', new_steps);

    var new_step_li, new_step_thumbLi = $('.this-steps .new_steps');
    console.log('new_step_li:', new_step_li);
    console.log('new_step_thumbLi:', new_step_thumbLi);
    
    
    
    
    for (new_step_li = 0; new_step_li < new_steps.length; new_step_li++) {
        new_step_thumbLi.eq(new_step_li).attr("id", 'new_steps' + new_step_li);
        new_step_thumbLi.eq(new_step_li).find(".mobile-step-detail .steps-cont").text("STEP 0"+ (new_step_li + 1));

        // Hide all second-level steps initially except the first one
        if (new_step_li > 0) {
            new_step_thumbLi.eq(new_step_li).hide();
        }
    }

    // Hide all second-level steps initially
    $('.this-steps .new_steps').hide();

    // Handle campaign type selection with validation
    $('input[name="type_post_content"]').click(function() {
        var selectedValue = $(this).val();
        console.log('Campaign type selected:', selectedValue, 'preliminary_step_number:', preliminary_step_number);

        // Hide alert message
        $(".alert-select-option").addClass("d-none");

        // Hide current step
        $(this).closest(".marketplace-imports").removeClass("d-block").addClass("d-none");

        if(selectedValue == "Boost me"){
            preliminary_step_number = 1; // Move to boost type selection step
            $('.nav-left').attr("data-from-step", "boost-me");
            $(this).closest("form").find(".boost-me").removeClass("d-none").addClass("d-block");
        }else if(selectedValue == "Reaction video"){
            preliminary_step_number = 2; // Skip boost type, go directly to second-level steps
            globalStepNumberTracker = 0; // Start at first second-level step
            $('.nav-left').attr("data-from-step", "Reaction video");
            $(this).closest("form").find(".shoutout").removeClass("d-none").addClass("d-block");

            // Reset radio button states when entering from campaign type selection
            setTimeout(function() {
                if (typeof resetSocialMediaStep === 'function') {
                    resetSocialMediaStep();
                }
                if (typeof showPostTypeOptions === 'function') {
                    showPostTypeOptions();
                }
            }, 100);

            $(".get_type").hide();
            $(".get_type.reaction-video").show();
            $("#steps-point0").addClass("inprogress");
            $("#new_steps0").show();
        }else if(selectedValue == "Survey"){
            preliminary_step_number = 2; // Skip boost type, go directly to second-level steps
            globalStepNumberTracker = 0; // Start at first second-level step
            $('.nav-left').attr("data-from-step", "Survey");
            $(this).closest("form").find(".shoutout").removeClass("d-none").addClass("d-block");

            // Reset radio button states when entering from campaign type selection
            setTimeout(function() {
                if (typeof resetSocialMediaStep === 'function') {
                    resetSocialMediaStep();
                }
                if (typeof showPostTypeOptions === 'function') {
                    showPostTypeOptions();
                }
            }, 100);

            $(".get_type").hide();
            $(".get_type.survey").show();
            $("#steps-point0").addClass("inprogress");
            $("#new_steps0").show();
        }
    })

    // Add validation function for campaign type selection
    function validateCampaignTypeSelection() {
        if (!$('input[name="type_post_content"]:checked').length) {
            $(".alert-select-option").removeClass("d-none");
            return false;
        }
        return true;
    }
</script>
@endsection

<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.css" rel="stylesheet" type="text/css" />

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

@section('script_links')
    <script>
        window.Laravel = {
            urls: {
                getTasks: "{{ url('get-tasks') }}",
                getTasksInput: "{{ url('get-tasks-input') }}",
                getInfluencers: "{{ url('get-influencers') }}",
                marketStepFilter: "{{ url('market-step-filter') }}",
            },
            assets: {
                icons: {
                    clickitfameWhite: "{{ asset('/assets/front-end/images/icons/clickitfame_white.svg') }}",
                    clickitfameLogo: "{{ asset('/assets/front-end/images/icons/clickitfame_logo.svg') }}",
                    deleteIcon: "{{ asset('/assets/front-end/images/icons/icon-delete.svg') }}",
                    deleteNew: "{{ asset('/assets/front-end/images/new/delete.svg') }}"
                }
            },
            csrfToken: "{{ csrf_token() }}",
            user: @json(auth()->user())
        };
    </script>
    <script src="{{ asset('js/create-campaign.js') }}"></script>
@endsection
