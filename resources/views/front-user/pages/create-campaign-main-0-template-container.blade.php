@extends('front-user.layouts.master_user_dashboard')

@section('content')
    <form data-parsley-validate id="form-create-campaign" enctype="multipart/form-data" method="post" >
        @csrf
        <div class="marketplace-imports campaign-type d-block">
            @include('front-user.pages.create-campaign-step-0-1-campaign-type')
        </div>
        <div class="marketplace-imports boost-me d-none">
            @include('front-user.pages.create-campaign-step-0-2-boost-me-post-type-content')
        </div>
        <div class="marketplace-imports shoutout d-none">
            @include('front-user.pages.create-campaign-step-0-3-enter-all-campaign-details')
        </div>
    </form>
@endsection

@section('script_links')
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.css" rel="stylesheet" type="text/css" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    window.Laravel = {
        urls: {
            getTasks: "{{ url('get-tasks') }}",
            getTasksInput: "{{ url('get-tasks-input') }}",
            getInfluencers: "{{ url('get-influencers') }}",
            marketStepFilter: "{{ url('market-step-filter') }}",
        },
        assets: {
            icons: {
                clickitfameWhite: "{{ asset('/assets/front-end/images/icons/clickitfame_white.svg') }}",
                clickitfameLogo: "{{ asset('/assets/front-end/images/icons/clickitfame_logo.svg') }}",
                deleteIcon: "{{ asset('/assets/front-end/images/icons/icon-delete.svg') }}",
                deleteNew: "{{ asset('/assets/front-end/images/new/delete.svg') }}"
            }
        },
        csrfToken: "{{ csrf_token() }}",
        user: @json(auth()->user())
    };
</script>
<script src="{{ asset('js/create-campaign.js') }}"></script>
@endsection

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

