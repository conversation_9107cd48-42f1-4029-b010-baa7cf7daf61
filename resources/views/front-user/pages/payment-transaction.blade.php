@extends('front-user.layouts.master_user_dashboard')
@section('content')
<style>
  @media screen and (max-width: 1000px) and (min-width: 700px) {
    .col-md-6 {
        width: 100% !important;
    }
}
    a:hover {
        color: white !important;
    }
</style>
<section id="campaignForm">
    <div class="step_form_custom">
        <div class="customerSer">
            <h1 class="dashHeading"><span>CASH OUT</span></h1>
            <div class="paymentPage d-flex justify-content-between">
                <div class="paymentPagenumber">
                    <img src="{{ asset('/assets/front-end/images/icons/payment-icon-1.png') }}" alt="">
                    <h2>In progress</h2>
                    <div class="ortContent">You currently have orders not finished with the worth of:</div>
                    <span class="paymentLikeBtn">{{ number_format($inprogressAmount, 2) }} €</span>
                </div>
                <div class="paymentPagenumber">
                    <img src="{{ asset('/assets/front-end/images/icons/payment-icon-2.png') }}" alt="">
                    <h2>Pending</h2>
                    <div class="ortContent">Your money will be released after 14 days</div>
                    <span class="paymentLikeBtn">{{ number_format($pendingAmount, 2) }} €</span>
                </div>
            </div>
            <div class="paymentPage d-flex justify-content-center">
                <div class="paymentPagenumber greenBox">
                    <img src="{{ asset('/assets/front-end/images/icons/payment-icon-3.png') }}" alt="">
                    <h2>CONGRATULATIONS</h2>
                    <div class="ortContent">You can get paid currently for</div>
                    <span class="paymentLikeBtn color-green">{{ ($availableAmount > 0) ? number_format($availableAmount, 2) : "0.00" }} €</span>
                    @if($stripeAccount)
                    <div class="row">
                        @if($availableAmount > 0)
                        <div class="col-md-6 col-sm-12">
                            <button class="btnpor bg-green color-white" type="button" data-bs-toggle="modal" data-bs-target="#payoutpopup">Cash out</button>
                        </div>
                        @endif
                        <div class="col-md-6 col-sm-12">
                            <a class="profile-connect-btn" target="_blank" href="{{ url('stripe/go/to/dashboard') }}" style="margin-top: 18px; width: 220px;">
                                <img src="{{ asset('/assets/front-end/images/icons/icon-stripe-dashboard.svg') }}" style="position: static;"> Stripe Dashboard
                            </a>
                            <span class="accnumberoo" style="display: block; margin-top: -16px;margin-left:13px !important;">{{ (isset($stripeAccount->email) && $stripeAccount->email != '') ? $stripeAccount->email : $stripeAccount->stripe_user_id }}</summary>
                        </div>
                    </div>
                        @else
                        <div class="mt-3 mb-0">Please connect Stripe account to get paid.</div>
                    @endif

                    <!--modal paidPayment form -->
                    <div class="modal fade influencer" id="paidPayment" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="paidPaymentLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-body">
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" alt=""></button>
                                    <div class="wizardHeading">Get paid to account</div>
                                    <div class="wizardForm">
                                        <form method="post"   action="{{ url('/get-paid-payment')}}"  data-parsley-validate>
                                        @csrf
                                            <input type="number" name="payment" value="{{$availableAmount}}" max="{{$availableAmount}}">
                                            <input type="submit" name="confirm"   value="Confirm">
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end  modal  paidPayment form -->

                    <!--modal paidPayment form -->
                    <div class="modal fade influencer" id="payoutpopup" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="payoutpopupLabel" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered modal-lg">
                            <div class="modal-content">
                                <div class="modal-body">
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" alt=""></button>
                                    <div class="wizardHeadingOrt"><img src="{{ asset('/assets/front-end/images/icons/payment-icon-3.png') }}" alt=""> Cash out</div>
                                    <form id="paymentForm" method="post"   action="{{ url('/get-paid-payment')}}"  data-parsley-validate>
                                    @csrf
                                        <input type="hidden" name="payment" value="{{ $availableAmount }}" max="{{ $availableAmount }}">
                                        <div class="text-center greenBox">
                                            <h2>CONGRATULATIONS</h2>
                                            <div class="ortContent">The following amount will be paid to you:</div>
                                            <span class="paymentLikeBtn color-green">{{ ($availableAmount > 0) ? number_format($availableAmount, 2) : 0 }} €</span>
                                            <div class="ortContent">Please note that a Cash out can take up to 3 business days. The Cash out will be made to the account you have connected.</div>
                                            <button class="btnpor bg-green color-white mb-5" type="submit" data-bs-dismiss="modal" id="confirmPayment"  >Cash out</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end  modal  paidPayment form -->

                    <!--modal payment confirm form -->
                    <div class="modal fade influencer" id="payoutconfirmpopup" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="payoutconfirmpopupLabel" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered modal-lg">
                            <div class="modal-content">
                                <div class="modal-body">
                                    <div class="text-center greenBox">
                                        <div class="text-center chkImage"><img src="{{ asset('/assets/front-end/images/icons/icon-green-border-check.svg') }}" alt=""></div>
                                        <div class="ortContentntr text-uppercase">Your Cashout is on the way!</div>
                                        <button class="btnpor bg-green color-white mb-5 confirmbutton" type="button" data-bs-dismiss="modal">Confirm</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end  modal  payment confirm form -->
                    <!--modal payment confirm form -->
                    <div class="modal fade influencer" id="payouterrorpopup" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="payoutconfirmpopupLabel" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered modal-lg">
                            <div class="modal-content">
                                <div class="modal-body">
                                    <div class="text-center errorBox">
                                        <div class="text-center chkImage"><img src="{{ asset('/assets/front-end/images/icons/icon-high-demand.svg') }}" alt=""></div>
                                        <div class="ortContentntr" id="errorText">Something went wrong!</div>
                                        <button class="btnpor bg-error color-white mb-5" type="button" data-bs-dismiss="modal">Ok</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end  modal  payment confirm form -->
                </div>
            </div>
        </div>
    </div>
</section>
<div class="loaderss" id="pageLoader">
    <img src="{{ asset('/assets/front-end/images/loading-loading-forever.gif') }}" alt="">
</div>

@endsection

@section('script_links')

@endsection

@section('script_codes')
<script type="text/javascript">
    var formData = null;
    formData = $('#paymentForm').serialize();
    $('#confirmPaymentOld').click(function(){
        $("#pageLoader").show()
        $.ajax({
            url: "{{ url('/get-paid-payment')}}",
            type: "post",
            dataType: 'json',
            data: formData,
        }).done(function (data) {
            $("#pageLoader").hide()
            if(data.code == '200'){
                $('#payoutconfirmpopup').modal('show');
            }else{
                $('#errorText').text(data.msg)
                $('#payouterrorpopup').modal('show');
            }
        });
    });


    $('.confirmPaymentOld').click(function(){
        location.reload();
    });

</script>
@endsection
