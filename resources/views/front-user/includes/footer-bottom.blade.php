</div>


@php $dialogues = DB::table('dialogues')->where('name', 'popuprequir')->first(); @endphp

<!-- Modal -->
<div class="modal fade influencer" id="popuprequir" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="popuprequirLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <div class="wizardHeading">{{$dialogues->heading}}</div>
                <div class="wizardForm">
                   {!!$dialogues->content!!}
                    <div class="text-center">
                        <img src="{{ asset('/') }}/assets/front-end/images/inahh.jpg" class="img-fluid img-thumbnail">
                    </div>

                    <a href="{{url('my-profile')}}" class="et-submit mx-3 confirm-link">
                        Confirm
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@php $dialogues = DB::table('dialogues')->where('name', 'verifyModal')->first(); @endphp
<!-- Modal -->
<div class="modal fade influencer" id="verifyModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="verifyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body pt-4 px-5 d-flex flex-column justify-content-center align-items-center">
                <div class="wizardTitle text-center">{{$dialogues->heading}}</div>
                <div class="wizardForm">
                    <div class="popup2btns d-flex">
                        <a href="{{url('verify-email')}}" class="et-submit mx-3 confirm-link">Confirm</a>
                        <a href="#" class="et-submit red-btn mx-3 confirm-link cancleBtn" data-bs-dismiss="modal" aria-label="Close">Cancel</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@php $dialogues = DB::table('dialogues')->where('name', 'publishModal')->first(); @endphp
<!-- Modal -->
<div class="modal fade influencer" id="publishModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="publishModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body pt-4 px-5 d-flex flex-column justify-content-center align-items-center">
                <div class="text-center injj">
                    <i class="fa-solid fa-triangle-exclamation"></i>
                </div>
                <div class="wizardTitle">{{$dialogues->heading}}</div>
                <div class="wizardForm">
                    <div class="popup2btns d-flex">
                    <a href="#" class="et-submit mx-3 confirm-link" data-bs-dismiss="modal" aria-label="Close">Ok</a>
                    <a href="#" class="et-submit red-btn mx-3 confirm-link cancleBtn" data-bs-dismiss="modal" aria-label="Close">Cancel</a>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Modal -->
{{-- <div class="modal fade influencer" id="confirmreset" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <div class="wizardHeading">Error</div>
                <span id="errorTextModal"></span>

                <div class="wizardForm" >

                    <button type="button" class="et-submit mx-3 confirm-link" data-bs-dismiss="modal" aria-label="Close">Ok</button>
                </div>
            </div>
        </div>
    </div>
</div> --}}

<!--social connect error popup Start -->
<div class="modal fade influencer" id="confirmreset" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="payoutconfirmpopupLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <div class="text-center errorBox">
                    <div class="text-center chkImage"><img src="{{ asset('/') }}/assets/front-end/images/icons/icon-no-data-image.png" alt=""></div>
                    <div class="manreq-title">Error - Oh Nooo..</div>
                    <div class="manreq-text"><span id="errorTextModal"></span></div>
                    <button class="btnpor bg-error color-white mb-5" type="button" data-bs-dismiss="modal">Ok</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!--social connect error popup end-->





@php $dialogues = DB::table('dialogues')->where('name', 'draftmode')->first(); @endphp
<!-- Modal -->
<div class="modal fade influencer" id="draftmode" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="draftmodeLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <div class="wizardHeading">{{$dialogues->heading}}</div>
                <div class="wizardForm">
                    {!!$dialogues->content!!}
                    <input type="hidden" name="draftcount" id="draftcount">
                    <div class="popup2btns d-flex">
                        <a href="javascript:void(0)" class="confirm-link draftmode"  onclick="saveDraftForm()">Ok</a>
                        <a href="#" class="et-submit red-btn mx-3 confirm-link cancleBtn" data-bs-dismiss="modal" aria-label="Close">Cancel</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@php $requests = DB::table('dialogues')->where('name', 'requests')->first(); @endphp
<!-- Modal -->
<div class="modal fade influencer" id="requestModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="requestModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body pt-4 px-5 d-flex flex-column justify-content-center align-items-center">
                <div class="text-center injj">
                    <i class="fa-solid fa-triangle-exclamation"></i>
                </div>
                <div class="wizardForm">
                    {!!$requests->content!!}
                    <button type="button" class="et-submit mx-3 confirm-link" data-bs-dismiss="modal" aria-label="Close">Ok</button>
                </div>
            </div>
        </div>
    </div>
</div>




<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.20.1/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.14/moment-timezone-with-data-2012-2022.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js" integrity="sha512-uto9mlQzrs59VwILcLiRYeLKPPbS/bT71da/OEBYEwcdNUk8jYIy+D176RYoop1Da+f9mvkYrmj5MCLZWEtQuA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="{{ asset('assets/front-end/js/jquery.simpleLoadMore.js') }}"></script>
<script>
    // Auto width input script start
    $.fn.textWidth = function(text, font) {
        if (!$.fn.textWidth.fakeEl) {
            $.fn.textWidth.fakeEl = $('<span>').hide().appendTo(document.body);
        }

        $.fn.textWidth.fakeEl.text(text || this.val() || this.text() || this.attr('placeholder')).css('font', font || this.css('font'));
        return $.fn.textWidth.fakeEl.width();
    };

    $('.in-value .width-dynamic').on('input', function() {
        var inputWidth = $(this).textWidth();
        $(this).css({
            width: inputWidth
        })
    }).trigger('input');

    $( document ).ready(function() {
        $('.timezone').val(moment.tz.guess());
        $.ajax({
            url: "{{url('set-session')}}",
            data: {param:'timezone',value:moment.tz.guess()}
        });
    });
</script>

<script type="text/javascript">
    jQuery(window).scroll(function () {
        if (jQuery(window).scrollTop() >= 100) {
            jQuery('.site-header').addClass('fixed-header');
        } else {
            jQuery('.site-header').removeClass('fixed-header');
        }
    });
</script>


<script type="text/javascript" src="{{ URL::asset('/assets/front-end/js/jquery.mCustomScrollbar.js') }}"></script>
<script src="{{ URL::asset('/assets/admin/plugins/sweetalert/sweetalert.min.js') }}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datepicker/0.6.5/datepicker.min.js"></script>
<script type="text/javascript">
	var oneYearFromNow = new Date();
	oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() - 18);
	$('#dob').datepicker({
		endDate: oneYearFromNow
	});
</script>

<script>
    $(window).scroll(function() {
        var scroll = $(window).scrollTop();

        if (scroll >= 500) {
            $(".site-header").addClass("sticky");
        } else {
            $(".site-header").removeClass("sticky");
        }
    });

    $(document).ready(function() {
        $('.select').select2();
    });

if($('input[type="text"]:enabled').filter(function(){return $(this).val()=="";})){
    $("input").addClass("ds");
};


$(window).on("load", function(){

    $(".dashboardsidebar ul.main-menu").mCustomScrollbar({
        theme:"minimal"
    });

});
function saveDraftForm(){
    // e.preventDefault();
    var count = $("#draftcount").val();
     // $("#publishButton").val('Publish');
    console.log(count);
    $('#draft'+count).submit();

}
</script>


<script type="text/javascript">

// Step form Script
$(document).ready(function(){
    $(document).on("click",".next",function(event) {
        $.ajax({
            url: "{{url('latest-social-connect-data')}}",
        })
        .done(function(data) {
            $(".connectPublish").html(data);
        });
    });
});

$('.publish').click(function() {
    if (parseInt($('#infoFieldReq').html()) > 0) {
        $('#popuprequir').modal('show');
    } else {
        if ($('#verified').val() != '') {
            $('#draft5').submit();
        }
    }
});

$(document).ready(function() {
    function runTabs() {
        if (document.getElementById("step_form_link")) {
            const collection = document.getElementById("step_form_link").children;
            for (let i = 0; i < collection.length; i++) {
                collection[i].setAttribute("data-title","collection"+i);
            }
        }

        if (document.querySelector(".step_form")) {
            const tab_data = document.querySelector(".step_form").children;
            for (let i = 0; i < tab_data.length; i++) {
                tab_data[i].setAttribute("data-content","collection"+i )
            }
        }
    }

    runTabs();
})

$(document).on("click", ".loaderShow", function() {
    $("#pageLoader").show();
});

$('.influncerList').on('shown.bs.modal', function (e) {
    hide = true;
    $('body').on('click', '.influncerlist-dropdown button.accordion-button', function () {

        var self = $(this);

        if (self.closest(".accordion-item").hasClass('open')) {
            $('.influncerlist-dropdown button.accordion-button').closest(".accordion-item").removeClass('open');
            return false;
        }

        $('.influncerlist-dropdown button.accordion-button').closest(".accordion-item").removeClass('open');

        self.closest(".accordion-item").toggleClass('open');
        hide = false;
    });

    $(".delete-inf").click(function(){
        $(this).closest(".dr-row").remove();
    });
});
$(document).on("click",".showLoader",function(){
    $("#pageLoader").show();
});

$(document).on("change", 'input.wizard-file[type=file]', function(){
    var filename = $('input.wizard-file[type=file]').val().replace(/C:\\fakepath\\/i, '')
    $(this).prev("#icon").text(filename)
});

$(document).on("click", ".open-extra", function(){
    $(".mobile-menu").toggleClass("open-menu");
})

$(document).on("click", ".menu-overlay", function(){
    $(".mobile-menu").removeClass("open-menu");
    $(".havesubmenu").removeClass("toggleMenu");
});

$('.copy_text').click(function(e) {
    e.preventDefault();

    var copyText = $(this).attr('href');
    document.addEventListener('copy', function(e) {
        e.clipboardData.setData('text/plain', copyText);
        e.preventDefault();
    }, true);
    document.execCommand('copy');
    console.log('copied text : ', copyText);
    $(this).append("<span id='copylert'>Link copied!</span>")
    setTimeout(function() { $("#copylert").remove(); }, 5000);
});

$('.copy_text1').click(function(e) {
    e.preventDefault();
    var copyText = $(this).attr('href');
    document.addEventListener('copy', function(e) {
        e.clipboardData.setData('text/plain', copyText);
        e.preventDefault();
    }, true);
    document.execCommand('copy');
    console.log('copied text : ', copyText);
    $(this).append("<span id='copylert'>Link copied!</span>")
    setTimeout(function() { $("#copylert").remove(); }, 5000);
});

$(document).ready(function(){
    var get_id = localStorage.getItem('name')
    $("#tablink"+get_id).addClass("current")
    $("#collection"+get_id).addClass("formActive").show();
});

$(document).on("click", function() {
    localStorage.setItem('name', "0");
});

$(document).on("click", ".havesubmenu", function() {
    $(this).next(".menusal").toggleClass("active")
})

$('.statics-html').simpleLoadMore({
    item: '.statics-html-text',
    count: 10,
});

$(document).on("click", ".submit-review", function() {
    $(this).closest("form").submit(function(e) {
        $("#pageLoader").show();
    });
});

$('body').on('shown.bs.modal', '.modal', function() {
    $(this).find('select').each(function() {
        var dropdownParent = $(document.body);
        if ($(this).parents('.modal.in:first').length !== 0)
        dropdownParent = $(this).parents('.modal.in:first');
        $(this).select2({
            dropdownParent: dropdownParent
        // ...
        });
    });
    $('#rqrIn3').select2({
        dropdownParent: $('#drop2'),
    });
    $('#cntry').select2({
        dropdownParent: $('#drop1')
    });
    $('#rqrIn3_inf').select2({
        dropdownParent: $('#drop4'),
    });
    $('#influencer_country').select2({
        dropdownParent: $('#drop3')
    });
    // $('#rqrIn31').select2({
    //     dropdownParent: $('#applicationform1')
    // });
    $('#cntry1').select2({
        dropdownParent: $('#applicationform')
    });
    $('#rqrIn31').select2({
        dropdownParent: $('#applicationform2')
    });
});
function refreshPage(){
  window.location.reload();
}
// $('#rqrIn3').selectpicker();

$(window).scroll(function() {
    var scroll = $(window).scrollTop();
    if (scroll >= 800) {
        $("#backtop").addClass("visible");
        $("#new_steps3 .step-nevigationbutton").addClass("fixed");
    }
    else{
        $("#backtop").removeClass("visible");
        $("#new_steps3 .step-nevigationbutton").removeClass("fixed");
    }
});
$(document).ready(function() {

    // variables
    var toTop = $('#backtop');
    // logic
    toTop.on('click', function() {
        $('html, body').animate({
        scrollTop: $('html, body').offset().top,
        });
    });
});


// $(function(){
//     $('#scrolling-menu a').click(function () {
//         $('#scrolling-menu a').removeClass('active');
//         $(this).addClass('active');
//      });
//  });
 $("#scrolling-menu a[href^='#']").click(function(e) {
	e.preventDefault();

	var position = $($(this).attr("href")).offset().top;

	$("body, html").animate({
		scrollTop: position
	} /* speed */ );
});

$(document).on("load", ".nbrTake", function(number) {
    // alert("sa")
    var digit = ['zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];
    var elevenSeries = ['ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];
    var countingByTens = ['twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];
    var shortScale = ['', 'thousand', 'million', 'billion', 'trillion'];

    number = number.toString(); number = number.replace(/[\, ]/g, ''); if (number != parseFloat(number)) return 'not a number'; var x = number.indexOf('.'); if (x == -1) x = number.length; if (x > 15) return 'too big'; var n = number.split(''); var str = ''; var sk = 0; for (var i = 0; i < x; i++) { if ((x - i) % 3 == 2) { if (n[i] == '1') { str += elevenSeries[Number(n[i + 1])] + ' '; i++; sk = 1; } else if (n[i] != 0) { str += countingByTens[n[i] - 2] + ' '; sk = 1; } } else if (n[i] != 0) { str += digit[n[i]] + ' '; if ((x - i) % 3 == 0) str += 'hundred '; sk = 1; } if ((x - i) % 3 == 1) { if (sk) str += shortScale[(x - i - 1) / 3] + ' '; sk = 0; } } if (x != number.length) { var y = number.length; str += 'point '; for (var i = x + 1; i < y; i++) str += digit[n[i]] + ' '; } str = str.replace(/\number+/g, ' '); return str.trim() + ".";

})
// var cnt = $(".nbrTake").text()
// $(".nbrTake").each(function(){

//     alert(cnt)
// })
// console.log(numberToWords(11))

// document.addEventListener('DOMContentLoaded', function() {
//     // Function to adjust the height of dashboard-left
//     function adjustLeftHeight() {
//         var dashboardRight = document.getElementById('dashboard-right');
//         var dashboardLeft = document.getElementById('dashboard-left');
//         var rightHeight = dashboardRight.offsetHeight;

//         // Set the height of the dashboard-left to match the dashboard-right
//         dashboardLeft.style.height = (rightHeight + 190) + 'px';
//     }

//     // Function to check if the device is desktop
//     function isDesktop() {
//         return window.innerWidth > 768; // Adjust this value based on your breakpoint for mobile devices
//     }

//     // Adjust the height on page load if it's a desktop
//     if (isDesktop()) {
//         adjustLeftHeight();
//     }

//     // Adjust the height on window resize if it's a desktop
//     window.addEventListener('resize', function() {
//         if (isDesktop()) {
//             adjustLeftHeight();
//         } else {
//             // Optionally reset the height for mobile
//             var dashboardLeft = document.getElementById('dashboard-left');
//             dashboardLeft.style.height = 'auto'; // or remove the height style
//         }
//     });
// });


</script>
