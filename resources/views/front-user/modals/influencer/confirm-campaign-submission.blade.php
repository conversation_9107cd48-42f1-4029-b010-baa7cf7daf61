{{-- After the influencer clicks on the submit button to initiate the campaign submission, an ajax call will be made to /initiate-influencer-campaign-submission/{id}
and then this modal will show up. Here the influencer will confirm the submission by selecting the task checkboxes
they has completed. --}}
<style>
    .socialComp {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 16px;
        margin-top: 30px;
    }

    .socialComp > div {
        flex: 1 1 0;
        text-align: center;
    }
</style>

<div class="modal fade confirm-submit influencer"
    id="influencerConfirmCampaignSubmission{{ $influencerDataItem->id }}"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
    tabindex="-1"
    aria-labelledby="requestTimeLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body pt-4">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" alt="">
                </button>
                <div class="wizardForm">
                    <form method="post" action="{{ url('/confirm-influencer-campaign-submission') }}" enctype="multipart/form-data" data-parsley-validate>
                        @csrf
                        <input type="hidden" name="influencer_request_id" id="content_influencer_request_id">
                        <input type="hidden" name="post_id" id="content_post_id">
                        <input type="hidden" name="advertising" id="content_advertising">
                        <input type="hidden" name="media" id="content_media">
                        <input type="hidden" name="media_url" id="content_media_url">
                        <input type="hidden" name="published_at" id="content_published_at">
                        <div class="form-border">
                            <div class="form-group">
                                <div class="imageNvideo">
                                    <img src="" id="content_url_img">
                                    <video controls id="content_url_video_tag">
                                        <source src="" type="video/mp4" id="content_url_video">
                                    </video>
                                </div>
                            </div>
                            <div class="ssn d-flex">
                                <div class="socialdetail">
                                    <div class="socialimage">
                                        <img src="" alt="social media icon" id="social-media-icon">
                                    </div>
                                    <div class="socialcontent">
                                        <div class="socialcontent-posttype" style="max-width: 100%;">
                                            <span class="m-0">Share Content from Brand - Post/Story</span>
                                        </div>
                                        <div class="socialcontent-postlink" style="max-width: 100%;">
                                            <a target="_blank" class="view-link" id="viewLink"></a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{-- Here we could not use the <insight-stats> component because $influencerRequestDetail->social_posts only
                            becomes available after influencer submit. TODO A possible refactoring. --}}
                            <div class="socialComp">
                                <div id="frienddiv"
                                    data-toggle="tooltip"
                                    title="Number of unique Instagram users that have seen the content at least once;"
                                    style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                                    <i class="fas fa-users" aria-hidden="true"></i>
                                    <span id="friend"></span>
                                </div>
                                <div id="viewdiv"
                                    data-toggle="tooltip"
                                    title="Total number of times the content has been seen."
                                    style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                                    <i class="fas fa-eye" aria-hidden="true"></i>
                                    <span id="view"></span>
                                </div>
                                <div id="likediv"
                                    data-toggle="tooltip"
                                    title=""
                                    style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                                    <i class="fas fa-thumbs-up" aria-hidden="true"></i>
                                    <span id="like"></span>
                                </div>
                                <div id="sharediv"
                                    data-toggle="tooltip"
                                    title="Total number of shares."
                                    style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                                    <i class="fas fa-share-alt-square" aria-hidden="true"></i>
                                    <span id="share"></span>
                                </div>
                                <div id="commentdiv"
                                    data-toggle="tooltip"
                                    title="Total number of comments."
                                    style="display: flex; align-items: center; justify-content: center; gap: 6px;">
                                    <i class="fas fa-comments" aria-hidden="true"></i>
                                    <span id="commentSet"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-border">
                            <div class="bold-text margin">Please confirm if you have fullfilled all the following tasks</div>
                            <div class="tasklists">
                                @include('front-user.pages.influencer-tasks')
                            </div>
                            <span id="listFieldError" class="position-relative"></span>
                        </div>
                        @if ($influencerDataItem->post_type == 'Survey')
                            <div class="upload">
                                <label for="file-upload">
                                    <strong>Please upload the survey insights as a screenshot:</strong>
                                </label>
                                <input type="file"
                                    id="file-upload"
                                    name="survey_image"
                                    required
                                    data-parsley-fileextension="jpg,jpeg,png,gif"
                                    data-parsley-fileextension-message="Only JPG, JPEG, PNG, or GIF files are allowed."
                                    data-parsley-error-message="Please upload a jpg, png or gif image of the survey insights">
                                <div id="survey_image_preview" style="text-align: center"></div>
                                <label for="file-upload" class="upload-label">Browse<br>Or Drag and Drop to Upload</label>
                            </div>
                        @endif
                        <div class="custom-task-list" style="margin-bottom:-7px !important;">
                            <div class="form-check" style="background: none;">
                                <input class="form-check-input" type="checkbox"
                                    name="client_rights_terms" id="client_rights_terms"
                                    data-parsley-error-message="Please confirm"
                                    data-parsley-mincheck="1" required>

                                <label class="form-check-label" for="client_rights_terms">
                                    By submitting, I confirm that if I don't complete all the agreed tasks,
                                    the client has the right to get a full refund.
                                </label>
                            </div>
                        </div>
                        <div class="widthBtnPopup d-flex">
                            <input
                                class="table-btn green-btn et-submit accept mx-1 ds submit-campaign-influencer influencer-confirm-campaign-submission"
                                type="submit"
                                name="confirm"
                                value="Submit">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
window.Parsley.addValidator('fileextension', {
    validateString: function(value, requirement, instance) {
        var fileInput = instance.$element[0];
        if (!fileInput.files.length) {
            return false;
        }

        var allowedExtensions = requirement.split(',').map(function(ext) {
            return ext.trim().toLowerCase();
        });

        var fileName = fileInput.files[0].name;
        var fileExtension = fileName.split('.').pop().toLowerCase();

        return allowedExtensions.includes(fileExtension);
    },
    requirementType: 'string',
    messages: {
        en: 'Invalid file type.'
    }
});
</script>