// Set up CSRF token for all AJAX requests
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': window.Laravel.csrfToken
    }
});

const GENERIC_ERROR = "Something went wrong. Please refresh the page and try again.";

const MAX_INFLUENCER_IN_CAMPAIGN = 50;

const CAMPAIGN_TYPE_BOOST_ME = 'Boost me';
const CAMPAIGN_TYPE_REACTION_VIDEO = 'Reaction video';
const CAMPAIGN_TYPE_SURVEY = 'Survey';

const STEP_INVALID = 0;
const STEP_SELECT_CAMPAIGN_TYPE = 1;
const STEP_SELECT_BOOST_ME_TYPE = 2;

const STEP_SELECT_SOCIAL_MEDIA = 3;
const STEP_SELECT_EXTRA_TASKS = 4;
const STEP_ADD_CAMPAIGN_DETAILS = 5;
const STEP_MARKETPLACE_SELECT_INFLUENCER = 6;
const STEP_PAYMENT_AND_REQUEST = 7;

// Constants for calculations
const VAT_RATE = 0.19;
const ADMIN_COMMISSION_RATE = 5;
const MIN_PLATFORM_FEE = 2;

class InfluencerData {
    constructor({
        influencerId = null,
        followers = null,
        price = null,
        vat = null,
        isSmallBusinessOwner = null,
        influencerImage = null,
        influencerImageNew = null,
        influencerUsername = null
    } = {}) {
        this.influencerId = influencerId;
        this.followers = followers;
        this.price = price;
        this.vat = vat;
        this.isSmallBusinessOwner = isSmallBusinessOwner;
        this.influencerImage = influencerImage;
        this.influencerImageNew = influencerImageNew;
        this.influencerUsername = influencerUsername;
    }
}

// Usage:
// const newInfluencer = new InfluencerData();
// const withData = new InfluencerData({ influencerId: 42, price: 100.0 });

var selectedInfluencers = {};

// Should be one of CAMPAIGN_TYPE_BOOST_ME, CAMPAIGN_TYPE_REACTION_VIDEO, CAMPAIGN_TYPE_SURVEY
var selectedCampaignType;

// Initialize step indicators (only if not already initialized)
var createCampaignSteps = $('#create-campaign-top-nav .steps-point');

// Get step indicator elements and their content containers
var stepIndicators = $('#create-campaign-top-nav .steps-point');
var stepContentContainers = $('#create-campaign-top-nav .steps-point .steps-cont');

// Step tracking variables
var step_number = STEP_SELECT_BOOST_ME_TYPE; // For second-level steps (0-4: social media, tasks, details, marketplace, payment)

// TODO this variable globalStepNumberTracker should be used,
// step_number will be deprecated and removed in the future version
var globalStepNumberTracker = step_number;

var prevStep = STEP_INVALID;
var currentStep = STEP_SELECT_CAMPAIGN_TYPE;

var maxInfluencerCounter = 0;

var formData = {
    'campaign_type': null,
    'boost_me_content_type': null,
    'social_media_name': null,
    'post_type': null,
    'additional_tasks': []
};

// Calculated price
// var currentSubtotal = 0;
// var currentFollower = 0;
// var newSubtotal = 0;
// var newFollower = 0;

var subtotal = 0;
var followers = 0;

var platformFee = 0;
var platformFeeVAT = 0;

var influencerVAT = 0;
var totalVAT = 0;
var totalPrice = 0;

// Initialize post type options on page load
$(document).ready(function() {
    $(document).on('change', '.checkFilter', function() {
        getFilteredInfluencers();
    });

    // Reset all radio button states on page load
    resetSocialMediaStep();
    showStepContent(currentStep);
    updateTotals();
});

// Based on selectedInfluencers, we will build the list which will be dynamically
// shown in the payment page.
function buildInfluencerListOnPaymentPage() {
    let listHtml = '';

    Object.entries(selectedInfluencers).forEach(([influencerId, influencerData]) => {
        let small_business_owner_pricing = `
            <span id="vat_value" data-vat="0">0</span> € 
            <span style="font-size: 8px; font-weight:100;">(Small business owner according to § 19 UStG)</span>`;
        let non_small_business_owner_pricing = `
            <span
                id="vat_value"
                data-vat="${influencerData.vat.toFixed(2)}">
                    ${formatNumberWithThousandSeparator(influencerData.vat)}
            </span> € 
            <span style="font-size: 8px; font-weight:100;">(VAT 19%)</span>`;

        let VAT_text = influencerData.isSmallBusinessOwner ? small_business_owner_pricing : non_small_business_owner_pricing;

        // Create influencer list row with data attributes
        listHtml += '<div class="campaign-list influencer_id' + influencerData.influencerId + '">\
                <div class="campaign-item row">\
                    <div class="campaign-influencer col-5" style="margin: 0; display:flex; align-items: center;">\
                        <img src="' + influencerData.influencerImageNew + '" alt="Influencer" class="influencer-pic">\
                        <div class="influencer-details">\
                            <span class="influencer-name" target="_blank" style="color: #212529; flex: 0 0 40px!important; margin: 0!important;">' +
                            influencerData.influencerUsername + '</span>\
                            <span class="follower-count-val" data-followers="' +
                            influencerData.followers + '"><span id="follower-count-val-' +
                            influencerData.influencerId + '">' + influencerData.followers + '</span> Followers</span>\
                        </div>\
                    </div>\
                    <div class="campaign-pricing col-6" style="margin: 0; padding:0; display:flex;">\
                        <div class="row w-100">\
                            <span class="user_price-val col-4" data-price="' + influencerData.price + '">\
                                € <span id="user_price-val-' + influencerData.influencerId + '">' +
                                formatNumberWithThousandSeparator(influencerData.price) + '</span></span>\
                            <span class="campaign_vat_price col-8" style="margin: 0; padding:0;">' + VAT_text + '</span>\
                        </div>\
                        </div>\
                    <div class="remove-icon col-1" style="margin: 0; padding:0; text-align: center;">\
                            <img src="' + window.Laravel.assets.icons.deleteNew + '" data-delete="influencer_id' + influencerData.influencerId + '">\
                    </div>\
                </div>\
            </div>';
    });

    return listHtml;
}

function getSelectedInfluencersCount() {
    return Object.keys(selectedInfluencers).length;
}

function getTasks(socialMedia, campaignType, postType, boostMeContentType) {
    $.ajax({
        url: window.Laravel.urls.getTasks,
        data: {
            media: socialMedia,
            social_media_name: socialMedia,
            campaign_type: campaignType,
            type: campaignType,
            select_type: postType,
            post_type: postType,
            ptc: boostMeContentType,
            boost_me_content_type: boostMeContentType,
        }
    })
    .done(function(data) {
        $("#campaign-tasks").html(data);
    })
    .fail(function(xhr, status, error) {
        toastr.error(GENERIC_ERROR);
    });
}

function getAdditionalTasksInputFields(socialMedia, campaignType, postType, boostMeContentType, additionalTaskIds) {
    $.ajax({
        url: window.Laravel.urls.getTasksInput,
        data: {
            media: socialMedia,
            social_media_name: socialMedia,
            campaign_type: campaignType,
            type: campaignType,
            select_type: postType,
            post_type: postType,
            ptc: boostMeContentType,
            boost_me_content_type: boostMeContentType,
            task_additional: additionalTaskIds
        }
    })
    .done(function(data) {
        $("#campaign-detail-with-additional-tasks-input").html(data);

        // Initialize Tagify if hashtag input exists
        setTimeout(function() {
            var input_tag_one = document.querySelector('#hashtag1');
            if (input_tag_one) {
                var arrayFromPHP = $("#hashtag_arr").val();
                if (arrayFromPHP) {
                    new Tagify(input_tag_one, {
                        whitelist: arrayFromPHP.split(","),
                    });
                }
            }
        }, 100);
    })
    .fail(function(xhr, status, error) {
        toastr.error(GENERIC_ERROR);
    });
}

function getInfluencers() {
    $.ajax({
        url: window.Laravel.urls.getInfluencers,
        data: formData
    }).done(function(data) {
        $("#influencer-marketplace-container").html(data);

        // Restore selected influencers
        $(".selected-influencer-in-cart .influencer-detail").each(function(index) {
            var selected_attribute = $(this).attr("data-id");
            $(".influencer-list .influencer-detail[data-id=" + selected_attribute + "]").addClass("selected");
            $(".influencer-list .influencer-detail[data-id=" + selected_attribute + "] button.select-button")
                .text("UNSELECT")
                .removeClass("select-button")
                .addClass("unselect-button");

            const toggleImage = document.getElementById(`card_clickitfame_logo_${selected_attribute}`);
            if (toggleImage) {
                toggleImage.src = window.Laravel.assets.icons.clickitfameWhite;
            }
        });

        // Initialize load more functionality based on screen size
        setTimeout(function() {
            if (typeof $.fn.simpleLoadMore !== 'undefined') {
                if ($(window).width() > 1399) {
                    $('.influencer-list').simpleLoadMore({
                        item: '.influencer-detail',
                        count: 15,
                        counterInBtn: true,
                        btnText: 'View More {showing}/{total}',
                    });
                } else if ($(window).width() > 1024) {
                    $('.influencer-list').simpleLoadMore({
                        item: '.influencer-detail',
                        count: 12,
                        counterInBtn: true,
                        btnText: 'View More {showing}/{total}',
                    });
                } else if ($(window).width() > 991) {
                    $('.influencer-list').simpleLoadMore({
                        item: '.influencer-detail',
                        count: 9,
                        counterInBtn: true,
                        btnText: 'View More {showing}/{total}',
                    });
                } else if ($(window).width() > 767) {
                    $('.influencer-list').simpleLoadMore({
                        item: '.influencer-detail',
                        count: 6,
                        counterInBtn: true,
                        btnText: 'View More {showing}/{total}',
                    });
                } else {
                    $('.influencer-list').simpleLoadMore({
                        item: '.influencer-detail',
                        count: 12,
                        counterInBtn: true,
                        btnText: 'View More {showing}/{total}',
                    });
                }
            }
        }, 500);
    }).fail(function(xhr, status, error) {
        toastr.error(GENERIC_ERROR);
    });
}

function getFilteredInfluencers() {
    const dataString = $("#form-create-campaign").serialize();
    $.ajax({
        url: window.Laravel.urls.marketStepFilter,
        data: dataString
    }).done(function(data) {
        $("#influencer-marketplace-container").html(data);
        
        $(".selected-influencer-in-cart .influencer-detail").each(function() {
            const selected_attribute = $(this).attr("data-id");
            $(".influencer-list .influencer-detail[data-id=" + selected_attribute + "]").addClass("selected");
        });
        
        $(".select").select2();
        $("#hashtags").select2({
            placeholder: "Select",
            multiple: true
        });
    }).fail(function(xhr, status, error) {
        toastr.error(GENERIC_ERROR);
    });
}

// If useCached = true, then we will use the currently loaded data specific 
// to the stepNumber and rebuild the view.
// This is specifically helpful when user navigating back in the create
// campaign step form
function showStepContent(stepNumber, useCached = false) {
    console.log('>>> ' + '#create-campaign-step-' + stepNumber + ' will be SHOWN');
    
    $('#create-campaign-step-' + stepNumber).removeClass('d-none').addClass('d-block');

    if (stepNumber >= STEP_SELECT_SOCIAL_MEDIA) {
        $('#create-campaign-steps-3-to-7').removeClass('d-none').addClass('d-block');
        $('#steps-point-' + (stepNumber - 1)).addClass("completed").removeClass("inprogress");
        $('#steps-point-' + stepNumber).addClass("inprogress").removeClass("completed");
    }

    if (stepNumber == STEP_SELECT_EXTRA_TASKS && !useCached) {
        getTasks(
            formData.social_media_name,
            formData.campaign_type,
            formData.post_type,
            formData.boost_me_content_type
        );
    }

    if (stepNumber == STEP_ADD_CAMPAIGN_DETAILS && !useCached) {
        getAdditionalTasksInputFields(
            formData.social_media_name,
            formData.campaign_type,
            formData.post_type,
            formData.boost_me_content_type,
            formData.additional_tasks
        );
    }

    if (stepNumber == STEP_MARKETPLACE_SELECT_INFLUENCER) {
        getInfluencers();
    }

    if (stepNumber == STEP_PAYMENT_AND_REQUEST) {
        let listHtml = buildInfluencerListOnPaymentPage();
        $('#create-campaign-step-7 #influencers-row').html(listHtml);
    }
}

function hideStepContent(stepNumber) {
    console.log('>>> ' + '#create-campaign-step-' + stepNumber + ' will be HIDDEN');
    $('#create-campaign-step-' + stepNumber).removeClass('d-block').addClass('d-none');
    if (stepNumber <= STEP_SELECT_SOCIAL_MEDIA) {
        $('#create-campaign-steps-3-to-7').removeClass('d-block').addClass('d-none');
        $('.steps-point').removeClass("completed").removeClass("inprogress");
    }
}

// Add validation function for campaign type selection
function validateCampaignTypeSelection() {
    if (!$('input[name="campaign_type"]:checked').length) {
        $(".alert-select-option").removeClass("d-none");
        return false;
    }
    return true;
}

// Helper functions for calculations
function calculatePlatformFee(subtotal) {
    let fee = (subtotal * ADMIN_COMMISSION_RATE) / 100;
    return fee < MIN_PLATFORM_FEE ? MIN_PLATFORM_FEE : fee;
}

function parseFloatSafe(value) {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? 0 : parsed;
}

function parseIntSafe(value) {
    const parsed = parseInt(value);
    return isNaN(parsed) ? 0 : parsed;
}

function formatNumberWithThousandSeparator(value) {
    return parseFloatSafe(value).toLocaleString('de-DE', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function updateTotals() {
    subtotal = 0;
    followers = 0;
    influencerVAT = 0;

    platformFee = 0;
    platformFeeVAT = 0;

    totalVAT = 0;
    totalPrice = 0;
    if (getSelectedInfluencersCount() > 0) {
        Object.entries(selectedInfluencers).forEach(([influencerId, influencerData]) => {
            subtotal += influencerData.price;
            followers += influencerData.followers;
            influencerVAT += influencerData.isSmallBusinessOwner ? influencerData.vat : 0;
        });

        // Calculate platform fee
        platformFee = calculatePlatformFee(subtotal);
        platformFeeVAT = platformFee * VAT_RATE;
        
        // Calculate the total fee
        totalVAT = influencerVAT + platformFeeVAT;
        totalPrice = subtotal + platformFee + totalVAT;
    }

    // Update the subtotal and followers count in the cart view of the influencer marketplace page
    $('#subtotal').text(formatNumberWithThousandSeparator(subtotal)).data('subtotal', subtotal);
    $('#follower').text(followers).data('followers', followers);

    // Update the subtotal (only influencer's price) payable by the customer in the payment page
    $('#subtotal_in_payment_page').text(formatNumberWithThousandSeparator(subtotal)).data('subtotal', subtotal);
    
    // Update the total followers count through the selected influencers for this campaign in the payment page
    $('#follower_in_payment_page').text(followers).data('followers', followers);
    
    // Update the total vat payable by the customer in the payment page
    $('#vat_in_payment_page').text(formatNumberWithThousandSeparator(totalVAT)).data('vat', totalVAT);
    
    // Update the total platform fee payable by the customer in the payment page (this does not include commission/provision)
    $('#fee_in_payment_page').text(formatNumberWithThousandSeparator(platformFee)).data('fee', platformFee);
    
    // Update the order total/grand total payable by the customer in the payment page
    $('#total_in_payment_page').text(formatNumberWithThousandSeparator(totalPrice)).data('total', totalPrice);

    console.log('>>>>>>>> updateTotals() debug info:');
    console.log('subtotal:', subtotal);
    console.log('follower:', followers);
    console.log('platformFee:', platformFee);
    console.log('platformFeeVAT:', platformFeeVAT);
    console.log('influencerVAT:', influencerVAT);
    console.log('totalVAT:', totalVAT);
    console.log('totalPrice:', totalPrice);
}

// Function to reset social media step radio button states
function resetSocialMediaStep() {
    // Uncheck all social media radios
    $('input[name="mp_socialmedia"]').prop('checked', false);

    // Uncheck all post type radios
    $('input[name="mp_socialmedia_type2"]').prop('checked', false);

    // Remove active class from social media options
    $(".social_media_radio").removeClass("active");

    // Hide all post type containers
    $(".media_platform_post_type").hide();

    // Hide all post type option groups
    $(".media_platform_post_type .get_type").hide();

    // Clear error messages
    $("#error-select-social-media").text("");
    $("#error-post-type").text("");

    // Remove validation attributes
    $('input[name="mp_socialmedia_type2"]').removeAttr('required')
        .removeAttr('data-parsley-errors-container')
        .removeAttr('data-parsley-error-message');
}

// Function to update post type validation based on visible options
function updatePostTypeValidation() {
    // Remove validation from all post type radios first
    $('input[name="mp_socialmedia_type2"]').removeAttr('required')
        .removeAttr('data-parsley-errors-container')
        .removeAttr('data-parsley-error-message');

    // Add validation to the first visible post type radio
    var firstVisiblePostType = $('.media_platform_post_type .get_type:visible input[name="mp_socialmedia_type2"]').first();
    if (firstVisiblePostType.length) {
        firstVisiblePostType.attr('required', 'required')
            .attr('data-parsley-errors-container', '#error-post-type')
            .attr('data-parsley-error-message', 'Please select post type.');
    }
}

// Function to show appropriate post type options while preserving existing selections
function showPostTypeOptionsPreserveSelection() {
    var selectedBoostType = $('input[name="boost_me_content_type"]:checked').val();
    var selectedCampaignType = $('input[name="campaign_type"]:checked').val();

    // console.log('Showing post type options (preserving selection) for boost type:', selectedBoostType, 'campaign type:', selectedCampaignType);

    // Store current selection before hiding options
    var currentSelection = $('input[name="mp_socialmedia_type2"]:checked').val();

    // Hide all post type options first
    $(".media_platform_post_type .get_type").hide();

    // Show appropriate options based on selection
    if (selectedBoostType) {
        $(".media_platform_post_type .get_type." + selectedBoostType).show();
    } else if (selectedCampaignType) {
        var campaignClass = selectedCampaignType.toLowerCase().replace(' ', '-');
        $(".media_platform_post_type .get_type." + campaignClass).show();
    }

    // Restore the previous selection if it's still visible
    if (currentSelection) {
        var restoredRadio = $('.media_platform_post_type .get_type:visible input[name="mp_socialmedia_type2"][value="' + currentSelection + '"]');
        if (restoredRadio.length) {
            restoredRadio.prop('checked', true);
            // console.log('Restored post type selection:', currentSelection);
        }
    }

    // Update validation
    updatePostTypeValidation();

    // console.log('Visible post type options (preserved):', $('.media_platform_post_type .get_type:visible').length);
}

// When returning to the social media step, restore post type visibility if social media is already selected
function restorePostTypeVisibility() {
    var selectedSocialMedia = $('input[name="mp_socialmedia"]:checked');
    if (selectedSocialMedia.length) {
        // Show the post type container for the selected social media
        var postTypeContainer = selectedSocialMedia.closest(".media_platform").next(".media_platform_post_type");
        postTypeContainer.css("display", "flex");

        // Preserve existing post type selection
        var selectedPostType = $('input[name="mp_socialmedia_type2"]:checked').val();

        // Show appropriate post type options without clearing selections
        showPostTypeOptionsPreserveSelection();
    }
}
