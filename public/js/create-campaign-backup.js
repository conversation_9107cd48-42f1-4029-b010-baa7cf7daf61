// Set up CSRF token for all AJAX requests
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': window.Laravel.csrfToken
    }
});

const CAMPAIGN_TYPE_BOOST_ME = 'Boost me';
const CAMPAIGN_TYPE_REACTION_VIDEO = 'Reaction video';
const CAMPAIGN_TYPE_SURVEY = 'Survey';

const STEP_INVALID = 0;
const STEP_SELECT_CAMPAIGN_TYPE = 1;
const STEP_SELECT_BOOST_ME_TYPE = 2;

const STEP_SELECT_SOCIAL_MEDIA = 3;
const STEP_SELECT_EXTRA_TASKS = 4;
const STEP_ADD_CAMPAIGN_DETAILS = 5;
const STEP_MARKETPLACE_SELECT_INFLUENCER = 6;
const STEP_PAYMENT_AND_REQUEST = 7;

// Constants for calculations
const VAT_RATE = 0.19;
const ADMIN_COMMISSION_RATE = 5;
const MIN_PLATFORM_FEE = 2;

var selectedInfluencerIds = [];

// Should be one of CAMPAIGN_TYPE_BOOST_ME, CAMPAIGN_TYPE_REACTION_VIDEO, CAMPAIGN_TYPE_SURVEY
var selectedCampaignType;

// Initialize step indicators (only if not already initialized)
var createCampaignSteps = $('#create-campaign-top-nav .steps-point');

// Get step indicator elements and their content containers
var stepIndicators = $('#create-campaign-top-nav .steps-point');
var stepContentContainers = $('#create-campaign-top-nav .steps-point .steps-cont');

// Step tracking variables
var step_number = STEP_SELECT_BOOST_ME_TYPE; // For second-level steps (0-4: social media, tasks, details, marketplace, payment)

// TODO this variable globalStepNumberTracker should be used,
// step_number will be deprecated and removed in the future version
var globalStepNumberTracker = step_number;

var prevStep = STEP_INVALID;
var currentStep = STEP_SELECT_CAMPAIGN_TYPE;

var maxInfluencerCounter = 0;

// Initialize post type options on page load
$(document).ready(function() {
    // Reset all radio button states on page load
    resetSocialMediaStep();
    showStepContent(currentStep);
});

$('input[name="campaign_type"]').click(function() {
    selectedCampaignType = $(this).val();
    prevStep = currentStep;

    if(selectedCampaignType == CAMPAIGN_TYPE_BOOST_ME) {
        currentStep += 1;
    } else {
        // Here, nextStep is getting +1 more because we have extra step (step 2)
        // for Boost me only, where user choose post type content.
        // For other campaign types, we directly go to step 3
        currentStep += 2;
    }

    console.log(selectedCampaignType);
    console.log(currentStep);
    console.log(prevStep);

    hideStepContent(prevStep);
    showStepContent(currentStep);
});

// Handle boost type selection and show appropriate post type options
$('input[name="boost_me_post_type"]').click(function() {
    var selected_boost_me_post_type = $('input[name="boost_me_post_type"]:checked').val();

    console.log('selected boost me post type: ' + selected_boost_me_post_type);

    var formData = $('#form-create-campaign').serializeArray();
    var jsonObject = {};
    $.each(formData, function(i, field) {
        jsonObject[field.name] = field.value;
    });

    console.log(jsonObject);

    prevStep = currentStep;
    currentStep += 1;

    console.log(currentStep);
    console.log(prevStep);

    hideStepContent(prevStep);
    showStepContent(currentStep);

    // $("#steps-point0").addClass("inprogress");
    // $("#new_steps0").show();
});

$(document).on("click", ".button-open-filter", function() {
    $(".filter-button-outer").hide()
    $(".filter-box-outer").show()
    $("select.filter-by").select2();
    $("select").select2();
});

$(document).on("click", ".button-close-filter", function() {
    $(".filter-button-outer").show()
    $(".filter-box-outer").hide()
});

$(document).on("click", ".step-nevigationbutton > div.influencer-cart", function() {
    $(".selected-influencer-box").toggleClass("open")
    $(".filter-overlay").toggleClass("open")
});

$(document).on("click", ".step-nevigationbutton > div > img.close_inf_box", function() {
    $(".selected-influencer-box").toggleClass("open")
    $(".filter-overlay").toggleClass("open")
});

$(document).on("click", ".influencer-detail button.select-button", function(event) {
    if (maxInfluencerCounter >= 50) {
        toastr.info("Maximum number of influencer limit reached. ");
        return;
    }

    const $influencerDetail = $(this).closest(".influencer-detail");
    const get_influencer_id = $influencerDetail.attr("data-id");
    const get_influncer_image = $influencerDetail.find(".profile-pic").html();
    const get_influncer_image_new = $influencerDetail.find(".profile-pic img").attr("src");
    const get_influncer_username = $influencerDetail.find(".user_name").text();
    const is_small_business_owner = Boolean(parseInt($influencerDetail.find("#is_small_business_owner").text() || "0"));
    const get_influncer_follower = parseIntSafe($influencerDetail.find(".follower-count-val").data("followers")) || 0;
    const get_influncer_price = parseFloatSafe($influencerDetail.find(".user_price-val").data("price")) || 0;

    // Update subtotal and follower count
    const currentSubtotal = parseFloatSafe($('#subtotal').data('subtotal') || 0);
    const currentFollower = parseIntSafe($('#follower').data('followers') || 0);
    const newSubtotal = currentSubtotal + get_influncer_price;
    const newFollower = currentFollower + get_influncer_follower;

    // Update with formatted text and data attributes
    $('#subtotal').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
    $('#follower').text(newFollower).data('followers', newFollower);

    // Add to selected influencers
    var influncer_div = "<div class='influencer-detail card-container text-center mx-auto mt-4' data-id='" +
        get_influencer_id + "'>" + $influencerDetail.html() + "</div>";
    $(".selected-influencer-contr").append(influncer_div);
    $("span.influencer-cart-count").text($(".selected-influencer-contr .influencer-detail").length);
    $(".selected-influencer-contr .influencer-detail button").text("Remove").removeClass("select-button").addClass("remove-button");
    $influencerDetail.addClass("selected");

    $(this).text("UNSELECT").removeClass("select-button").addClass("unselect-button");

    $("#delected-influencer").val($(".selected-influencer-contr .influencer-detail").length);

    const toggleImage = document.getElementById(`card_clickitfame_logo_${get_influencer_id}`);
    if (toggleImage) {
        toggleImage.src = window.Laravel.assets.icons.clickitfameWhite;
    }

    // Add to influencer list
    const influencer_id = get_influencer_id.replace(/influencer_id/, '');
    const VAT_value = get_influncer_price * VAT_RATE;

    if (!selectedInfluencerIds.includes(influencer_id)) {
        selectedInfluencerIds.push(influencer_id);
    }

    // console.log('*****************');
    // console.log('Selected influencer ids: ');
    // console.log(selectedInfluencerIds);
    // console.log('subtotal: ' + newSubtotal);
    // console.log('follower: ' + newFollower);
    // console.log('get_influencer_price: ' + get_influncer_price);
    // console.log('get_influencer_follower: ' + get_influncer_follower);

    let small_business_owner_pricing =
        '<span id="vat_value" data-vat="0">0</span> € <span style="font-size: 8px; font-weight:100;">(Small business owner according to § 19 UStG)</span>';
    let non_small_business_owner_pricing =
        `<span id="vat_value" data-vat="${VAT_value.toFixed(2)}">${formatNumberWithThousandSeparator(VAT_value)}</span> € <span style="font-size: 8px; font-weight:100;">(VAT 19%)</span>`;

    let VAT_text = is_small_business_owner ? small_business_owner_pricing : non_small_business_owner_pricing;

    // Create influencer list row with data attributes
    var influncer_list_row_new = '<div class="campaign-list ' + get_influencer_id + '">\
            <div class="campaign-item row">\
                <div class="campaign-influencer col-5" style="margin: 0; display:flex; align-items: center;">\
                    <img src="' + get_influncer_image_new + '" alt="Influencer" class="influencer-pic">\
                    <div class="influencer-details">\
                        <span class="influencer-name" target="_blank" style="color: #212529; flex: 0 0 40px!important; margin: 0!important;">' + get_influncer_username + '</span>\
                        <span class="follower-count-val" data-followers="' + get_influncer_follower + '"><span id="follower-count-val-' + get_influencer_id + '">' + get_influncer_follower + '</span> Followers</span>\
                    </div>\
                </div>\
                <div class="campaign-pricing col-6" style="margin: 0; padding:0; display:flex;">\
                    <div class="row w-100">\
                        <span class="user_price-val col-4" data-price="' + get_influncer_price + '">\
                            € <span id="user_price-val-' + get_influencer_id + '">' + formatNumberWithThousandSeparator(get_influncer_price) + '</span></span>\
                        <span class="campaign_vat_price col-8" style="margin: 0; padding:0;">' + VAT_text + '</span>\
                    </div>\
                    </div>\
                <div class="remove-icon col-1" style="margin: 0; padding:0; text-align: center;">\
                        <img src="' + window.Laravel.assets.icons.deleteNew + '" data-delete="' + get_influencer_id + '">\
                </div>\
            </div>\
        </div>';

    var influncer_list_row = '\
        <tr class="influencer-detail-final ' + get_influencer_id + '">\
            <td>\
                <input type="hidden" name="influencer_selected_id[]" value="' + influencer_id + '">\
                <div class="finish-image"> ' + get_influncer_image + ' </div>\
            </td>\
            <td class="influencer-name">\
                ' + get_influncer_username + '\
            </td>\
            <td class="total-follower">\
                <span class="follower-count-val" data-followers="' + get_influncer_follower + '">' + get_influncer_follower + '</span> Follower\
            </td>\
            <td class="pricing">\
                <span class="user_price-val" data-price="' + get_influncer_price + '">' + formatNumberWithThousandSeparator(get_influncer_price) + '</span> €\
            </td>\
            <td class="remove-list">\
                <img src="' + window.Laravel.assets.icons.deleteIcon + '" data-delete="' + get_influencer_id + '" alt="">\
            </td>\
        </tr>';

    $(".form-shadow-box.middle-box table tbody").append(influncer_list_row);
    $('#influencers-row').append(influncer_list_row_new);
    
    // Update totals
    updateTotals();
    
    maxInfluencerCounter++;
});

$(document).on("click", ".influencer-detail button.unselect-button", function() {
    const influencer_id = $(this).closest(".influencer-detail").attr("data-id").replace(/influencer_id/, '');
    $('.selected-influencer-contr div[data-id="influencer_id' + influencer_id + '"]').remove();

    $(".form-shadow-box.middle-box table tbody .influencer_id" + influencer_id).remove();

    $('.influencer-list div[data-id="influencer_id' + influencer_id + '"]').removeClass("selected");
    $("span.influencer-cart-count").text($(".selected-influencer-contr .influencer-detail").length);
    $("#delected-influencer").val($(".selected-influencer-contr .influencer-detail").length);

    const toggleImage = document.getElementById(`card_clickitfame_logo_influencer_id${influencer_id}`);
    if (toggleImage) {
        toggleImage.src = window.Laravel.assets.icons.clickitfameLogo;
    }

    $('.influencer-list div[data-id="influencer_id' + influencer_id + '"] button.unselect-button')
        .text("SELECT")
        .removeClass("unselect-button")
        .addClass("select-button");

    var get_influncer_follower = parseIntSafe($(this).closest(".influencer-detail").find(".follower-count-val").data("followers")) || 0;
    var get_influncer_price = parseFloatSafe($(this).closest(".influencer-detail").find(".user_price-val").data("price")) || 0;
    var is_small_business_owner = Boolean(parseInt($(this).closest(".influencer-detail").find("#is_small_business_owner").text() || "0"));

    selectedInfluencerIds = selectedInfluencerIds.filter(id => id != influencer_id);

    // console.log('>>>>>>>>>>>>>>>');
    // console.log('selected influencer ids after removal: ');
    // console.log(selectedInfluencerIds);

    // Update subtotal and follower count
    const currentSubtotal = parseFloatSafe($('#subtotal').data('subtotal') || 0);
    const currentFollower = parseIntSafe($('#follower').data('followers') || 0);
    const newSubtotal = currentSubtotal - get_influncer_price;
    const newFollower = currentFollower - get_influncer_follower;

    // Update with formatted text and data attributes
    $('#subtotal').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
    $('#follower').text(newFollower).data('followers', newFollower);
    
    // Update totals
    updateTotals();
    
    $(".influencer_id" + influencer_id).remove();
});

$(document).on("click", ".influencer-detail button.remove-button", function() {
    var get_influencer_id = $(this).closest(".influencer-detail").attr("data-id").replace(/influencer_id/, '');
    $(this).closest(".influencer-detail").remove();

    $(".form-shadow-box.middle-box table tbody .influencer_id" + get_influencer_id).remove();

    $('.influencer-list div[data-id="influencer_id' + get_influencer_id + '"]').removeClass("selected");
    $("span.influencer-cart-count").text($(".selected-influencer-contr .influencer-detail").length);
    $("#delected-influencer").val($(".selected-influencer-contr .influencer-detail").length);

    const toggleImage = document.getElementById(`card_clickitfame_logo_influencer_id${get_influencer_id}`);
    if (toggleImage) {
        toggleImage.src = window.Laravel.assets.icons.clickitfameLogo;
    }

    $('.influencer-list div[data-id="influencer_id' + get_influencer_id + '"] button.unselect-button')
        .text("SELECT")
        .removeClass("unselect-button")
        .addClass("select-button");

    var get_influncer_follower = parseIntSafe($(this).closest(".influencer-detail").find(".follower-count-val").data("followers")) || 0;
    var get_influncer_price = parseFloatSafe($(this).closest(".influencer-detail").find(".user_price-val").data("price")) || 0;
    var is_small_business_owner = Boolean(parseInt($(this).closest(".influencer-detail").find("#is_small_business_owner").text() || "0"));

    // Update subtotal and follower count
    const currentSubtotal = parseFloatSafe($('#subtotal').data('subtotal') || 0);
    const currentFollower = parseIntSafe($('#follower').data('followers') || 0);
    const newSubtotal = currentSubtotal - get_influncer_price;
    const newFollower = currentFollower - get_influncer_follower;

    // Update with formatted text and data attributes
    $('#subtotal').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
    $('#follower').text(newFollower).data('followers', newFollower);
    
    // Update totals
    updateTotals();
    
    $(".influencer_id" + get_influencer_id).remove();
    maxInfluencerCounter -= 1;
});

// Delete icon click handler in campaign list
$(document).on("click", ".campaign-item .remove-icon img", function() {
    var get_influencer_id = $(this).data("delete");
    const influencer_id = get_influencer_id.replace(/influencer_id/, '');
    
    // Remove the influencer from various containers
    $('.selected-influencer-contr div[data-id="influencer_id' + get_influencer_id + '"]').remove();
    $(".form-shadow-box.middle-box table tbody .influencer_id" + get_influencer_id).remove();
    $('.influencer-list div[data-id="influencer_id' + get_influencer_id + '"]').removeClass("selected");
    
    // Reset the button state
    $('.influencer-list div[data-id="influencer_id' + get_influencer_id + '"] button.unselect-button')
        .text("SELECT")
        .removeClass("unselect-button")
        .addClass("select-button");
    
    // Update the logo image
    const toggleImage = document.getElementById(`card_clickitfame_logo_influencer_id${get_influencer_id}`);
    if (toggleImage) {
        toggleImage.src = window.Laravel.assets.icons.clickitfameLogo;
    }

    // Get the follower count from the data attribute instead of text
    var get_influncer_follower = parseIntSafe($(this).closest(".campaign-item").find(".follower-count-val").data("followers")) || 0;

    // Get the price from the data attribute
    var get_influncer_price = parseFloatSafe($(this).closest(".campaign-item").find(".user_price-val").data("price")) || 0;

    // Get VAT value if available
    var vat_value = parseFloatSafe($(this).closest(".campaign-item").find("#vat_value").data("vat")) || 0;

    // Update subtotal and follower count
    const currentSubtotal = parseFloatSafe($('#subtotal').data('subtotal') || 0);
    const currentFollower = parseIntSafe($('#follower').data('followers') || 0);
    const newSubtotal = currentSubtotal - get_influncer_price;
    const newFollower = currentFollower - get_influncer_follower;

    // Update all relevant elements with formatted text and data attributes
    $('#subtotal').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);
    $('#subtotal_in_payment_page').text(formatNumberWithThousandSeparator(newSubtotal)).data('subtotal', newSubtotal);

    $('#follower').text(newFollower).data('followers', newFollower);
    $('#follower_in_payment_page').text(newFollower).data('followers', newFollower);

    // Update totals
    updateTotals();

    selectedInfluencerIds = selectedInfluencerIds.filter(id => id != influencer_id);

    // console.log('>>>>>>>>>>>>>>>>');
    // console.log('After removing influencer from payment page: ');
    // console.log(selectedInfluencerIds);

    // Update counter and remove the element
    maxInfluencerCounter -= 1;
    $(this).closest("div.campaign-list").remove();
    $(".influencer-cart span.influencer-cart-count").text(maxInfluencerCounter);
});

$(document).on("click", ".create-campaign-steps .nav-right", function() {
    prevStep = currentStep;
    currentStep += 1;
    var nextStep = currentStep + 1;

    console.log('>>>>>>>>>>>>> Next button clicked');
    console.log('current step:', currentStep);
    console.log('>>>>>>>>>>>>>');

    // Validate current step inputs
    var isValid = true;
    currentStep.find(':input:not(:button)').each(function(index, value) {
        if ($(this).parsley) {
            if (!$(this).parsley().validate()) {
                isValid = false;
            }
        }
        if ($(this).hasClass('tagify--outside')) {
            $(this).siblings('tags').find('span').attr('style', 'border:1px solid red');
        }
    });

    // Additional validation for specific steps
    if (current_step_number == 0) {
        // Validate social media selection
        if (!$('input[name="mp_socialmedia"]:checked').length) {
            isValid = false;
            $("#error-select-social-media").text("Please select social media.");
        } else {
            $("#error-select-social-media").text("");
        }

        // Validate post type selection from visible options only
        var visiblePostTypes = $('.media_platform_post_type .get_type:visible input[name="mp_socialmedia_type2"]');
        var hasVisibleSelection = false;

        visiblePostTypes.each(function() {
            if ($(this).is(':checked')) {
                hasVisibleSelection = true;
                return false; // break the loop
            }
        });

        if (visiblePostTypes.length > 0 && !hasVisibleSelection) {
            isValid = false;
            $("#error-post-type").text("Please select post type.");
        } else {
            $("#error-post-type").text("");
        }
    }

    // This is the marketplace step, where user selects influencers for their campaign
    if (current_step_number == STEP_MARKETPLACE_SELECT_INFLUENCER) {
        // Validate influencer selection
        if ($(".selected-influencer-contr .influencer-detail").length == 0) {
            isValid = false;
            toastr.error("Please select at least one influencer before proceeding.");
            return false;
        }
    }

    if (isValid && nextStep < 5) {
        // Update global step tracking
        globalStepNumberTracker = nextStep;

        // Hide current step and show next step
        currentStep.hide();
        $('#new_steps' + nextStep).show();
        $('#steps-point' + nextStep).addClass("inprogress");
        $('#steps-point' + current_step_number).addClass("completed").removeClass("inprogress");

        // Load tasks for step 1 (globalStepNumberTracker 0 -> next step 1)
        if (nextStep == 1) {
            var media = $('input[name="mp_socialmedia"]:checked').val();
            var type = $('input[name="campaign_type"]:checked').val();
            var select_type = $('input[name="mp_socialmedia_type2"]:checked').val();
            var boost_me_post_type = $('input[name="boost_me_post_type"]:checked').val();
            $.ajax({
                    url: window.Laravel.urls.getTasks,
                    data: {
                        media: media,
                        type: type,
                        select_type: select_type,
                        ptc: boost_me_post_type
                    }
                })
                .done(function(data) {
                    $("#latest-task").html(data);
                })
                .fail(function(xhr, status, error) {
                    console.error("Failed to load tasks:", error);
                });
        }

        // Load task inputs for step 2 (globalStepNumberTracker 1 -> next step 2)
        if (nextStep == 2) {
            var media = $('input[name="mp_socialmedia"]:checked').val();
            var type = $('input[name="campaign_type"]:checked').val();
            var select_type = $('input[name="mp_socialmedia_type2"]:checked').val();
            var boost_me_post_type = $('input[name="boost_me_post_type"]:checked').val();

            var task_additional = new Array();
            $(".task_additional:checked").each(function() {
                task_additional.push($(this).val());
            });

            $.ajax({
                url: window.Laravel.urls.getTasksInput,
                data: {
                    media: media,
                    type: type,
                    select_type: select_type,
                    ptc: boost_me_post_type,
                    task_additional: task_additional
                }
            })
            .done(function(data) {
                $("#latest-task-inputs").html(data);

                // Initialize Tagify if hashtag input exists
                setTimeout(function() {
                    var input_tag_one = document.querySelector('#hashtag1');
                    if (input_tag_one) {
                        var arrayFromPHP = $("#hashtag_arr").val();
                        if (arrayFromPHP) {
                            new Tagify(input_tag_one, {
                                whitelist: arrayFromPHP.split(","),
                            });
                        }
                    }
                }, 100);
            })
            .fail(function(xhr, status, error) {
                console.error("Failed to load task inputs:", error);
            });
        }

        // Load influencers for step 3 (globalStepNumberTracker 2 -> next step 3)
        if (nextStep == STEP_MARKETPLACE_SELECT_INFLUENCER) {
            var formData = $('#form-create-campaign').serializeArray();
            var jsonObject = {};
            $.each(formData, function(i, field) {
                jsonObject[field.name] = field.value;
            });

            jsonObject.media = jsonObject.mp_socialmedia;
            jsonObject.type = jsonObject.campaign_type;
            jsonObject.select_type = jsonObject.mp_socialmedia_type2;

            $.ajax({
                    url: window.Laravel.urls.getInfluencers,
                    data: jsonObject
            })
            .done(function(data) {
                $("#latest-influencer-lists").html(data);
                $('#compaingTitle').html($('#campaign_title').val());

                // Restore selected influencers
                $(".selected-influencer-contr .influencer-detail").each(function(index) {
                    var selected_attribute = $(this).attr("data-id");
                    $(".influencer-list .influencer-detail[data-id=" + selected_attribute + "]").addClass("selected");

                    $(".influencer-list .influencer-detail[data-id=" + selected_attribute + "] button.select-button")
                        .text("UNSELECT")
                        .removeClass("select-button")
                        .addClass("unselect-button");

                    const toggleImage = document.getElementById(`card_clickitfame_logo_${selected_attribute}`);
                    if (toggleImage) {
                        toggleImage.src = window.Laravel.assets.icons.clickitfameWhite;
                    }
                });

                // Initialize load more functionality based on screen size
                setTimeout(function() {
                    if (typeof $.fn.simpleLoadMore !== 'undefined') {
                        if ($(window).width() > 1399) {
                            $('.influencer-list').simpleLoadMore({
                                item: '.influencer-detail',
                                count: 15,
                                counterInBtn: true,
                                btnText: 'View More {showing}/{total}',
                            });
                        } else if ($(window).width() > 1024) {
                            $('.influencer-list').simpleLoadMore({
                                item: '.influencer-detail',
                                count: 12,
                                counterInBtn: true,
                                btnText: 'View More {showing}/{total}',
                            });
                        } else if ($(window).width() > 991) {
                            $('.influencer-list').simpleLoadMore({
                                item: '.influencer-detail',
                                count: 9,
                                counterInBtn: true,
                                btnText: 'View More {showing}/{total}',
                            });
                        } else if ($(window).width() > 767) {
                            $('.influencer-list').simpleLoadMore({
                                item: '.influencer-detail',
                                count: 6,
                                counterInBtn: true,
                                btnText: 'View More {showing}/{total}',
                            });
                        } else {
                            $('.influencer-list').simpleLoadMore({
                                item: '.influencer-detail',
                                count: 12,
                                counterInBtn: true,
                                btnText: 'View More {showing}/{total}',
                            });
                        }
                    }
                }, 500);
            })
            .fail(function(xhr, status, error) {
                console.error("Failed to load influencers:", error);
            });
        }

    }

    if ($('#new_steps3').is(':visible')) {
        $('html, body').animate({
            scrollTop: $('html, body').offset().top,
        });
        var btn = $('body');
        btn.addClass('show');
    }
});

// Steps navigation - back button (only for second-level steps in shoutout section)
$(document).on("click", ".nav-left", function() {
    currentStep -= 1;
    prevStep = currentStep - 1;

    console.log('<<<<<<<<<<<<<< Previous button clicked');
    console.log('currentStep: ' + currentStep);
    console.log('prevStep: ' + prevStep);
    console.log('<<<<<<<<<<<<<<');
    
    if (currentStep == STEP_SELECT_CAMPAIGN_TYPE) {
        // If we're at the first second-level step, go back to first-level steps
        var fromStep = $(this).attr("data-from-step");
        if (fromStep == "Reaction video" || fromStep == "Survey") {
            // Go back to campaign type selection
            $(".shoutout").addClass("d-none").removeClass("d-block");
            $(".campaign-type").addClass("d-block").removeClass("d-none");
        } else {
            // Go back to boost type selection
            $(".shoutout").addClass("d-none").removeClass("d-block");
            $(".boost-me").addClass("d-block").removeClass("d-none");
        }

        // Reset all radio button states when going back to first-level steps
        resetSocialMediaStep();

        // Reset step indicators and global step tracking
        $('#steps-point0').removeClass("inprogress completed");
    } else if (prevStep >= STEP_SELECT_CAMPAIGN_TYPE) {
        // Navigate within second-level steps - preserve radio button states
        currentStep.hide();
        $('#new_steps' + prevStep).show();
        $('#steps-point' + current_step_number).removeClass("inprogress");
        $('#steps-point' + prevStep).addClass("inprogress").removeClass("completed");

        // If returning to social media step (step 0), restore post type visibility
        if (prevStep == 0) {
            setTimeout(function() {
                restorePostTypeVisibility();
            }, 100);
        }
    }

    console.log('<<<<<<<<<<<<<<<<<<<');
    console.log('Going back:');
    console.log('current step number: ' + current_step_number);

    if (current_step_number == STEP_MARKETPLACE_SELECT_INFLUENCER) {
        var formData = $('#form-create-campaign').serializeArray();
        var jsonObject = {};
        $.each(formData, function(i, field) {
            jsonObject[field.name] = field.value;
        });

        jsonObject.media = jsonObject.mp_socialmedia;
        jsonObject.type = jsonObject.campaign_type;
        jsonObject.select_type = jsonObject.mp_socialmedia_type2;

        $.ajax({
            url: window.Laravel.urls.getInfluencers,
            data: jsonObject
        })
        .done(function(data) {
            $("#latest-influencer-lists").html(data);
            $('#compaingTitle').html($('#campaign_title').val());

            // Restore selected influencers
            $(".selected-influencer-contr .influencer-detail").each(function(index) {
                var selected_attribute = $(this).attr("data-id");
                $(".influencer-list .influencer-detail[data-id=" + selected_attribute + "]").addClass("selected");
                $(".influencer-list .influencer-detail[data-id=" + selected_attribute + "] button.select-button")
                    .text("UNSELECT")
                    .removeClass("select-button")
                    .addClass("unselect-button");

                const toggleImage = document.getElementById(`card_clickitfame_logo_${selected_attribute}`);
                if (toggleImage) {
                    toggleImage.src = window.Laravel.assets.icons.clickitfameWhite;
                }
            });

            // Initialize load more functionality based on screen size
            setTimeout(function() {
                if (typeof $.fn.simpleLoadMore !== 'undefined') {
                    if ($(window).width() > 1399) {
                        $('.influencer-list').simpleLoadMore({
                            item: '.influencer-detail',
                            count: 15,
                            counterInBtn: true,
                            btnText: 'View More {showing}/{total}',
                        });
                    } else if ($(window).width() > 1024) {
                        $('.influencer-list').simpleLoadMore({
                            item: '.influencer-detail',
                            count: 12,
                            counterInBtn: true,
                            btnText: 'View More {showing}/{total}',
                        });
                    } else if ($(window).width() > 991) {
                        $('.influencer-list').simpleLoadMore({
                            item: '.influencer-detail',
                            count: 9,
                            counterInBtn: true,
                            btnText: 'View More {showing}/{total}',
                        });
                    } else if ($(window).width() > 767) {
                        $('.influencer-list').simpleLoadMore({
                            item: '.influencer-detail',
                            count: 6,
                            counterInBtn: true,
                            btnText: 'View More {showing}/{total}',
                        });
                    } else {
                        $('.influencer-list').simpleLoadMore({
                            item: '.influencer-detail',
                            count: 12,
                            counterInBtn: true,
                            btnText: 'View More {showing}/{total}',
                        });
                    }
                }
            }, 500);
        })
        .fail(function(xhr, status, error) {
            console.error("Failed to load influencers:", error);
        });
    }

    $(".alert-select-option").addClass("d-none");
});

$(document).ready(function() {
    $(document).on('change', '.checkFilter', function() {
        getVenueAjax();
    });
});

$(document).on('click', '.resetFilter', function(e) {
    $('#sort_by option[value=""]').attr('selected', 'selected');
    $('#target_age option[value=""]').attr('selected', 'selected');
    $('#language option[value=""]').attr('selected', 'selected');
    $('#content_attracts option[value=""]').attr('selected', 'selected');
    $('#hashtags').val(null).trigger('change');
    $('#influencer_type option[value=""]').attr('selected', 'selected');
    $('#gender option[value=""]').attr('selected', 'selected');
    $('#target_gender option[value=""]').attr('selected', 'selected');
    $('#rank option[value=""]').attr('selected', 'selected');

    $('#followers option[value=""]').attr('selected', 'selected');
    $('#amount-followers').val('0');
    $('#amount-price').val('0');
    getVenueAjax();
});

$('.nuwidth-dynamic.proba.dvambers').keyup(function() {
    this.value = this.value.replace(/[^0-9\.]/g, '');
});

$(document).on("change", "#selected-files", function() {
    var numFiles = $("input", this)[0].files.length;
});

$(document).on("click", ".button-Request", function() {
    if (
        selectedInfluencerIds.length == 0 ||
        $(".marketplace-finish .form-shadow-box table tbody").children().length == 0        
    ) {
        toastr.error("Please select at least one influencer before proceeding.");
        return false;
    }

    $("#pageLoader").show();

    // Ensure form is valid before submission
    const $form = $(this).closest('form');
    if ($form.length && typeof $form.parsley === 'function') {
        return $form.parsley().validate();
    }
});

function showStepContent(stepNumber) {
    $('#create-campaign-step-' + stepNumber).removeClass('d-none').addClass('d-block');
    if (stepNumber >= STEP_SELECT_SOCIAL_MEDIA) {
        $('#create-campaign-steps-3-to-7').removeClass('d-none').addClass('d-block');
        $('#steps-point-' + (stepNumber - 1)).addClass("completed").removeClass("inprogress");
        $('#steps-point-' + stepNumber).addClass("inprogress").removeClass("completed");
    }
}

function hideStepContent(stepNumber) {
    $('#create-campaign-step-' + stepNumber).removeClass('d-block').addClass('d-none');
    if (stepNumber < STEP_SELECT_SOCIAL_MEDIA) {
        $('#create-campaign-steps-3-to-7').removeClass('d-none').addClass('d-block');
        $('.steps-point').removeClass("completed").removeClass("inprogress");
    }
}

// Add validation function for campaign type selection
function validateCampaignTypeSelection() {
    if (!$('input[name="campaign_type"]:checked').length) {
        $(".alert-select-option").removeClass("d-none");
        return false;
    }
    return true;
}

// Helper functions for calculations
function calculatePlatformFee(subtotal) {
    let fee = (subtotal * ADMIN_COMMISSION_RATE) / 100;
    return fee < MIN_PLATFORM_FEE ? MIN_PLATFORM_FEE : fee;
}

function parseFloatSafe(value) {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? 0 : parsed;
}

function parseIntSafe(value) {
    const parsed = parseInt(value);
    return isNaN(parsed) ? 0 : parsed;
}

function formatNumberWithThousandSeparator(value) {
    return parseFloatSafe(value).toLocaleString('de-DE', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function updateTotals() {
    // Get values from data attributes instead of text
    const subtotal = parseFloatSafe($('#subtotal').data('subtotal'));
    const follower = parseIntSafe($('#follower').data('followers'));
    
    // Update all subtotal and follower displays with formatted text and data attributes
    $('#subtotal_in_payment_page').text(formatNumberWithThousandSeparator(subtotal)).data('subtotal', subtotal);
    $('#follower_in_payment_page').text(follower).data('followers', follower);
    
    // Calculate platform fee
    const platformFee = calculatePlatformFee(subtotal);
    const platformFeeVAT = platformFee * VAT_RATE;
    
    // Calculate total VAT from selected influencers
    let influencerVAT = 0;
    $(".selected-influencer-contr .influencer-detail").each(function() {
        const price = parseFloatSafe($(this).find(".user_price-val").data("price"));
        const isSmallBusiness = Boolean(parseInt($(this).find("#is_small_business_owner").text() || "0"));
        if (!isSmallBusiness) {
            influencerVAT += price * VAT_RATE;
        }
    });
    
    const totalVAT = influencerVAT + platformFeeVAT;
    const totalPrice = subtotal + platformFee + totalVAT;
    
    // Update all price displays with formatted text and data attributes
    $('#vat_final_new').text(formatNumberWithThousandSeparator(totalVAT)).data('vat', totalVAT);
    $('#total_final').text(formatNumberWithThousandSeparator(totalPrice)).data('total', totalPrice);
    $('#total_final_new').text(formatNumberWithThousandSeparator(totalPrice)).data('total', totalPrice);
    $('#fee_final').text(formatNumberWithThousandSeparator(platformFee)).data('fee', platformFee);
    $('#fee_final_new').text(formatNumberWithThousandSeparator(platformFee)).data('fee', platformFee);
    $('#total_input').val(totalPrice.toFixed(2));
    $('#fee_input').val(platformFee.toFixed(2));
}

// Function to reset social media step radio button states
function resetSocialMediaStep() {
    // Uncheck all social media radios
    $('input[name="mp_socialmedia"]').prop('checked', false);

    // Uncheck all post type radios
    $('input[name="mp_socialmedia_type2"]').prop('checked', false);

    // Remove active class from social media options
    $(".social_media_radio").removeClass("active");

    // Hide all post type containers
    $(".media_platform_post_type").hide();

    // Hide all post type option groups
    $(".media_platform_post_type .get_type").hide();

    // Clear error messages
    $("#error-select-social-media").text("");
    $("#error-post-type").text("");

    // Remove validation attributes
    $('input[name="mp_socialmedia_type2"]').removeAttr('required')
        .removeAttr('data-parsley-errors-container')
        .removeAttr('data-parsley-error-message');
}

// Function to update post type validation based on visible options
function updatePostTypeValidation() {
    // Remove validation from all post type radios first
    $('input[name="mp_socialmedia_type2"]').removeAttr('required')
        .removeAttr('data-parsley-errors-container')
        .removeAttr('data-parsley-error-message');

    // Add validation to the first visible post type radio
    var firstVisiblePostType = $('.media_platform_post_type .get_type:visible input[name="mp_socialmedia_type2"]').first();
    if (firstVisiblePostType.length) {
        firstVisiblePostType.attr('required', 'required')
            .attr('data-parsley-errors-container', '#error-post-type')
            .attr('data-parsley-error-message', 'Please select post type.');
    }
}

// Function to show appropriate post type options based on campaign/boost type
function showPostTypeOptions() {
    var selectedBoostType = $('input[name="boost_me_post_type"]:checked').val();
    var selectedCampaignType = $('input[name="campaign_type"]:checked').val();

    // console.log('Showing post type options for boost type:', selectedBoostType, 'campaign type:', selectedCampaignType);

    // Hide all post type options first
    $(".media_platform_post_type .get_type").hide();

    // Clear any previous selections
    $('input[name="mp_socialmedia_type2"]').prop('checked', false);

    if (selectedBoostType) {
        $(".media_platform_post_type .get_type." + selectedBoostType).show();
    } else if (selectedCampaignType) {
        var campaignClass = selectedCampaignType.toLowerCase().replace(' ', '-');
        $(".media_platform_post_type .get_type." + campaignClass).show();
    }

    // Update validation (no auto-selection)
    updatePostTypeValidation();

    // console.log('Visible post type options:', $('.media_platform_post_type .get_type:visible').length);
}

// Function to show appropriate post type options while preserving existing selections
function showPostTypeOptionsPreserveSelection() {
    var selectedBoostType = $('input[name="boost_me_post_type"]:checked').val();
    var selectedCampaignType = $('input[name="campaign_type"]:checked').val();

    // console.log('Showing post type options (preserving selection) for boost type:', selectedBoostType, 'campaign type:', selectedCampaignType);

    // Store current selection before hiding options
    var currentSelection = $('input[name="mp_socialmedia_type2"]:checked').val();

    // Hide all post type options first
    $(".media_platform_post_type .get_type").hide();

    // Show appropriate options based on selection
    if (selectedBoostType) {
        $(".media_platform_post_type .get_type." + selectedBoostType).show();
    } else if (selectedCampaignType) {
        var campaignClass = selectedCampaignType.toLowerCase().replace(' ', '-');
        $(".media_platform_post_type .get_type." + campaignClass).show();
    }

    // Restore the previous selection if it's still visible
    if (currentSelection) {
        var restoredRadio = $('.media_platform_post_type .get_type:visible input[name="mp_socialmedia_type2"][value="' + currentSelection + '"]');
        if (restoredRadio.length) {
            restoredRadio.prop('checked', true);
            // console.log('Restored post type selection:', currentSelection);
        }
    }

    // Update validation
    updatePostTypeValidation();

    // console.log('Visible post type options (preserved):', $('.media_platform_post_type .get_type:visible').length);
}

// When returning to the social media step, restore post type visibility if social media is already selected
function restorePostTypeVisibility() {
    var selectedSocialMedia = $('input[name="mp_socialmedia"]:checked');
    if (selectedSocialMedia.length) {
        // Show the post type container for the selected social media
        var postTypeContainer = selectedSocialMedia.closest(".media_platform").next(".media_platform_post_type");
        postTypeContainer.css("display", "flex");

        // Preserve existing post type selection
        var selectedPostType = $('input[name="mp_socialmedia_type2"]:checked').val();

        // Show appropriate post type options without clearing selections
        showPostTypeOptionsPreserveSelection();
    }
}

function getVenueAjax() {
    const dataString = $("#form-create-campaign").serialize();
    $.ajax({
        url: window.Laravel.urls.marketStepFilter,
        data: dataString
    }).done(function(data) {
        $("#latest-influencer-lists").html(data);
        
        $(".selected-influencer-contr .influencer-detail").each(function() {
            const selected_attribute = $(this).attr("data-id");
            $(".influencer-list .influencer-detail[data-id=" + selected_attribute + "]").addClass("selected");
        });
        
        $(".select").select2();
        $("#hashtags").select2({
            placeholder: "Select",
            multiple: true
        });
    }).fail(function(xhr, status, error) {
        console.error("AJAX request failed:", error);
        toastr.error("Failed to load influencers. Please try again.");
    });
}