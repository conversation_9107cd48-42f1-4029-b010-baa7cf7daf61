.header-logo img {
    width: 255px;
    height: auto;
}

@media (max-width: 800px) {
    .header-logo img {
        width: 216px;
        height: auto;
    }
}

@media (max-width: 500px) {
    .header-logo img {
        width: 150px;
        height: auto;
    }
}

@media only screen and (max-width: 1920px) {
    .container,
    .container-lg,
    .container-md,
    .container-sm,
    .container-xl,
    .container-xxl {
        max-width: 1200px;
    }
    .language-section ul li a,
    .language-section ul {
        font-size: 18px;
    }
    .language-section img {
        width: 22px;
        height: 22px;
    }
    .soon-dropdown .btn-group > img {
        width: 22px;
    }
    .comming-soon-box {
        padding: 0;
    }
    .comming-soon-box .logo img {
        width: 380px;
    }
    .home-heading {
        font-size: 28px;
        padding-top: 26px;
        padding-bottom: 34px;
    }
    .comming-soon-loader {
        width: 741px;
        height: 54px;
        margin: 40px auto 63px;
    }
    .comming-soon-loader:before {
        width: 50%;
        height: 48px;
        left: 1px;
    }
    .custom-loader {
        font-size: 20px;
    }
    .comming-step-box img {
        max-height: 50px;
        margin-bottom: 23px;
    }
    .comming-step-box-heading {
        font-size: 18px;
    }
    .comming-step-box-text {
        font-size: 15px;
    }
    button.apply-btn {
        width: 238px;
        height: 44px;
        font-size: 14px;
        margin: 54px 16px 0;
    }
    .comming-soon-box {
        padding: 40px;
    }
    .home-comming-soon:before {
        width: 1092px;
        height: 1092px;
    }
    .comming-soon-loader:before {
        width: 50%;
        height: 40px;
        left: 2px;
        margin: auto;
    }
}
@media only screen and (max-width: 1499px) {
    .customerSer {
        padding: 25px 25px;
    }
    table.connectPrising tr.campningDiv > td {
        padding: 15px 13px 15px;
    }
    h1.dashHeading {
        font-size: 29px;
        line-height: 36px;
        margin-bottom: 20px;
        padding: 16px 17px;
    }
    .menuoverlay {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: transparent;
        position: fixed;
        display: none;
        z-index: 11;
        background: #00000046;
    }
    .dashboard-left {
        width: 50px;
    }
    .dashboard-left.open {
        width: 286px;
    }
    .dashboard-right {
        margin-left: 50px;
    }
    .side-barlogo img.iconLogo {
        display: block;
    }
    .side-barlogo img.fullLogo {
        display: none;
    }
    .open .side-barlogo img.iconLogo {
        display: none;
    }
    .open .side-barlogo img.fullLogo {
        display: block;
    }
    .dashboardsidebar .ativeLink {
        display: flex;
        width: 50px;
        flex-direction: column-reverse;
    }
    .open .dashboardsidebar .ativeLink {
        background: transparent;
        align-items: center;
        justify-content: center;
        flex-direction: row;
        width: auto;
    }
    .ativeLink .form-check {
        width: 50px;
        flex: auto;
        overflow: hidden;
    }
    .open .ativeLink .form-check {
        width: 183px;
        flex: auto;
        overflow: hidden;
    }
    .ativeLink .form-check input.form-check-input ~ label {
        padding: 11px 5px;
        border-radius: 0;
        width: 50px;
    }
    .open .ativeLink .form-check input.form-check-input ~ label {
        height: auto;
        content: "";
        display: inline-block;
        color: #fff;
        font-size: 15px;
        line-height: 20px;
        margin: 0;
        padding: 15px 8px;
        text-align: center;
        font-weight: 500;
        letter-spacing: 1.5px;
        cursor: pointer;
        overflow: hidden;
        width: 183px;
        box-shadow: 0px 10px 20px rgba(35, 170, 35, 0.1);
        border-radius: 15px;
    }
    .dashboardsidebar ul a {
        padding: 6px;
        margin: 7px auto;
        padding: 0;
        width: 40px;
        height: 40px;
        border-radius: 5px;
        justify-content: center;
    }
    .open .dashboardsidebar ul a {
        padding: 15px 27px;
        margin: 0 10px;
        display: flex;
        align-items: center;
        width: calc(100% - 20px);
        border-radius: 25px;
        height: auto;
    }
    .dashboardsidebar ul a img {
        height: 22px;
        width: auto;
        margin: 0 auto;
    }
    .open .dashboardsidebar ul a img {
        width: 18px;
        height: 18px;
        margin-right: 12px;
    }
    span.lki {
        width: 0;
        overflow: hidden;
        display: none !important;
    }
    .open span.lki {
        width: 100%;
        display: block !important;
    }
    .ativeLink .form-check input.form-check-input ~ label span {
        display: none;
    }
    .open .ativeLink .form-check input.form-check-input ~ label span {
        display: inline-block;
    }
    .ativeLink .form-check input.form-check-input ~ label i {
        margin-right: 0;
        font-size: 20px;
        display: inline-block;
        padding: 1px 5px;
    }
    .open .ativeLink .form-check input.form-check-input ~ label i {
        font-size: 14px;
        padding: 0px 2px;
    }
    .ativeLink {
        padding: 0;
    }
    .open .ativeLink {
        padding: 7px 15px;
    }
    .toggleLink {
        padding: 9px 0px;
        margin: 0 0 8px;
    }
    .open .toggleLink {
        padding: 0;
        margin-left: 20px;
    }
    .side-barlogo {
        width: 224px;
        margin: 0 auto;
        padding: 25px 0;
        transition: all 0.3s;
    }
    .open .side-barlogo {
        width: 224px;
    }
    .side-barlogo {
        width: auto;
    }
    a.button-ccg {
        width: 40px;
        height: 44px;
        border-radius: 12px;
        background: transparent;
        padding: 0;
    }
    .button-ccg span.hide-clp {
        display: none;
    }
    .open .button-ccg span.hide-clp {
        display: block;
    }
    .button-ccg img {
        display: block;
        width: 100%;
        height: 100%;
    }
    .open .button-ccg img {
        display: none;
    }
    .dashboardsidebar ul li.home-link {
        flex-direction: column-reverse;
    }
    .open .dashboardsidebar ul li.home-link {
        flex-direction: row;
    }
    .toggleLink {
        padding: 9px 0px;
        margin: 7px auto;
    }
    .open a.button-ccg {
        width: auto;
        background: linear-gradient(
            97.12deg,
            #AD80FF 19.01%,
            #f86988 48.05%,
            #ad80ff 82.21%
        );
        border-radius: 12px;
        padding: 0 36px;
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        color: #ffffff;
        height: 44px;
        display: inline-block;
        line-height: 42px;
        transition: all 0.9s;
        margin: 0 auto;
    }
    .open a.button-ccg:hover {
        background: linear-gradient(
            97.12deg,
            #AD80FF 100%,
            #f86988 0%,
            #ad80ff 0%
        );
        color: #fff;
    }
    .dashboardsidebar ul a.havesubmenu:after {
        display: none;
    }
    .open .dashboardsidebar ul a.havesubmenu:after {
        display: block;
    }
    .menusal {
        position: absolute;
    }
    .open .menusal {
        position: static;
        width: 100%;
    }
    .menusal.opwnMenu {
        display: block;
        width: 250px;
    }
    .open .menusal.opwnMenu {
        width: 100%;
    }
    .ing-io {
        display: inline-block;
        width: auto;
        position: relative;
    }
    #new_steps3 .step-nevigationbutton.fixed {
        bottom: 18px;
        width: calc(100% - 169px);
        left: 89px;
    }
}

/*1499px*/
@media only screen and (max-width: 1399px) {
    .userDetailImage {
        width: 40px;
        height: 40px;
        flex: 0 0 40px;
    }
    span.infoName {
        font-size: 14px;
    }
    span.infoStar {
        font-size: 14px;
    }
    label {
        font-size: 14px;
    }
    .ser_op > span.numberTab {
        padding: 9px 0 1px;
    }
    .steps.selectedUserOuter {
        position: fixed;
        bottom: 0;
        right: 21px;
        background: #fff;
        width: 320px;
        margin: 0;
        padding: 13px 12px;
        box-shadow: 0 0 32px 0 rgb(27 58 146 / 52%);
        max-height: 658px;
        overflow: auto;
        border-radius: 13px 13px 0 0;
    }
    .divOnet {
        flex: 0 0 100%;
    }
    .divSeco {
        position: fixed;
        right: 0;
        bottom: 0;
    }
    .SelectedUserOpen {
        background: rgb(255 245 243);
        border: solid 2px rgb(255 245 243);
        color: #000000;
        border-radius: 5px;
        font-size: 18px;
        line-height: normal;
        padding: 7px 18px;
        display: block;
        text-align: center;
        cursor: pointer;
        background: rgb(255 245 243);
        border-radius: 20px !important;
        border: 1px solid #e8eff6;
        display: flex;
        padding: 10px 14px;
        align-items: center;
        justify-content: center;
    }
    .SelectedUserOpen span#userCnt {
        min-width: 31px;
        display: inline-block;
        font-size: 15px;
        min-height: 16px;
        padding: 4px 2px;
        font-weight: normal;
        margin-left: auto;
        border-radius: 50%;
    }
    .steps.selectedUserOuter .markeplaceRequest {
        display: none;
        box-shadow: none;
        border: 0;
    }
    .steps.selectedUserOuter .markeplaceRequest {
        display: none;
    }
    .steps.selectedUserOuter.openFilter .markeplaceRequest {
        display: block;
    }
    .selectedUser {
        max-height: fit-content;
        overflow: auto;
    }
    .influencer .steps.selectedUserOuter {
        position: static;
        width: 100%;
        background: transparent;
        box-shadow: none;
        height: auto;
        max-height: unset;
    }
    .influencer .steps.selectedUserOuter .markeplaceRequest {
        display: block;
    }
    .steps-section {
        padding: 19px 19px;
    }
}
/*1299px*/
@media screen and (max-width: 1299px) {
    /* new css for desktop and mobile  */
    .banner-title h3.title {
        font-size: 29px;
    }
    .orText span {
        font-size: 15px;
        line-height: 21px;
        padding: 7px 0 0;
    }
    .ramt header.site-header.rink-header {
        padding: 7px 0 !important;
    }
    .dashboard-right header.site-header {
        margin: 24px 20px;
    }
    .steps-section,
    .step_form_custom {
        margin-bottom: 24px;
    }
    .step_form {
        margin-top: 24px;
    }
    .ativeLink .form-check {
        position: relative;
        height: auto;
        min-height: unset;
        margin: 0;
        padding: 0;
        width: 50px;
        flex: auto;
        overflow: hidden;
    }
    .colOffline .ativeLink .form-check input.form-check-input ~ label,
    .colOnline .ativeLink .form-check input.form-check-input ~ label {
        padding: 11px 5px;
        border-radius: 0;
        width: 50px;
    }
    .colOffline .ativeLink .form-check input.form-check-input ~ label i,
    .colOnline .ativeLink .form-check input.form-check-input ~ label i {
        margin-right: 0;
        font-size: 20px;
        display: inline-block;
        padding: 1px 5px;
    }
    .ativeLink .form-check input.form-check-input ~ label span {
        display: none;
    }
    .dashboardsidebar ul a {
        padding: 17px 9px;
    }
    .open .dashboardsidebar .ativeLink {
        flex-direction: unset;
    }
    .open .dashboardsidebar .ativeLink {
        flex-direction: unset;
        width: auto;
        padding: 7px 15px;
        align-items: center;
        justify-content: center;
    }
    .open .ativeLink .form-check {
        width: 183px;
    }
    .open .ativeLink .form-check input.form-check-input ~ label {
        width: 183px;
        border-radius: 10px;
        padding: 15px 8px;
    }
    .open .ativeLink .form-check input.form-check-input ~ label i {
        font-size: 14px;
        padding: 0px 2px;
        margin-right: 7px;
    }
    .open .ativeLink .form-check input.form-check-input ~ label span {
        display: inline-block;
    }
    .open span.lki {
        width: auto;
    }
    .page_tab {
        padding: 26px 19px;
    }
    .campningDiv img {
        height: 17px;
    }
    .campHistory .campningDiv img {
        height: 100%;
    }
    .connectPrising table td.taskName {
        font-size: 15px;
    }
    span.timing {
        font-size: 10px;
    }
    table.connectPrising tr.campningDiv > td {
        padding: 8px 10px 18px;
    }
    .custDetail .usernamewidth {
        width: 113px;
    }
    .soclPrice {
        font-size: 13px;
    }
    h1.dashHeading {
        font-size: 21px;
        line-height: 32px;
        margin-bottom: 11px;
        padding: 16px 17px;
    }
    table.connectPrising {
        border-spacing: 0 20px !important;
    }
    span.sertp {
        max-width: 122px;
    }
    span.timing {
        width: 146px;
    }
    span.acce,
    span.reje,
    span.pend {
        width: 130px;
    }
    .table-btn {
        margin-left: 0;
        margin-right: 0;
    }
    .ord {
        width: auto;
    }
    .modal-lg,
    .modal-xl,
    .confirm-content .modal-lg,
    .confirm-content .modal-xl {
        max-width: 90%;
        margin: 30px auto;
    }
    .data-table.cac {
        max-width: 100%;
    }
    .steps_con .step-nevigationbutton {
        margin: 23px 0 0;
    }
    .steps_con {
        margin: 0 20px 20px;
        padding: 1px 19px 22px;
    }
    .influencer-detail {
        width: calc(25% - 30px);
    }
    .social_media_radio span.media_platform {
        padding-left: 34px;
    }
    .media_platform label {
        font-size: 18px;
    }
    .media_platform label img {
        max-width: 45px;
        max-height: 45px;
    }
    .campaign-type-share-information {
        line-height: normal;
        padding-top: 0;
    }
    .position-relative.otrcm .social_media_radio {
        min-height: unset;
    }
    .home-comming-soon:before {
        content: "";
        background: rgba(254, 254, 252, 1);
        width: 86vw;
        height: 0;
        flex-shrink: 0;
        border-radius: 1280px;
        background: #fefefc;
        filter: blur(40px);
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        margin: auto;
        padding-top: 86vw;
    }
    .influencer-filter button {
        width: auto;
        height: 51px;
        padding-right: 26px;
        padding-left: 26px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    .influencer-filter button img {
        position: static;
        margin-left: 10px;
    }
    .filter-buttons span.select2.select2-container.select2-container--default {
        width: 176px !important;
        margin: 0 11px 17px;
    }
    .filter-form .form-group {
        flex: 0 0 calc(25% - 30px);
    }
} /*1299*/

@media screen and (max-width: 1199px) {
    /* new css for desktop and mobile  */
    span.tabText {
        font-size: 14px;
    }
    .connectWithInner {
        padding: 13px 13px;
    }
    .socialCnt {
        width: 60px;
        height: 60px;
        padding: 0;
    }
    .connectWithInner {
        padding: 13px;
    }
    .influencer-detail {
        width: calc(33.33% - 30px);
    }
    .steps-section h2 {
        font-size: 30px;
        line-height: normal;
        margin: 0;
        padding: 0 10px;
    }
    .social_media_radio {
        flex-direction: column;
        align-items: baseline;
        justify-content: flex-start;
    }
    .media_platform label img {
        margin-top: 0;
        margin-bottom: 0;
    }
    .social_media_radio span.media_platform {
        flex: auto;
        height: 20px;
        flex: 0 0 72px;
    }
    tags.tagify.tagify--outside {
        margin: 0;
    }
    .home-heading {
        font-size: 32px;
        padding-top: 41px;
    }
    .comming-soon-loader {
        width: 827px;
        height: 55px;
        margin: 45px auto 58px;
    }
    .comming-soon-loader:before {
        width: 50%;
        height: 46px;
    }
    .comming-soon-box .logo img {
        width: 379px;
    }
    .comming-soon-box {
        padding: 60px 0;
    }
    .comming-soon-loader {
        width: 713px;
    }
    .bannersection {
        height: auto;
        padding-top: 139px;
    }
    .pageSection .pageSectionImage:before {
        right: 72px;
    }
    .pageSectioncontent {
        flex: 0 0 calc(100% - 322px);
        padding-left: 42px;
        padding-right: 42px;
    }
    .task-div-outer .form-group {
        width: 50%;
        flex: 0 0 calc(50% - 21px);
    }
    .task-div-outer {
        gap: 42px;
        width: 100%;
    }
}
/*1199*/
@media screen and (max-width: 1099px) {

    .inside-table.request-content {
		padding: 33px 64px 75px;
		gap: 44px calc(33.33% - 204px);
	}

	.request-popup .modal-dialog.default-width {
		max-width: 90%;
	}

	.request-content-data.icon-before {
		gap: 34px 50px;
		padding: 29px 67px 46px;
	}

	.request-content-data .inside-table-row {
		width: calc(50% - 25px);
	}
}
/*1099px*/
@media screen and (max-width: 999px) {

}
@media screen and (max-width: 991px) and (min-width: 768px) {

    .influncerdetailpopup .modal-lg {
        max-width: 96%;
    }
    .mobile-menu .image_more{
        height: 42px;
        width:50px;
    }
}
/*999px*/
@media screen and (max-width: 991px) {
    .header-logo {
        height: 38px;
    }
    ul.h-menu a {
        padding: 2px 5px;
    }
    ul.h-menu li {
        margin-left: 8px;
    }
    .banner-title {
        padding-right: 0;
    }
    span.serIcon {
        font-size: 1.6rem;
        width: 63px;
        height: 60px;
        flex: 0 0 60px;
    }
    span.serName {
        font-size: 14px;
        padding: 0 0 0 16px;
        width: auto;
    }
    .aboutSection,
    .influncers,
    .counterSection {
        padding: 40px 0;
    }
    .sectionTitle {
        font-size: 33px;
    }
    .sectiontext {
        padding-right: 0;
    }
    .sectionImage img {
        width: 100%;
    }
    .-layout .blogBox,
    .-layout .userDetails {
        margin-top: 2rem;
    }
    .counterSection .sectiontext {
        max-width: 100%;
    }
    div#counter .conterAll {
        flex: 0 0 25%;
        padding: 14px 0;
    }
    .counter-value {
        font-size: 30px;
        line-height: 34px;
    }
    div#counter {
        margin: 45px auto 0;
    }
    ul.h-menu li {
        font-size: 16px;
    }
    .servicesSection {
        padding: 26px 23px;
        width: auto;
    }
    .markeplaceUsers.markeplaceUserList {
        max-width: 730px;
        margin: 0 auto 32px;
    }
    .steps.filter {
        display: none;
    }
    .steps.filter.openFilter {
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        margin: 0;
        z-index: 1;
        background: rgb(23 25 54 / 40%);
        border-radius: 0;
        overflow: auto;
    }
    .steps.filter.openFilter .markeplaceSidebar {
        background: #fff;
        max-width: 90%;
        margin: 87px auto 139px;
        padding: 37px;
        border-radius: 15px;
        width: 500px;
        position: relative;
    }
    span#closeFilter {
        position: absolute;
        right: 18px;
        top: 9px;
    }
    .openFilterBtn.d-block.d-lg-none {
        width: 130px;
        height: 37px;
        border-radius: 15px;
        border: 1px solid #3cc23c;
        font-style: normal;
        font-weight: 800;
        font-size: 13px;
        text-align: center;
        color: #ffffff;
        transition: all 0.3s;
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        display: inline-flex;
        margin: 0;
        letter-spacing: 0;
        white-space: normal;
        line-height: 34px;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        background: #3cc23c;
    }
    .wizardPopup {
        max-width: 90%;
        padding-top: 0;
    }
    .connectWithInner {
        padding: 26px 10px;
        display: inline-flex !important;
        width: 100%;
        margin: 15px 0px;
    }
    .informationDiv {
        padding: 23px 0;
    }
    hr {
        margin: 12px 13px !important;
        width: calc(100% - 25px) !important;
    }
    table .form-group {
        margin: 0;
    }
    .connectPrising table td {
        padding: 12px 15px;
        width: calc(100% - 12px);
    }
    .image {
        height: 465px;
    }
    .steps_con {
        padding: 0;
        box-shadow: none;
        border-radius: 0;
    }
    .page_tab .steps {
        margin: 0;
        padding: 16px;
    }
    .form-group {
        margin: 0;
        margin-bottom: 20px;
    }
    .mediabox {
        justify-content: start;
        overflow: auto;
    }
    .select_media {
        flex: 0 0 307px;
    }
    .checkone.d-flex .iconbox {
        position: absolute;
        left: 0;
        top: 0;
    }
    .myDiv {
        position: relative;
    }
    .socialCnt.nmt {
        margin: 18px 0 0 15px;
        padding: 0;
        background: transparent;
    }
    .sahrePriceHeading {
        padding-left: 0;
    }
    .inputs {
        max-width: 50%;
        flex: 0 0 50%;
    }
    .sahrePrice {
        width: 100%;
    }
    .myDiv {
        border-bottom: solid 2px #e7e7e7;
        padding: 20px 0;
        background: #008000b3;
        padding: 18px 17px 20px;
        width: 100%;
        margin: 14px 0;
        border-radius: 24px;
    }
    .markeplaceUserCheck {
        width: 100%;
    }
    .markeplaceUserSidebar {
        position: absolute;
    }
    .mobile-menu {
        display: block;
    }
    .dashboardsidebar {
        display: none;
    }
    .dashboard-left {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: auto;
        top: auto;
        padding: 0;
        border: 0;
    }
    .dashboard-right {
        margin: 0;
        padding-bottom: 130px;
    }
    .extra-menu {
        display: none;
    }
    .mobile-menu ul {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0 30px;
        justify-content: space-between;
    }
    .mobile-menu ul li {
        margin: 0 8px;
        flex: 0 0 calc(20% - 16px);
    }
    .mobile-menu ul li img {
        height: 30px;
    }
    .mobile-menu ul li span.lki {
        width: auto;
        display: block !important;
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: 20px;
        display: flex;
        align-items: center;
        text-align: center;
        color: #000000;
        margin-top: 5px;
    }
    .mobile-menu ul li a.active span {
        color: #AD80FF;
    }
    .mobile-menu ul li .active img {
        filter: brightness(0) saturate(100%) invert(30%) sepia(90%) saturate(5000%) hue-rotate(258deg) brightness(110%) contrast(105%);
    }
    .mobile-menu ul li a.active span.lki:after,
    .mobile-menu.open-menu ul li.extramenu a span.lki:after {
        content: "";
        width: 70px;
        height: 70px;
        background: #AD80FF;
        position: absolute;
        border-radius: 50%;
        border: solid 0px rgba(253, 155, 141, 1);
        left: 0;
        right: 0;
        margin: auto;
        bottom: -54px;
    }
    .mobile-menu ul li a.active span.lki:before,
    .mobile-menu.open-menu ul li.extramenu a span.lki:before {
        content: "";
        width: 90px;
        height: 90px;
        background: rgba(253, 155, 141, 0.4);
        position: absolute;
        border-radius: 50%;
        left: 0;
        right: 0;
        margin: auto;
        bottom: -67px;
    }
    .mobile-menu ul li a {
        display: block;
        padding: 28px 0;
        margin-top: 28px;
        padding-top: 0;
        text-align: center;
        position: relative;
        overflow: visible;
        width: 100%;
    }
    .menu-influencer {
        display: flex;
        flex-direction: column-reverse;
    }
    .mobile-menu.open-menu .extra-menu {
        display: block;
    }
    .mobile-menu {
        border-bottom: 0;
    }
    .mobile-menu.open-menu {
        border-bottom: 0;
    }
    .mobile-menu.open-menu .extra-menu {
        display: block;
    }
    .mobile-menu.open-menu ul li.extramenu a span {
        color: #AD80FF;
    }
    .mobile-menu.open-menu ul li.extramenu a img {
        filter: brightness(0) saturate(100%) invert(30%) sepia(90%) saturate(5000%) hue-rotate(258deg) brightness(110%) contrast(105%);

    }
    .open-menu .menu-overlay {
        display: block;
        position: fixed;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        background: rgb(23 25 54 / 40%);
    }
    .menu-overlay {
        display: none;
    }
    .open-menu .menu-influencer {
        background: #fff;
        z-index: 1;
        position: relative;
        border-top-right-radius: 20px;
        border-top-left-radius: 20px;
    }
    .dashboard-right header.site-header .header-logo {
        display: block;
        height: auto;
        width: 300px;
        margin-right: auto;
    }
    .dashboard-right header.site-header {
        box-shadow: none !important;
        margin: 0;
        width: 100%;
        border-radius: 0;
    }
    .dashboard-right header.site-header .menus.ms-auto {
        width: auto;
    }
    .page_tab {
        margin: 0;
        box-shadow: none;
        border-radius: 0;
    }
    .dashboard-right header.site-header .container {
        max-width: 100%;
    }
    /* .dashboard-right header.site-header span.showUser {
        display: none;
    } */
    body {
        background-color: #fff !important;
    }
    tr.campningDiv table {
        display: grid;
        grid-template-areas:
            "data-task data-task data-task data-task"
            "data-soci data-user data-ptyp data-pric"
            "data-time data-butt data-butt data-butt";
        grid-template-columns: 24% 34% 28% 18%;
    }
    table.connectPrising tr.campningDiv table tbody,
    table.connectPrising tr.campningDiv table tbody tr {
        display: contents;
    }
    tr.campningDiv table td.taskName {
        grid-area: data-task;
    }
    tr.campningDiv table td.camp-button {
        grid-area: data-butt;
    }
    tr.campningDiv table td.firDaat {
        grid-area: data-soci;
    }
    tr.campningDiv table td.custDetail {
        grid-area: data-user;
    }
    tr.campningDiv table td.soclDetail {
        grid-area: data-ptyp;
    }
    tr.campningDiv table td.show-pricing {
        grid-area: data-pric;
    }
    tr.campningDiv table td.time-show {
        grid-area: data-time;
        align-items: start;
    }
    .table-btn {
        margin-right: 0;
        margin-top: 0;
        margin-bottom: 0;
        margin-left: 15px !important;
    }
    .connectPrising table td {
        display: flex;
        align-items: center;
    }
    .connectPrising table td.soclDetail {
        width: 100%;
    }
    .btn-div {
        width: 100%;
        flex-direction: row;
    }
    .influncerCol {
        max-width: 100%;
        margin-left: 00;
        display: flex;
        flex-direction: row-reverse;
        width: 100%;
    }
    input.table-btn.nobg-btn.ds {
        white-space: normal;
        line-height: normal;
    }
    .customerSer {
        border: 0;
        box-shadow: none;
    }
    tr.campningDiv table td.soclDetail > div,
    span.sertp,
    .custDetail .usernamewidth,
    tr.campningDiv table td.custDetail > div {
        max-width: unset;
    }
    span.sertp {
        white-space: normal;
    }
    table.connectPrising {
        margin: 15px 0;
    }
    .soclPrice {
        width: auto;
    }
    .desabled .datatip img {
        opacity: 1;
    }
    .accordion-item button table {
        display: grid;
        grid-template-areas:
            "acr-data-task acr-data-task acr-data-task acr-data-drpb"
            "acr-data-soci acr-data-ptyp acr-data-user acr-data-pric"
            "acr-data-cbtn acr-data-cbtn acr-data-cbtn acr-data-cbtn";
        grid-template-columns: 20% 34% 28% 18%;
    }
    .accordion-item button table tbody,
    .accordion-item button table tbody tr {
        display: contents;
    }
    .accordion-item button table tbody tr td.taskName {
        grid-area: acr-data-task;
    }
    .accordion-item button table tbody tr td.dropdown-button.camp-button {
        grid-area: acr-data-drpb;
    }
    .accordion-item button table tbody tr td.firDaat {
        grid-area: acr-data-soci;
    }
    .accordion-item button table tbody tr td.soclDetail {
        grid-area: acr-data-ptyp;
    }
    .accordion-item button table tbody tr td.custDetail {
        grid-area: acr-data-user;
    }
    .accordion-item button table tbody tr td.show-pricing {
        grid-area: acr-data-pric;
    }
    .accordion-item button table tbody tr td.camp-button {
        grid-area: acr-data-cbtn;
    }
    .connectPrising.inner
        .accordion-item
        .accordion-header
        button.accordion-button {
        padding: 10px 12px 6px;
    }
    .connectPrising table td.camp-button.dropdown-button img {
        width: 40px;
        height: auto;
        margin-right: 15px;
        margin-left: auto;
        margin-right: 0;
    }
    .accordion-item button table tbody tr td.camp-button .d-flex.btn-container {
        justify-content: start;
        margin-left: 0 !important;
    }
    .accordion-item
        button
        table
        tbody
        tr
        td.camp-button
        .d-flex.btn-container
        a.table-btn.nobg-btn.mx-3,
    span.timing {
        margin: 4px !important;
    }
    tr.accordian-table td {
        display: table-cell;
    }
    .nav-tabs.filtertab .nav-link {
        border-radius: 15px;
        font-size: 13px;
        width: 127.01px;
        margin: 0 2px;
    }
    .data-table table .accordian-table td > div > img {
        height: 22px;
    }
    .modal-dialog.default-width {
        max-width: 90%;
        margin: 30px auto;
    }
    .ontro {
        max-width: 91%;
        padding: 0 0 46px;
        margin: 0 auto;
    }
    .media-div img {
        width: 46px;
    }
    .admethod {
        margin-left: 21px;
    }
    .admethod > span {
        font-size: 26px;
    }
    .campningDiv.indata {
        margin: 16px 30px;
        padding: 14px 16px;
    }
    .post-text .post-title {
        width: 208px;
    }
    .confirm-submit .modal-dialog.modal-lg,
    .complaint-confirm-popup .modal-lg {
        max-width: 90%;
        margin: 30px auto;
    }
    .active ~ .menusal {
        display: none;
    }
    .active ~ .menusal.opwnMenu {
        display: block;
        position: absolute;
        bottom: 104%;
    }
    .active ~ .menusal.opwnMenu {
        display: block;
    }
    .active ~ .menusal.opwnMenu {
        position: absolute;
        left: 0;
    }
    .menusal {
        bottom: 100% !important;
        top: auto;
        width: 100%;
        left: 0;
    }
    .mobile-menu ul li .menusal a {
        margin: 0;
        padding: 0;
    }
    .mobile-menu ul li .menusal ul {
        display: flex;
        justify-content: start;
        flex-wrap: wrap;
    }
    .mobile-menu ul li .menusal ul li {
        flex: 0;
        margin: 5px;
    }
    .mobile-menu ul li .menusal ul li a {
        font-size: 14px;
        color: #fff;
        background: #000;
        padding: 4px 10px;
        border-radius: 5px;
        white-space: nowrap;
    }
    .menusal.opwnMenu {
        display: none;
    }
    .mobile-menu ul li a.toggleMenu ~ .menusal.opwnMenu,
    .mobile-menu ul li a.toggleMenu ~ .menusal {
        display: block;
        width: 100%;
        background: #fff;
        padding-bottom: 26px;
        margin-bottom: -14px;
        border-radius: 15px 15px 0 0;
    }
    .vod,
    .shareconent {
        margin: 0 -13px;
    }
    .steps.selectedUserOuter {
        bottom: 90px;
    }
    .steps.selectedUserOuter.openFilter {
        height: calc(100vh - 145px);
    }
    .mobile-menu ul li a {
        padding: 15px 0 22px;
        margin-top: 16px;
        padding-top: 0;
        width: 100%;
        position: relative;
        overflow: hidden;
    }
    .contentAllWz {
        align-items: center;
    }
    .vizrdSelection {
        margin-top: 0;
    }
    .wrapper label {
        display: block;
        cursor: pointer;
        width: 29px;
        margin: -11px 0 7px;
    }
    .wrapper {
        width: 147px;
    }
    .wrapper-outer.position-relative {
        width: 150px;
    }
    .mobile-menu ul li a.active span.reqCont {
        color: #fff !important;
    }
    .mobile-menu ul li a.active span.reqCont:before {
        display: none;
    }
    .mobile-menu ul li a.active span.reqCont:after {
        display: none;
    }
    .paymentPagenumber {
        max-width: calc(50% - 12px);
    }
    .payment-setup-div {
        width: 100%;
    }
    .steps-section,
    .step_form_custom {
        margin-bottom: 0;
        padding: 0;
    }
    .steps-section {
        margin-top: 30px;
    }
    .socialUserImage {
        border-radius: 50%;
    }
    .selected-media-action .campaign-type-content {
        margin: 16px 0;
    }
    .selected-media-action .campaign-type-content label {
        width: 308px;
        height: 121px;
    }
    .selected-media-action .react-action-price-one.d-flex {
        padding: 0 19px;
    }
    .steps-section {
        height: auto;
        line-height: normal;
        margin-bottom: 38px;
    }
    .steps-section h2 {
        margin: 0;
        padding: 0;
    }
    .social_media_radio,
    .position-relative.otrcm {
        flex: 0 0 calc(50% - 20px);
        width: calc(50% - 20px);
        min-height: unset;
    }
    .position-relative.otrcm {
        margin-bottom: 20px;
    }
    .page_tab.new_steps.campaign-type,
    .page_tab.new_steps {
        padding: 10px 10px;
    }
    .social_media_radio span.media_platform {
        padding-left: 36px;
    }
    .media_platform label img {
        max-width: 50px;
        max-height: 50px;
        margin-right: 13px;
    }
    .media_platform label {
        font-size: 18px;
        line-height: 19px;
    }
    .step-nevigationbutton {
        margin: 0;
    }
    .form-shadow-box.half-box {
        padding: 33px 35px;
        margin: 17px;
        width: calc(50% - 34px);
    }
    .mobile-step-detail {
        display: block;
    }
    .steps-cont {
        display: none;
    }
    .steps-which {
        display: none;
    }
    .mobile-step-detail .steps-cont {
        display: block;
    }
    .mobile-step-detail .steps-which {
        display: block;
    }
    .steps-section {
        justify-content: center;
    }
    .steps-point {
        flex: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .steps-point:after {
        left: calc(50% + 36px);
    }
    .steps-which {
        font-size: 23px;
        line-height: normal;
        margin-bottom: 40px;
        text-align: center;
    }
    .steps-cont {
        padding: 0;
        text-align: center;
        font-size: 16px;
        line-height: normal;
    }
    .influencer-filter > img {
        height: 43px;
        margin-left: 21px;
    }
    .filter-form .form-group {
        margin: 18px 10px;
        flex: 0 0 calc(25% - 20px);
    }
    .filter-form {
        margin: 0 -10px;
    }
    .filter-div {
        width: calc(100% - 40px);
        left: 0;
        right: 0;
        margin: auto;
        top: 20px;
        bottom: 20px;
    }
    .workingTimer.show-mobile {
        display: block;
    }
    tr.campningDiv table td.time-show {
        display: none;
    }
    .latest-campaigns-box {
        margin-bottom: 79px;
        width: calc(50% - 40px);
        margin: 0 20px 42px;
    }
    .latest-campaigns-title {
        padding: 20px 0;
    }
    .popup-social-section {
        width: calc(100% - 40px);
        padding: 36px 0;
    }
    .latest-campaigns-box-outer {
        flex-wrap: wrap;
        overflow: auto;
    }
    .comming-soon-box {
        padding: 36px 0;
    }
    .comming-soon-box .logo img {
        width: 265px;
    }
    .home-heading {
        font-size: 20px;
    }
    .comming-soon-loader {
        width: 89%;
        height: 43px;
        margin: 45px auto 30px;
    }
    .comming-soon-loader:before {
        height: 34px;
    }
    .custom-loader {
        font-size: 16px;
    }
    .comming-step-box img {
        max-height: 60px;
        margin-bottom: 25px;
        margin-top: 21px;
    }
    .comming-step-box-text {
        font-size: 16px;
        line-height: 21px;
    }
    .comming-step-box-heading {
        font-size: 20px;
        line-height: 24px;
        padding-bottom: 7px;
    }
    button.apply-btn {
        width: 260px;
        height: 50px;
        font-size: 16px;
        line-height: 24px;
    }
    .applicationform .form-group {
        margin-bottom: 21px;
        width: 100%;
    }
    .application-heading {
        font-size: 48px;
    }
    .social_media.d-flex.justify-content-between.flex-wrap {
        align-items: center;
        justify-content: center !important;
    }
    #new_steps3 .step-nevigationbutton.fixed {
        bottom: 127px;
        width: calc(100% - 60px);
        left: 29px;
    }
    main.mobile {
        padding: 81px 0 35px;
    }
    .pagesections {
        padding: 30px 17px 30px;
    }
    .mobile-menu ul li.disabled_alltime.desabled,
    .mobile-menu ul li.desabled {
        pointer-events: none;
        opacity: 0.5;
    }
    .customcheckbox input ~ label {
        height: auto;
    }
    .pageSection {
        padding: 30px 0 127px;
        flex-direction: column-reverse !important;
    }
    .bannersection .d-flex.align-items-center {
        flex-direction: column;
    }
    .bannerContent {
        max-width: 465px;
        text-align: center;
    }
    .bannerContentSubtitle {
        font-size: 18px;
    }
    .bannerContentText {
        padding-right: 0;
    }
    .bannerContentBtn.d-flex.align-items-center {
        flex-direction: row;
        justify-content: center;
        margin-bottom: 19px;
    }
    .bannerImage {
        max-width: 540px;
        padding-left: 7%;
        width: 100%;
    }
    .bannerImageContent {
        bottom: 4%;
        left: 0;
        border-radius: 7.89px;
        max-width: 422px;
    }
    section.pageSectionOuter {
        padding-top: 45px;
    }
    .mainHeading {
        font-size: 29px;
    }
    .pageSectionTitle {
        font-size: 25px;
    }
    .pageSectionText{
        font-size: 16px;
    }
    .subHeading {
        font-size: 16px;
    }
    .pageSectioncontent {
        flex: 0 0 96%;
        margin: 0 auto;
        width: 100%;
        position: relative;
        z-index: 1;
        text-align: center;
        border: solid 1px #AD80FF;
        border-radius: 10px;
        padding: 0;
        margin-bottom: 19px;
    }
    .pageFaqSection .nav {
        padding: 16px;
        gap: 20px;
        width: 252.069px;
    }
    .oskii {
        display: none;
    }
    .pageFaqSection .accordion-item h2 button.accordion-button{
        padding: 16px;
    }
    .pageFaqSection .accordion-item div.accordion-collapse .accordion-body{
        padding-left: 16px;
        padding-bottom: 16px;
        padding-right: 16px;
    }
    .pageFaqSection .accordion-item{
        margin-bottom: 16px;
    }
    .pageSectioncontent {
        border: solid 1px #AD80FF;
        border-radius: 10px;
        padding: 0;
    }

    .pageSectioncontent .pageSectionText {
        display: none;
        width: 100%;
        font-size: 20px;
        padding: 8px 18px 9px;
    }

    .pageSectionTitle {
        font-size: 25px;
        position: relative;
        padding: 0 20px;
    }

    .pageSectionTitle:after {
        content: "";
        width: 30px;
        height: 30px;
        position: absolute;
        right: 10px;
        top: 0;
        /* border: solid 1px #AD80FF; */
        bottom: 0;
        margin: auto;
        border-radius: 50%;
        background: url(../images/icons/ei_plus.svg);
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
    }
    img.dash-image {
        display: none;
    }
    .pageSection.flex-row-reverse .pageSectionImage {
        margin: auto;
    }
    .pageSectioncontent.open .pageSectionText {
        display: block;
    }
    .open .pageSectionTitle:after {
        background-image: url(../images/icons/ei_minus.svg);
    }
    .dash-image-mobile{
        display: block;
    }
    img.dash-image-mobile.fir {
        width: calc(50%);
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        margin: auto;
    }

    img.dash-image-mobile.sec {
        position: absolute;
        width: 100%;
        top: 0;
        bottom: 0;
        margin: auto;
    }

    img.dash-image-mobile.thi {
        position: absolute;
        left: 0;
        width: calc(50% - 146px);
        top: 0;
        bottom: 0;
        margin: auto;
    }

    .inside-table.request-content {
        padding: 15px 36px 42px;
        gap: 44px calc(33.33% - 187px);
    }
    .request-content .inside-table-row {
        flex: 0 0 140px;
        width: 140px;
    }
    span.type-content {
        font-size: 14px;
    }
    .new-camping {
        gap: 26px;
    }
    .camping-box {
        border-radius: 30px;
        flex: 0 0 220px;
        padding: 16px 15px 22px;
        width: 220px;
    }
    .cb-socialmedia{
        gap: unset;
    }
    .cb-smbl{
        gap: 7px;
    }
    .prcnt{
        margin-left: auto;
    }
    .bannersection {
        padding-top: 45px;
    }
}


/*991*/
@media only screen and (max-width: 899px) {
}
/*899px*/
@media screen and (max-width: 790px) {
    .statics-html:before {
        right: auto;
    }
    .statics-html-text:nth-child(even) span.timeline-date,
    span.timeline-date {
        right: auto;
        left: 19px;
    }
    .statics-html-text:after {
        display: none;
    }
    .statics-html-text:before {
        right: auto;
        left: -6px;
        top: 2px;
        bottom: auto;
    }
    .statics-html-text {
        padding-left: 141px;
        flex-direction: row;
        width: 100%;
    }
    .statics-html-text:nth-child(even) {
        text-align: left;
        justify-content: left;
    }
    .statics-html-text:nth-child(even):before {
        left: -6px;
    }
    .statics-html-text b {
        top: 0;
        left: 106px;
        position: absolute;
    }
    .statics-html {
        width: 100%;
    }
    .your-score-conternt {
        width: 297px;
        padding: 14px 37px;
    }
    span.timeline-date {
        width: 92px;
    }
}
/*790px*/
@media screen and (max-width: 768px) {
    /* new css for desktop and mobile  */
    .desktop-view{
        display: none !important;
    }
    .mobile-view{
        display: block !important;
    }
    .section-heading{
        padding-bottom: 25px !important;
    }
    .section-button{
        padding-right: 29px;
        padding-bottom: 10px;
    }
    .new_card_row{
        padding-left: 20px;
        padding-right: 20px;
    }
    .campaign-card {
        padding: 16px 0px 0px 0px !important ;
    }
    .btn-show-result1
    {
        width: 155%;
        font-size: 11px;
        padding-bottom: 22px;
        height: 23%;
    }
    .btn-review {
        width: 19%;
        padding: 0px;
        height: 16px;
        font-size: 10px;
    }
    .btn-waiting
    {
        width: 14%;
        /*width: 19%;*/
        padding: 0px;
        height: 16px;
        font-size: 10px;
    }
    .btn-hold
    {
        width: 19%;
        padding: 0px;
        height: 16px;
        font-size: 10px;
    }
    .btn-show-result
    {
        width: 19%;
        padding: 0px;
        height: 16px;
        font-size: 10px;
    }
    .btn-cancel
    {
        width: 19%;
        padding: 0px;
        height: 16px;
        font-size: 10px;
    }
    .btn-finish-campaign
    {
        width: 155%;
        font-size: 11px;
        height: 23%;
    }
    .btn-cancel-new
    {
        width: 155%;
        font-size: 11px;
        padding-bottom: 22px;
        height: 23%;
    }
    .btn-show-details {
        width: 150%;
        font-size: 11px;
        padding-bottom: 23px;
        height: 23%;
    }
    .btn-all-invoices{
        width: 150%;
        font-size: 11px;
        padding-bottom: 23px;
        height: 23%;
        margin-top: 5px;
    }
    .btn-load-more
    {
        display: none !important;
    }
    .campaign-card .nav-tabs .nav-link {
        padding: 7px !important;
        font-size: 15px !important;
    }
    .campaign-table {
        width: 110%;
        margin-left: -15px;
        margin-top: 0px;
        border-collapse: separate !important;
        border-spacing: 0 16px !important;
    }
    .campaign-table th, .campaign-table td {
        padding: 5px 3px 12px 2px;
        font-size: 9px;
  }
    .campaign-table td {
        display: inline;
        text-align: left;
    }
    .campaign-table img {
        width: 19px;
        height: 16px;
    }
    .campaign-table .actions {
        text-align: center;
    }
    /* MarketPlace CSS */
    .campaign-list {
        margin-left: 0px;
        margin-right: 0px;
        margin-bottom: 0px;
        padding-left: 0px;
        padding-right: 0px;
        font-size: 12px;
    }
    .campaign-pricing {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-left: 90px;
    }

    .campaign-pricing p {
        padding-right: 0;
        margin-bottom: 5px; /* Add some space between the price and VAT included */
    }
    .campaign_org_price{
        padding-left: 20px;
    }
    .campaign_vat_price {
        margin-left: 0px;
    }

    .order-summary {
        flex-direction: column;
        margin-left: 0px;
        margin-right: 0px;
    }

    .summary-column {
        width: 100%;
        flex-direction: column;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        width: 100%;
    }

    .summary-item .label {
        flex: 1;
        text-align: left;
    }

    .summary-item .value {
        flex: 1;
        text-align: right;
        white-space: nowrap;
        font-size: 15px;
    }
    .total-container {
        margin-left: 0px;
        margin-right: 0px;
    }
    .total-value {
        font-size: 19px;
    }
    .info-text {
        margin-left: 0px;
        margin-right: 0px;
        margin-bottom: 53px;
    }
    ul.h-menu a:hover:before,
    ul.h-menu .active a:before,
    ul.h-menu a:hover:after,
    ul.h-menu .active a:after,
    ul.h-menu a:before,
    ul.h-menu a:after {
        display: none;
    }
    header.site-header {
        display: block !important;
        padding: 7px 0;
    }
    span.showUser {
        width: auto;
        flex: 1;
        /* line-height: normal; */
        white-space: nowrap;
        padding-right: 8px;
        font-size: 21px;
    }
    li.drop-state {
        padding: 0;
    }
    .bin_links {
        padding: 6px 23px;
        margin-top: 10px;
    }
    ul.h-menu {
        z-index: 1;
    }
    ul.h-menu.openMenu {
        display: block;
    }
    .menus.ms-auto {
        position: relative;
    }
    #hsj-menu li a.icon {
        padding: 5px;
    }
    ul.h-menu li a {
        text-transform: uppercase;
        width: 100%;
        display: inline-block;
    }
    .logedin .menuToggle.menustyle {
        display: none;
    }
    .image {
        height: calc(50vh - 60px);
    }
    .serOtr {
        padding: 9px 0;
    }
    .col-lg-3.col-md-4.col-sm-12.side-profile {
        display: none;
    }
    .connectWith.editProfile {
        margin-bottom: 25px;
    }
    .-layout .site-header.rink-header ul.h-menu a {
        color: #000;
    }
    .wizardHeading {
        font-size: 26px;
        margin-bottom: 13px;
        line-height: normal;
    }
    .dashboard-right header.site-header {
        background: transparent;
        /* padding: 0; */
        display: block !important;
        box-shadow: none !important;
        /* margin: 11px 15px 13px;
        width: calc(100% - 30px); */
        box-shadow: 0 0 10px #000 !important;
    }
    .dashboard-right header.site-header .header-logo {
        width: 403px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .dashboard-right header.site-header .header-logo a {
        flex: 0 1 auto;
    }
    .dashboard-right header.site-header .header-logo .showUser {
        margin-left: 20px;
        flex: 0 1 auto;
    }
    .dashboard-right header.site-header .container {
        padding: 0;
    }
    .step_form {
        margin-top: 58px;
    }
    .page_tab {
        margin: 0 15px 40px;
    }
    .orcTitle {
        margin: 0 0 10px;
        font-size: 18px;
        line-height: normal;
        font-weight: bold;
    }
    .tabsram-in:first-child:before {
        right: 0;
        top: 100%;
        left: 0;
        height: 2px;
        width: 70%;
        bottom: auto;
    }
    .tabsram {
        flex-direction: column;
    }
    .tabsram-in {
        flex: 0 0 100%;
        max-width: 100%;
        padding: 12px 0 !important;
    }
    .informationDiv {
        padding: 0;
    }
    .select_media {
        flex: 0 0 296px;
    }
    .datatip:hover:after {
        display: none;
    }
    .contentAllWz {
        flex-wrap: wrap;
    }
    .vizrdSelection {
        flex: 0 0 100%;
        margin: 0 0 14px;
    }
    .openFilterBtn.d-block.d-lg-none {
        margin-left: 0;
    }
    a.btn.btn-primary.openWizard {
        margin-left: 20px;
    }
    .lockText {
        width: 100%;
    }
    .wizardForm ul li label {
        position: relative;
        margin: 9px;
        width: calc(33.33% - 18px);
    }
    .wizardForm ul .form-group > .justify-content-between,
    .wizardForm ul .sdee .justify-content-between {
        justify-content: start !important;
        margin: 0 -9px;
    }
    .wizardForm ul .form-grou p > .justify-content-between label.autowidth,
    .wizardForm ul li label.autowidth {
        margin: 5px 5px;
    }
    .wizardForm ul li label {
        width: calc(50% - 18px);
    }
    .floating-label br {
        display: none;
    }
    .page_tab {
        padding: 12px;
    }
    .mobile-menu ul {
        padding: 0 19px;
    }
    .mobile-menu{
        border-top: 6px solid #AD80FF;
       border-radius: 21px;
       }
    .mobile-menu ul li a {
        padding: 15px 0 22px;
        margin-top: 16px;
        padding-top: 0;
        width: 100%;
    }
    .mobile-menu ul li span.lki {
        font-size: 11px;
        font-weight: 500;
        line-height: normal;
    }
    .connectPrising table td {
        font-size: 13px;
    }
    .table-btn {
        width: calc(25% - 12px);
    }
    .socialBtnConnect .table-btn {
        width: 113px;
    }
    .btn-div {
        margin: 0;
    }
    tr.campningDiv table {
        display: grid;
        grid-template-areas:
            "data-task data-task data-task data-task"
            "data-soci data-user data-ptyp data-pric"
            "data-time data-time data-time data-time"
            "data-butt data-butt data-butt data-butt";
        grid-template-columns: 20% 23% 39% 18%;
    }
    tr.campningDiv table td.taskName {
        padding-bottom: 0;
        line-height: normal;
    }
    tr.campningDiv table td.time-show {
        padding-bottom: 23px;
    }
    .connectPrising table td {
        padding: 9px 7px;
    }
    .campningDiv .handelpletform img {
        width: 19px;
        height: auto;
        margin-right: 6px;
    }
    .custDetail span.ctrb {
        border-radius: 50%;
    }
    .campningDiv .user-count-typ img {
        height: 30px;
    }
    .accordion-item .handelpletform img {
        width: 24px;
    }
    .accordion-item button table {
        display: grid;
        grid-template-areas:
            "acr-data-task acr-data-task acr-data-task acr-data-drpb"
            "acr-data-soci acr-data-soci acr-data-ptyp acr-data-ptyp"
            "acr-data-user acr-data-user acr-data-pric acr-data-pric"
            "acr-data-cbtn acr-data-cbtn acr-data-cbtn acr-data-cbtn";
        grid-template-columns: 20% 34% 28% 18%;
    }
    .nav-tabs.filtertab .nav-link {
        width: auto;
    }
    .data-table table .accordian-table td {
        padding: 8px 5px;
    }
    tr.accordian-table td {
        display: inline-block;
    }
    tr.accordian-table {
        display: flex;
        flex-wrap: wrap;
    }
    .data-table table .accordian-table td {
        border: 0 !important;
        flex: 0 0 31%;
    }
    .data-table .tab-pane table .accordian-table {
        border: solid 1px;
        border-radius: 15px;
        padding: 11px 15px;
        align-items: center;
        margin: 8px 0;
    }
    .data-table table .accordian-table.action {
        border-color: #ff0000;
    }
    .data-table table .accordian-table.inprogress {
        border-color: rgba(246, 166, 20, 1);
    }
    .data-table table .accordian-table.complete {
        border-color: #63c063;
    }
    .data-table table .accordian-table.cancelled {
        border-color: #ff0000;
    }
    .data-table table .accordian-table td .data-set-user {
        margin: 0;
    }
    .data-table table .accordian-table td .data-set-foll {
        margin: 0;
    }
    .data-table table .accordian-table td .data-set-price {
        margin: 0;
    }
    .data-table table .accordian-table td.text-center {
        text-align: left !important;
    }
    .data-table table .accordian-table td:first-child {
        flex: 0;
    }
    .dashboard-right header.site-header .header-logo {
        width: 403px;
    }
    .modal-content .wizardHeading {
        line-height: normal;
        margin: 19px auto 20px;
        font-size: 24px;
        letter-spacing: 1px;
        max-width: 90%;
    }
    .influncerlist-dropdown button.accordion-button {
        padding: 6px 8px;
    }
    .dr-row {
        font-size: 14px;
    }
    .influencer-image {
        width: 28px;
        height: 28px;
        flex: 0 0 28px;
    }
    .influncerlist-dropdown.pb-5 {
        padding-bottom: 15px !important;
    }
    .text-center.start-camp.mt-0 {
        margin-bottom: 22px !important;
    }
    .ordertab .nav-item button.nav-link {
        font-size: 15px;
        line-height: 16px;
        padding: 8px 10px;
    }
    .detail-table {
        max-width: 90%;
    }
    .detail-table .inside-table {
        width: calc(100% - 0px);
        margin: 0;
    }
    .detail-table .inside-table .inside-table-row span:first-child {
        font-size: 14px;
        line-height: 16px;
        padding: 8px;
        flex: 0 0 260px;
    }
    .detail-table .inside-table .inside-table-row span:last-child {
        font-size: 14px;
        line-height: 16px;
        padding: 8px 4px;
    }
    .detail-table {
        max-width: 90%;
        margin: 21px auto;
    }
    .order-titles {
        font-size: 14px;
    }
    .order-content {
        font-size: 14px;
        line-height: 13px;
    }
    .wewPopup .popup2btns {
        margin: 22px 0 14px;
        justify-content: center;
    }
    .cancle-camp {
        font-size: 14px;
        line-height: 20px;
        height: 38px;
        padding: 0 20px;
    }
    .popup-title {
        font-size: 17px;
        line-height: 22px;
    }
    ul.nav-tabs.ordertab {
        margin-top: 0 !important;
        margin-bottom: 23px !important;
    }
    .popup-title-id {
        font-size: 12px;
        line-height: 17px;
        padding: 3px 0 11px;
    }
    button.btn-close {
        width: 20px;
        height: 20px;
    }
    button.btn-close img {
        width: 100%;
        height: 100%;
    }
    .influncerlist-dropdown .accordion-collapse.show:before {
        height: calc(100% + 42px);
        margin-top: -42px;
    }
    #paymentForm1 ul.nav-tabs.ordertab {
        margin-top: 16px !important;
    }
    .droporg {
        font-size: 19px;
        line-height: 24px;
        margin: 0 0 34px;
    }
    .continue-link-outer {
        margin-top: 35px;
        margin-bottom: 0;
    }
    .ontotalcount {
        margin: 19px 0 19px;
    }
    .manreq-title {
        font-size: 24px;
        line-height: 36px;
        padding: 24px 32px 14px;
    }
    .chkImage img {
        width: 90px;
        margin-top: 41px;
    }
    .modal .et-submit {
        margin-top: 8px;
        margin-bottom: 8px;
    }
    .et-submit {
        font-weight: 700;
        font-size: 14px;
        line-height: 36px;
        padding: 0 9px;
        min-width: 115px;
        height: 36px;
        margin: 30px 0 23px;
    }
    .form-border {
        padding: 30px 22px 22px;
        margin: 15px 0;
    }
    .bold-text {
        font-size: 15px;
        padding: 0 0 19px;
    }
    .modal-content .complaint-popup .wizardHeading {
        margin-top: 20px !important;
        margin-bottom: 16px;
    }
    .admethod > span {
        font-size: 24px;
        line-height: 21px;
    }
    .media-div img {
        width: 42px;
    }
    .campHistory .campningDiv img, .campHistory .campningDiv video {
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
    }
    .post-text {
        flex: 0 0 calc(100% - 80px);
    }
    .campningDiv.indata {
        display: grid;
        grid-template-areas:
            "data-one data-two data-two data-two"
            "data-one data-three data-three data-three"
            "data-one data-four data-four data-four";
        gap: 0;
        padding: 10px;
        align-items: start;
        grid-template-columns: 1fr 1fr 1fr 1fr;
    }
    .media-data {
        grid-area: data-one;
    }
    .post-text {
        grid-area: data-two;
        text-align: left;
        padding-bottom: 7px;
        padding-left: 7px;
        line-height: normal;
    }
    .publishfirDaat {
        grid-area: data-three;
        padding-left: 7px;
        line-height: normal;
    }
    .indata button.cunformBtn {
        grid-area: data-four;
        margin: 13px 0 0 7px;
        margin-left: 7px;
        margin-right: 0;
    }
    .media-data-inner {
        width: 100%;
        padding-top: 100%;
        height: 0;
        position: relative;
    }
    .hhsu {
        margin-top: 39px;
        margin-bottom: 27px;
        justify-content: center;
        align-items: center;
        margin: 24px 23px;
    }
    .socialimage {
        width: 46px;
        flex: 0 0 46px;
    }
    .socialcontent-posttype,
    .socialcontent-postlink a {
        font-size: 14px;
        line-height: 20px;
        max-width: 222px;
    }
    .socialComp > div > div {
        flex: 0 0 37%;
    }
    .ssn {
        margin: 24px auto 0;
    }
    img.complaint-confirm-image {
        width: 90px;
        margin-top: 41px;
        margin-bottom: 0;
    }
    p.thank-title1 {
        margin: 0;
        font-size: 24px;
        line-height: 36px;
        padding: 24px 32px 14px;
    }
    p.thank-title2 {
        margin: 8px 0 16px;
        font-weight: 500;
        font-size: 16px;
        line-height: 20px;
    }
    #thankYouContact a.et-submit.mx-4.complant-btn {
        margin-bottom: 3em;
    }
    .order-accept-text {
        font-size: 15px;
        line-height: 20px;
        letter-spacing: 0;
    }
    header button.user-btn.dropdown-toggle span {
        max-width: 121px;
    }
    .socialCnt {
        margin-right: 38px;
    }
    .paymentPage.d-flex.justify-content-between {
        flex-direction: column;
    }
    .payment-transaction-layout .customerSer {
        padding: 0;
    }
    .payment-transaction-layout .step_form_custom {
        padding: 0;
    }
    .payment-transaction-layout .paymentPagenumber {
        max-width: 100%;
        flex: 100%;
        width: 100%;
    }
    .payment-setup-div {
        padding: 17px 24px;
    }
    .ps-title {
        font-style: normal;
        font-weight: 800;
        font-size: 20px;
        line-height: normal;
        letter-spacing: 1px;
        margin-right: 22px;
    }
    .ps-title {
        font-size: 20px;
        margin-right: 22px;
        margin-bottom: 17px;
    }
    .payment-setup-div .d-flex.d-flex.align-items-center {
        flex-wrap: wrap;
    }
    header .container {
        max-width: 100%;
    }
    .react-action {
        width: 100%;
        flex: 0 0 100%;
        grid-area: display1;
    }
    .selected-media-action > .d-flex {
        flex-wrap: wrap;
        margin-bottom: 53px;
        display: grid !important;
        grid-template-rows: auto;
        grid-template-areas:
            "display1 display1 prc1"
            "display1 display1 prc2";
        width: 100%;
    }
    .selected-media-action {
        flex: 0 0 397px;
        width: 100%;
        margin: 0 auto;
        max-width: 511px;
    }
    .selected-media-action .react-action-price-one.d-flex {
        margin: 0;
        padding: 0;
        grid-area: prc1;
    }
    .publish-info .react-action,
    .publish-info .influencer-detail {
        width: calc(50% - 16px);
        flex: 0 0 calc(50% - 16px);
        margin: 0 8px 13px;
    }
    #collection4 .publish-info .influencer-detail {
        margin-right: 8px;
    }
    .publish-last-div {
        width: 478px;
        margin: 0 auto;
    }
    .publish-info-content.d-flex {
        margin: 0 -8px;
    }
    .am-selected-media ul.media-box {
        padding: 15px 27px;
        justify-content: center;
        margin-bottom: 42px;
        min-width: 489px;
    }
    .selected-media-action .react-action-price-one.disable.d-flex {
        grid-area: prc2;
    }
    .selected-media-action .campaign-type-content label {
        width: 265px;
        align-items: flex-start;
        padding: 13px 20px;
    }
    .selected-media-action .campaign-type-content {
        margin: 0;
    }
    .react-action {
        width: 256px;
    }
    .selected-media-action .campaign-type-text {
        line-height: 35px;
        padding-left: 13px;
    }
    .selected-media-action .campaign-type-content label:before {
        right: auto;
        top: 60px;
    }
    .selected-media-action .campaign-type-content input:checked ~ label:after {
        right: auto;
        left: 37px;
        top: 62px;
    }
    .selected-media-action .campaign-type-content label:after {
        right: auto;
        top: 62px;
        left: 18px;
    }
    .task-options.new {
        flex-direction: column;
    }
    .form-shadow-box.half-box {
        width: 100%;
        max-width: 498px;
        margin: 0 auto 32px;
    }

    .marketplace-imports.shoutout .steps-section .steps-point:after {
        /* display: none; */
    }
    .filter-form .form-group {
        margin: 18px 10px;
        flex: 0 0 calc(33.33% - 20px);
    }
    .filter-div {
        z-index: 111;
    }
    .influencer-detail {
        width: calc(50% - 30px);
    }
    .step-nevigationbutton > div.selected-influencer-box {
        width: 444px;
        padding: 21px 15px;
        right: 110px;
        bottom: 41px;
    }
    .selected-influencer-contr .influencer-detail {
        width: calc(100% - 26px);
        margin: 6.5px 13px;
        max-width: 263px;
        margin-left: auto;
        margin-right: auto;
    }
    .step-nevigationbutton > div.selected-influencer-box.open {
        position: fixed;
        left: 0;
        top: 0;
        border-radius: 0;
        width: 100vw;
        z-index: 1111111;
        height: 100vh;
    }
    .selected-influencer-contr {
        display: flex;
        flex-wrap: wrap;
        max-height: calc(100vh - 150px);
        overflow: auto;
        max-width: 581px;
        margin: 0 auto;
    }
    .form-shadow-box.middle-box {
        margin-top: 24px;
        width: 100%;
        padding: 30px 35px 15px 35px;
    }
    .step-nevigationbutton > div.influencer-cart {
        right: 71px;
    }
    .latest-campaigns-box {
        margin-bottom: 79px;
        width: calc(50% - 40px);
        margin: 0 20px 42px;
        min-width: 498px;
        margin-left: auto;
        margin-right: auto;
    }
    .content-area {
        min-height: calc(100vh - 250px);
    }
    p.caution-message {
        margin: 0 0 12px;
    }
    button.button-Request {
        margin-top: 0;
        margin-bottom: 0;
    }
    .terms-div.middle-box.text-center {
        padding-bottom: 53px;
    }
    .influencer-list {
        margin-left: 0;
        margin-right: 0;
    }
    td.td-finish-las {
        display: none;
    }
    .paymentPagenumber {
        max-width: 500px;
        margin: 39px auto 19px;
        width: 100%;
    }
    .loginpage .logo img {
        width: 304px;
    }
    section.loginpage {
        /* background-image: url(../images/icons/mobile-login1.png),
            url(../images/icons/mobile-login2.png),
            url(../images/icons/mobile-login3.png),
            url(../images/icons/mobile-login4.png); */
        background-position: top left, right top, bottom right, bottom left;
    }
    .img-usr{
        width: 24px;
        height: 24px;
        margin-left: 9px;
    }
    .freeprivacypolicy-com---nb .cc-nb-main-container {
        padding: 2rem !important;
    }
    .pageFaqSection .accordion-item h2 button.accordion-button, .pageFaqSection .accordion-item div.accordion-collapse .accordion-body{
        padding: 12px;
    }
    .pageFaqSection .accordion-item div.accordion-collapse .accordion-body{
        padding-top: 0;
    }
    .pageFaqSection {
        flex-wrap: wrap;
        padding-top: 35px;
    }

    .pageFaqSection .accordion-item {
        margin: 16px 0 !important;
    }
    .oskii {
        display: none;
    }

    .pageFaqSection .nav {
        width: 183.069px;
    }
    .copyrightOuter {
        padding: 27px 15px;
    }

    .pageSectioncontent.open .pageSectionText {
        display: block;
    }

    .open .pageSectionTitle:after {
        background-image: url(../images/icons/ei_minus.svg);
    }

    .footerLogoSection {
        width: 100%;
        flex: 0 0 100%;
    }

    .footerContainer .d-flex .footerSection {
        flex-wrap: wrap;
        max-width: 451px;
        padding: 59px 15px;
        gap: 28px 12px;
    }

    .footerlink:nth-child(2), .footerlink {
        /* flex: 0 0 47%; */
        margin: 30px 0;
    }

    .footerlink:last-child {
        flex: 0 0 100%;
        margin: 0;
    }
    .linkingSection {
        width: 100%;
    }

    .linkOption1 {
        top: 0;
        left: calc(50% - 120px);
    }

    .linkOption2 {
        right: 0;
        left: auto;
        top: calc(25% - 64px);
    }

    .linkOption4 {
        top: calc(75% - 99px);
        left: 0;
    }

    .linkOption5 {
        right: calc(50% - 120px);
        bottom: -11px;
    }
    .mobile-lan {
        display: block;
    }
    .h-menu-outer {
        position: absolute;
        background: #fff;
        display: flex;
        flex-direction: column;
        top: 59px;
        right: 16px;
        text-align: left;
        justify-content: start;
        align-items: start;
        border-radius: 8px;
        padding: 9px 6px;
        box-shadow: 0 4px 18px rgba(0, 0, 0, 0.2);
        display: none;
        width: auto;
    }

    .h-menu-outer ul {
        flex-direction: column;
    }

    .h-menu-outer li.leng {
        display: none;
    }

    .mobile-lan {
        margin-left: auto;
    }

    .h-menu-outer ul.h-menu {
        margin: 0 !important;
        text-align: left;
        align-items: start;
        width: 100%;
    }

    .h-menu-outer ul.h-menu li a {font-size: 16px !important;padding: 5px 9px !important;}

    .h-menu-outer ul.h-menu li {
        margin: 0;
    }

    .mobile-lan li a {
        padding: 0 4px;
        color: #877F7F;
        font-family: Outfit;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        text-decoration-line: underline;
    }

    .mobile-lan li {list-style: none;}

    .mobile-lan li a.active {
        color: #1E1854;
    }

    .h-menu-outer.openMenu {
        display: block;
    }

    span.menustyle.closebtn {
        width: 51px;
        display: inline-block;
        text-align: center;
        height: 44px;
        line-height: 44px;
    }

    span.menustyle.closebtn i {
        font-size: 22px;
        text-align: center;
    }
    .new-camping {
        flex-direction: column;
    }
    .camping-box {
        flex: unset;
        padding: 16px 15px 22px;
        width: 241px;
    }
    .top-title {
        margin-top: 0;
    }
    .request-content-data.icon-before {
        gap: 27px 26px;
        padding: 29px 33px 46px;
    }
    .request-content-data .inside-table-row {
        width: calc(50% - 13px);
    }
    .inside-table.request-content {
        padding: 15px 20px 42px;
    }
    .icon-before .order-titles{
        gap: 10px;
    }
    .request-content-data.icon-before .order-titles:before, .request-content-data .order-content:before {
        min-width: 20px;
        height: 20px;
        flex: 0 0 20px;
    }
    .request-content-data .inside-table-row{
        padding: 9px;
        border-radius: 15px;
    }
    .request-content-data .order-content{
        margin-top: 15px;
        margin-bottom: 10px;
    }
    .ontro .dr-row{
        padding-left: 0;
    }
    .dr-row:last-child {
        padding-bottom: 8px;
    }
    .payment-total {
        padding: 0 10px;
    }
    .card-radio{
        margin-right: 18px;
    }
    .card-remove-btn{
        margin-left: 18px;
        width: 80px;
    }
    .post-link {
        display: inline-block;
        position: static;
    }
    .influencer-name, .influencer-follo{
        margin: 0 0 0 8px;
    }


    /* New css for mobiles  */
    .new_header{
        display: none;
    }

}
/*767px*/
@media screen and (max-width: 680px) {

    .logForm {
        margin: 0;
    }
    .flexUser {
        flex: 0 0 calc(50% - 13px);
        max-width: calc(50% - 13px);
    }
    .vizrdSelection {
        width: 100%;
        text-align: center;
    }
    a.btn.btn-primary.openWizard {
        float: none;
        display: inline-block;
    }
    .contentAllWz {
        text-align: center;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
    }
    .openFilterBtn.d-block.d-lg-none {
        float: none;
        display: inline-block !important;
    }
    .steps.mb-3 {
        margin-top: 30px !important;
    }
    .wizardForm ul li > label {
        width: calc(48.33% - 10px);
    }
    .socialUserDetail {
        width: 100%;
        flex: 0 0 100%;
    }
    .connectWithInner {
        flex-wrap: wrap;
    }
    label.checkboxAllCheck span.selcc {
        font-size: 14px;
        padding: 4px 12px;
    }
    .page_tab {
        margin: 0 0px 40px;
    }
    .vizrdSelection {
        flex-wrap: wrap;
    }
    .sst {
        margin-top: 5px;
        margin-bottom: 5px;
    }
    .loginRight form {
        margin: 0 auto;
        width: 100%;
    }
    .loginpage .form-control {
        width: 100%;
    }
	.socialConect a.google.socialConectLink{
		width:100%;
		flex:0 0 100%;
	}
    .lgregistration-page:before {
        width: 100%;
    }
    .lgregistration-page .col-12.text-center button.apply-btn {
        font-size: 14px;
        width: 265px;
        margin: 0 auto 16px !important;
    }
    .lgregistration-page .col-12.text-center {
        flex-direction: column;
    }
    .app-text {
        padding-bottom: 27px;
    }
    .loginpage .logo img {
        width: 241px;
        margin-top: 30px;
    }
    .pageFaqSection .nav {
        width: 132.069px;
        padding: 11px;
        gap: 11px;
    }
    .pageFaqSection .accordion-item div.accordion-collapse .accordion-body, .pageFaqSection .accordion-item h2 button.accordion-button{
        font-size: 15px;
    }
    .cna {
        font-size: 13px;
        margin-right: 0;
    }

    .blur .socialBtnConnect {
        margin-left: auto;
        margin-top: 8px;
    }
    .linkingSection img.way-image {
        max-width: 100%;
        max-width: 711px;
        margin: 0 auto;
        width: calc(100% - 64px);
        position: relative;
        z-index: 0;
    }
	.orText {
	    width: 100%;
	}
    .bannerContentSubtitle {
        font-size: 11px;
    }

    .bannerContentTitle {
        font-size: 31px;
        left: inherit;
    }

    .bannerContentText {
        width: 296px;
        font-size: 14px;
        margin: 0 auto;
    }

    .bannersection .bannerContentBtn.d-flex.align-items-center {
        flex-direction: column;
    }

    .bannerImageContent ul li {
        font-size: 9px;
    }

    .bannerImageContent {
        max-width: 77%;
    }

    .mainHeading {
        font-size: 20px;
    }

    .subHeading {
        font-size: 14px;
    }

    .pageSectionTitle {
        font-size: 18px;
    }

    .pageSectioncontent .pageSectionText {
        font-size: 14px;
    }

    .pageSectionImage img {
        max-width: 191px;
    }

    .pageSection.d-flex.align-items-center {
        padding-bottom: 87px;
    }

    .pageSectionTitle:after {
        width: 20px;
        height: 20px;
        right: 4px;
    }

    .linkingSection.d-inline-flex.align-content-center.justify-content-center {
        width: 342px;
    }

    .linkOption {
        width: 120px;
        height: 107px;
    }

    .linkOptionimage {
        width: 40px;
        height: 40px;
    }

    .linkOptionimage img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .linkOptiontext {
        font-size: 10px;
        line-height: normal;
        height: auto;
    }

    .linkOptiontext {
        line-height: normal;
    }
    .pageSectionImage img.shadow-box {
        position: absolute;
        width: 194.1%;
        top: -18.24%;
        left: -67%;
        z-index: 1;
        height: auto;
        max-width: unset;
    }
    .linkOption4 {
        top: calc(75% - 57px);
        left: 0;
    }
    .linkOption2 {
        right: 0;
        left: auto;
        top: calc(25% - 57px);
    }
    .footerlink:nth-child(2), .footerlink {
        margin: 0;
    }
    img.dash-image-mobile.thi {
        position: absolute;
        left: -64px;
        width: calc(50% - 46px);
        top: 0;
        bottom: 0;
        margin: auto;
    }
	.footerLogoSection {
	    width: 100%;
	    flex: 0 0 100%;
	    margin: auto;
	}

	.footerlink:nth-child(2), .footerlink {
	    margin: 0 auto;
	}

	.footerHeading {
	    font-size: 17px;
	}

	ul.footerlink li a img {
	    width: 23px;
	}

	ul.footerlink li a {
	    gap: 5px;
	    font-size: 14px;
	}
	.footerlink:nth-child(3) {
	    margin: 0 auto;
	}
    .top-title {
        margin-top: 0;
    }

    .task-div-outer .form-group {
        width: 100%;
        flex: 100%;
    }

    .task-div-outer {
        flex-wrap: wrap;
        max-width: 556px;
        margin: 0 auto;
        width: 90%;
        gap: 0;
    }
	.task-title {
        font-size: 17px;
    }

    .custom-task-list {width: 90%;margin: 0 auto;}
    .custom-task-list.checked-list label, .custom-task-list label{
        min-width:unset;
        width: 100%;
    }
}
/*680px*/
@media screen and (max-width: 575px) {


    div#counter .conterAll:after {
        display: none;
    }
    .counter-value {
        font-size: 16px;
        line-height: 23px;
    }
    span.ttlCntr {
        color: #fff;
        font-weight: 400;
        font-size: 14px;
    }
    div#counter {
        background: transparent;
    }
    ul.f-menus li {
        width: 100%;
    }
    ul.f-menus {
        display: inline-block;
        width: 100%;
    }
    ul.f-menus li a {
        margin: 8px 0;
    }
    .userDetails {
        max-width: 310px;
        margin: 5px auto;
    }
    .outVarification.bgDarkBlue {
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    a.verifyAcNotification {
        margin: 20px auto 0;
        width: 202px;
        padding: 13px 0px;
    }
    .steps {
        margin: 33px 0;
    }
    .steps_con {
        background: transparent;
        padding: 0;
    }
    .open .dashboard-left {
        width: 100%;
    }
    .steps {
        padding: 27px 29px;
    }
    .dashboard-right header.site-header {
        padding: 16px 10px 11px;
        width: calc(100% - 0px);
        margin: 0 auto;
        background: #fff;
        display: block !important;
        box-shadow: 0 0 10px #000 !important;
    }
    .dashboard-right header.site-header .menus.ms-auto {
        flex-wrap: wrap;
        justify-content: center;
    }
    li.ntfMenu {
        margin-right: -6px;
        padding: 0 20px 0 0;
    }
    .dashboard-right header.site-header span.showUser {
        font-size: 21px;
        width: 100%;
    }
    .ser_op {
        font-size: 12px;
    }
    span.onClkOk {
        right: 6px;
        font-size: 11px;
        padding: 2px 8px 3px 25px;
    }
    span.onClkOk > span {
        width: 16px;
        height: 16px;
        left: 5px;
        line-height: 15px;
        font-size: 10px;
    }
    hr {
        margin: 0 13px 12px !important;
        width: calc(100% - 25px) !important;
        color: #979797;
    }
    .page_tab .steps {
        margin: 0;
        padding: 16px;
        padding: 0;
        margin-top: 0 !important;
    }
    .steps.selectedUserOuter {
        bottom: 81px;
        right: 0;
        left: 0;
        margin: auto;
    }
    .checkone.d-flex {
        flex-wrap: wrap;
    }
    .inputs {
        margin-bottom: 29px;
        padding: 0%;
    }
    .sahrePrice {
        width: calc(100% - 16px);
    }
    .inputs {
        max-width: 100%;
        flex: 0 0 100%;
    }
    .myDiv:first-child .inputs {
        margin: 0;
        padding: 0%;
    }
    body {
        background-color: #fff !important;
    }
    .newpost {
        width: auto;
        margin-top: 16px;
    }
    .bin_links {
        padding: 6px 23px;
        margin-top: 15px;
    }
    .d-flex.mediabox {
        flex-direction: column;
    }
    .select_media.checkedmedia {
        max-height: unset;
        overflow: unset;
    }
    .select_media {
        max-height: 50px;
        overflow: hidden;
    }
    .authForm .blueBtn.smallBtn {
        width: 171px;
    }
    .markeplaceUsers {
        padding: 0 10px;
    }
    tr.campningDiv table {
        display: grid;
        grid-template-areas:
            "data-task data-task data-task data-task"
            "data-soci data-soci data-user data-user"
            "data-ptyp data-ptyp data-pric data-pric"
            "data-time data-time data-time data-time"
            "data-butt data-butt data-butt data-butt";
        grid-template-columns: 20% 29% 29% 25%;
    }
    .mobile-menu{
     border-top: 6px solid #AD80FF;
    border-radius: 21px;
    }
    .mobile-menu .image_more{
        height: 50px;
        width:50px;
    }
    .mobile-menu ul li a {
        padding: 15px 0;
        margin-top: 15px;
        padding-top: 0;
    }
    .mobile-menu ul {
        padding: 0;
    }
    .mobile-menu ul li a.active span.lki:before,
    .mobile-menu.open-menu ul li.extramenu a span.lki:before {
        /* width: 48px;
        height: 60px;
        left: -9999px; */
        right: -9999px;
        bottom: -48px;
    }
    .mobile-menu ul li a.active span.lki:after,
    .mobile-menu.open-menu ul li.extramenu a span.lki:after {
        /* width: 44px;
        height: 44px;
        left: -9999px; */
        right: -9999px;
        bottom: -38px;
    }
    .mobile-menu .fixed
    .mobile-menu ul li span.lki {
        font-size: 9px;
        line-height: 9px;
        padding-bottom: 0;
    }
    .nav-tabs.filtertab .nav-link {
        padding: 0 5px;
        border-radius: 11px;
        box-shadow: none !important;
        height: 28px;
    }
    ul.nav-tabs.filtertab {
        margin: 0 -35px 13px;
        display: flex;
        width: calc(100% + 70px);
    }
    .data-table .tab-pane table .accordian-table {
        padding: 8px 12px;
        align-items: center;
    }
    .img-dropdown img {
        width: 18px;
        height: 18px;
        transform: rotate(180deg);
    }
    .img-to img {
        width: 18px;
        height: 18px;
    }
    .influencer-price {
        min-width: unset;
        margin-left: auto;
    }
    .influencer-name {
        width: auto;
    }
    .influencer-name,
    .influencer-follo {
        margin: 0 0 0 14px;
    }
    .modal-content .wizardHeading {
        font-size: 18px;
    }
    .detail-table .inside-table .inside-table-row span:first-child {
        font-size: 14px;
        line-height: 16px;
        padding: 3px;
        flex: 0 0 49%;
    }
    .detail-table .inside-table .inside-table-row span:last-child {
        font-size: 14px;
        line-height: 16px;
        padding: 2px 4px;
    }
    .detail-table {
        max-width: 100%;
        margin: 15px 13px;
        padding: 9px;
    }
    .order-accept-div {
        padding: 0 21px 34px;
    }
    .ordertab .nav-item button.nav-link {
        font-size: 14px;
        line-height: 16px;
        padding: 6px 9px;
    }
    ul.nav-tabs.ordertab {
        margin-top: 0 !important;
        margin-bottom: 82px !important;
    }
    .ontotalcount {
        padding-left: 0;
        padding-right: 21px;
        color: #353b5f;
        margin: 12px 0 0;
    }
    .droporg {
        text-align: center;
        font-style: normal;
        font-weight: 700;
        font-size: 15px;
        line-height: 24px;
        color: #353b5f;
        margin: 11px 0 18px;
    }
    .influncerlist-dropdown .accordion-collapse.show:before {
        height: calc(100% + 34px);
        margin-top: -34px;
    }
    .dp-text {
        padding-left: 11px;
    }
    .ontro {
        max-width: 91%;
        padding: 0 0 21px;
        margin: 0 auto;
    }
    .continue-link-outer {
        margin-top: 18px;
        margin-bottom: 0;
    }
    .payment-form {
        max-width: 100%;
    }
    .payment-one img {
        height: 24px;
    }
    .payment {
        /* width: 100%; */
        /* margin: 24px auto 0; */
    }
    .manreq-title {
        font-size: 18px;
        line-height: 34px;
        padding: 24px 32px 14px;
    }
    .chkImage img {
        width: 59px;
        margin-top: 32px;
    }
    .popup-title {
        padding-left: 18px;
        padding-right: 18px;
    }
    .modal-content {
        width: 100vw !important;
        min-height: 100vh !important;
        border-radius: 0 !important;
    }
    .modal-dialog.default-width {
        width: 100% !important;
        max-width: unset;
        margin: 0;
    }
    .inside-table-row {
        padding: 4px 0;
    }
    .modal-lg,
    .modal-xl,
    .confirm-content .modal-lg,
    .confirm-content .modal-xl,
    .confirm-submit .modal-dialog.modal-lg,
    .complaint-confirm-popup .modal-lg {
        max-width: 100%;
        margin: 0;
    }
    div.requesttime .modal-dialog {
        max-width: 100%;
        margin: 0;
    }
    .form-border {
        margin: 15px 0;
    }
    .tasklistContent {
        font-size: 14px;
    }
    div[aria-labelledby="complaintPopupConfirmLabel"]
        .modal-dialog.modal-lg
        .modal-body {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }
    .font-size-16 {
        font-size: 13px;
    }
    .contact-support {
        max-width: 90%;
        margin: 0 auto;
    }
    .custom-file-picker {
        width: 100%;
        height: 81px;
        margin-bottom: 15px;
    }
    body textarea.form-control {
        height: 185px;
    }
    .text-center.wizardHeading-subheading {
        font-size: 14px;
        margin-bottom: 0;
    }
    #manyrequest .modal-body,
    .complaint-confirm-popup .modal-lg .modal-body {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .admethod > span {
        font-size: 19px;
        line-height: 20px;
    }
    .form-border {
        padding: 21px 17px 11px;
    }
    .ssn.d-flex {
        flex-wrap: wrap;
        justify-content: center;
    }
    .ssn .socialComp {
        margin: auto;
    }
    .order-accept-div {
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    .order-accept-buttons {
        margin: 27px 0 0;
    }
    header button.user-btn.dropdown-toggle span {
        max-width: 78px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: none;
    }
    .cont-suy {
        font-size: 16px;
        line-height: 23px;
        padding: 20px 0 15px;
        text-align: left;
    }
    .requesttime .modal-body .sortNN {
        margin-top: 0;
        margin-bottom: 0;
        width: 100%;
    }
    .extra-time-content {
        padding: 20px;
    }
    .requesttime .modal-body {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }
    .extra-time-content p {
        font-size: 16px;
    }
    .data-table table .accordian-table.completed td:nth-child(2) {
        flex: 0 0 82%;
    }
    .custDetail .usernamewidth {
        max-width: 217px;
    }
    .wizardPopup {
        max-width: 100vw;
        padding: 15px;
        padding-top: 0;
        margin: 0;
        border-radius: 0;
        max-height: 100vh;
        overflow: auto;
    }
    .wizardPopup span.larg {
        font-size: 19px;
        line-height: 21px;
        margin-bottom: 13px;
    }
    .wizardForm input ~ span {
        padding: 8px 16px;
    }
    .wizardForm input ~ span img {
        width: 20px;
        left: 10px;
        top: 0;
        bottom: 0;
    }
    .dashboard-right header.site-header .header-logo {
        width: 123px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .dashboard-right header.site-header .header-logo a {
        flex: 0 1 auto;
    }
    .dashboard-right header.site-header .header-logo .showUser {
        margin-left: 20px;
        flex: 0 1 auto;
    }

    .steps.filter.openFilter .markeplaceSidebar {
        margin: 73px 0 82px;
        width: 100%;
        max-width: 100%;
        border-radius: 0;
        position: relative;
        padding-left: 15px;
        padding-right: 15px;
    }
    .selectedUser {
        max-height: unset;
        height: 218px;
    }
    div#requestDialogue {
        padding: 0;
    }
    .modal-body {
        padding: 15px;
    }
    .confirmText:before {
        width: 44px;
        height: 44px;
        border-radius: 50px;
        font-size: 42px;
        margin: 0 auto 25px;
        font-size: 20px;
    }
    .confirmInner {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
    }
    .divSeco {
        display: none;
    }
    .divSeco.show {
        display: block;
    }
    .steps_con {
        margin: 0;
        box-shadow: none;
        border-radius: 20px;
        padding: 0 0 36px;
    }
    .et-submit {
        min-width: 91px;
    }
    .btn-container {
        min-width: 100%;
    }
    .connectPrising table td {
        width: 100%;
    }
    td.camp-button.mobile {
        flex-direction: column;
        padding: 0;
    }
    .paymentPagenumber img {
        height: 44px;
    }
    .ortContent {
        font-size: 15px;
        line-height: 20px;
        padding: 23px 0 22px;
    }
    .paymentPagenumber h2 {
        font-size: 20px;
        line-height: 23px;
        margin-bottom: 19px;
        margin: 0;
        padding: 25px 0 4px;
    }
    .payment-transaction-layout .paymentPagenumber {
        max-width: 100%;
        margin: 51px 0 0;
        padding: 0 17px 25px;
    }
    .workingTimer.show-mobile {
        display: block;
        position: relative;
        width: 50%;
        width: calc(50% - 4px);
        margin-top: 20px;
        margin-right: 4px;
    }
    .workingTimer.show-mobile .timing {
        width: 100%;
        margin: 0 !important;
    }
    .accImg img {
        width: 138px;
    }
    .secTitle {
        margin-top: 31px;
    }
    main.desktop {
        display: none;
    }
    main.mobile {
        display: block;
    }
    .header-logo {
        height: auto;
        flex: 0 0 47%;
    }
    /* span.showUser {
        display: none;
    } */
    span.tabText {
        display: none !important;
    }
    .ser_op > span.numberTab {
        display: none;
    }
    .step_form_custom .current .steps-icon {
        /* border-color: #3cc23c; */
        color: #AD80FF;
        background: transparent;
    }
    .step_form_custom img.step-icon-check {
        display: none;
    }
    span.mobile-text {
        font-size: 20px;
        line-height: 18px;
    }
    .steps-icon {
        width: 30px;
        height: 30px;
    }
    .conttr {
        flex: 0 0 calc(100% - 104px);
    }
    a.soclBtn.disconnect.table-btn.red-btn,
    a.soclBtn.connect.table-btn.green-btn {
        font-size: 16px;
        padding: 10px 14px;
        width: 118px;
        line-height: 23px;
    }
    a.soclBtn.disconnect.table-btn.red-btn {
        margin: 14px 0 -16px 0;
    }
    tags.tagify.form-control.inputtags {
        padding: 3px 10px;
    }
    tag.tagify__tag {
        padding: 7px 3px;
    }
    .am-selected-media ul.media-box li {
        width: 34px;
        height: 34px;
        position: relative;
    }
    .am-selected-media ul.media-box {
        padding: 9px 16px;
        gap: 43px;
        margin-bottom: 42px;
        min-width: 100%;
    }
    .publish-last-div {
        width: 100%;
        max-width: 377px;
        margin: 0 auto;
    }
    .input-label {
        width: 214px;
        height: 54px;
        padding-right: 34px;
    }
    .input-label:before {
        right: 8px;
        width: 42px;
        height: 22px;
        border-radius: 14px;
    }
    .input-label:after {
        right: 31px;
    }
    input:checked ~ .input-label:after {
        right: 11px;
    }
    .step-nevigationbutton > div > img {
        width: 40px;
        height: 40px;
        padding: 7px 11px;
    }
    .publish-info-content.d-flex {
        flex-wrap: wrap;
        margin-bottom: 21px;
    }
    .publish-info .react-action,
    .publish-info .influencer-detail {
        width: calc(100% - 40px);
        flex: 0 0 calc(100% - 40px);
        margin: 0 auto 13px;
    }
    #collection4 .publish-info .influencer-detail {
        margin-right: auto;
    }
    button.button-ccg.new-style {
        max-width: 280px;
        flex: 1;
    }
    .final-step-outer {
        padding: 31px;
        padding-bottom: 0;
    }
    .final-step {
        width: 250px;
        font-size: 17px;
        padding: 32px 37px;
        margin: 0;
    }
    .final-step img.icon {
        width: 124px;
    }
    .modal-dialog {
        margin: 0;
    }
    .timeline-content span img {
        height: 17px;
    }
    h1.dashHeading {
        font-size: 18px;
        padding: 12px 14px;
    }
    .no-data-contant {
        font-size: 18px;
        line-height: 28px;
    }
    .no-data-div img {
        width: 107.24px;
        margin-bottom: 36px;
        margin-top: 43px;
    }
    .steps-section {
        position: relative;
        padding: 90px 0;
        margin: 48px 0;
        width: 100%;
        border-radius: 0;
        overflow: hidden;
    }
    .steps-section:before {
        content: "";
        width: 138%;
        height: calc(100% - 31px);
        transform: rotate(-7.644deg);
        flex-shrink: 0;
        border-radius: 75%;
        background: #AD80FF;
        position: absolute;
        left: -9999px;
        right: -9999px;
        top: 0;
        bottom: 0;
        margin: auto;
    }
    .steps-section h2 {
        position: relative;
        width: 100%;
        text-align: center;
        color: #fff;
        text-align: center;
        font-family: Mulish;
        font-size: 24px;
        font-style: normal;
        font-weight: 800;
        line-height: normal;
    }
    .campaign-type-content,
    .boost .campaign-type-content {
        margin-top: 0;
        margin-bottom: 28px;
    }
    .position-relative.otrcm {
        margin-bottom: 28px !important;
    }
    .social_media_radio,
    .position-relative.otrcm {
        flex: 0 0 calc(100% - 0px);
        width: calc(100% - 0px);
        max-width: 498px;
        margin: 0 auto;
    }
    .social_media.d-flex.justify-content-between.flex-wrap {
        justify-content: center !important;
    }
    .social_media_radio,
    .position-relative.otrcm {
        flex: 0 0 calc(100% - 0);
        width: calc(100% - 0);
        max-width: 498px;
    }
    .marketplace-imports.shoutout .steps-section:before {
        display: none;
    }

    .marketplace-imports.shoutout .steps-section {
        padding: 0;
    }
    .marketplace-imports.shoutout .steps-section .steps-icon {
        background: transparent;
    }
    .social_media_radio {
        margin-bottom: 37px;
    }
    .form-shadow-box.half-box {
        padding: 15px 15px;
    }
    .steps-position {
        display: none;
    }
    .filter-buttons {
        flex-wrap: wrap;
    }
    .filter-form {
        margin: 0 -10px;
        height: 285px;
        overflow: auto;
    }
    .custom-task-list {
        margin-bottom: 20px;
    }
    .influencer-detail {
        width: calc(100% - 30px);
        max-width: 321px;
        margin-left: auto;
        margin-right: auto;
    }
    .filter-form .form-group {
        flex: 0 0 calc(50% - 20px);
    }
    span.influencer-cart-count {
        width: 18px;
        height: 18px;
        font-size: 10px;
        right: -4px;
        top: -4px;
    }
    .step-nevigationbutton .influencer-cart img {
        width: 18px;
        height: 18px;
    }
    .form-shadow-box.middle-box {
        padding: 0;
        box-shadow: none;
        border: 0;
    }
    .form-shadow-box.middle-box {
        padding: 0;
        box-shadow: none;
        border: 0;
    }
    .form-shadow-box.middle-box table {
        display: inline-block;
    }
    .form-shadow-box.middle-box table thead {
        display: none;
    }
    .form-shadow-box.middle-box table tr {
        display: grid !important;
        grid-template:
            "user-image user-name user-price user-delete"
            "user-image user-follow user-price user-delete";
        grid-auto-columns: 43px calc(100% - 165px) 89px 24px;
        width: calc(100vw - 20px);
        padding: 14px 0;
    }
    .form-shadow-box.middle-box table tr td:nth-child(2) {
        grid-area: user-image;
        padding: 0;
    }
    .form-shadow-box.middle-box table tr td:nth-child(3) {
        grid-area: user-name;
        margin: 0;
        padding: 0 12px;
        line-height: 16px;
        height: 19px;
    }
    .form-shadow-box.middle-box table tr td:nth-child(4) {
        grid-area: user-follow;
        padding: 5px 12px;
        line-height: normal;
        height: 31px;
    }
    .form-shadow-box.middle-box table tr td:nth-child(5) {
        grid-area: user-price;
        padding: 0 8px;
        text-align: right;
        width: auto;
    }
    .form-shadow-box.middle-box table tr td:nth-child(6) {
        grid-area: user-delete;
        padding: 0;
    }
    .form-shadow-box.middle-box table tfoot tr {
        display: grid !important;
        grid-template:
            "user-image user-name user-follow user-delete" !important;
        grid-auto-columns: 0   calc(100% - 204px) 202px 0 0;
        width: calc(100vw - 20px);
        padding: 0 0;
    }
    .form-shadow-box.middle-box table tfoot tr td:nth-child(4) {
        text-align: right;
    }
    .form-shadow-box.middle-box table tfoot tr td:nth-child(3) {
        padding: 5px 12px;
        height: auto;
    }
    .form-shadow-box.middle-box table tfoot tr td:nth-child(2) {
        border: 0;
    }
    .form-shadow-box.middle-box table tfoot tr td:nth-child(1) {
        display: none;
    }
    .influncerCol {
        flex-wrap: wrap;
        flex-direction: row;
        gap: 20px;
    }
    .influncerList .wizardForm {
        max-width: 100%;
    }
    .influncerdetailpopup .modal-header {
        border-radius: 0;
    }
    .latest-campaigns-box-outer {
        height: calc(100vh - 410px);
    }
    .latest-campaigns-box {
        margin-bottom: 30px;
        width: calc(50% - 40px);
        margin: 0 0 42px;
        min-width: 304px;
    }
    .popup-social-image.green-circle {
        margin: 10px 6px;
        width: calc(50% - 12px);
    }
    .popup-social-section {
        width: calc(100% - 40px);
        padding: 0 0 15px;
    }
    .home-heading {
        font-size: 20px;
        padding-top: 34px;
    }
    .comming-soon-loader {
        width: 100%;
        border-width: 3px;
        height: 61px;
    }
    .applicationform .modal-body {
        padding: 20px;
    }
    .applicationform .nav-tabs {
        margin-bottom: 26px;
    }
    .applicationform .nav-tabs .nav-link {
        font-size: 16px;
        line-height: 24px;
        height: 42px;
        width: 129px;
    }
    .language-section,
    .soon-dropdown {
        z-index: 9;
    }
    .language-section ul li a,
    .language-section ul {
        font-size: 16px;
    }
    .language-section img {
        width: 20px;
        height: 20px;
    }
    .language-section {
        left: 12px;
        top: 12px;
    }
    .soon-dropdown {
        right: 12px;
        top: 12px;
    }
    .soon-dropdown .btn-group > img {
        width: 20px;
        height: 20px;
    }
    .soon-dropdown .btn-group .dropdown-toggle > img {
        width: 22px;
        height: 22px;
    }
    .soon-dropdown .btn-group {
        display: flex;
        align-items: center;
    }
    .home-comming-soon {
        background: transparent;
    }
    .comming-soon-loader:before {
        left: 3px;
        height: 50px;
    }
    .custom-loader {
        font-size: 18px;
    }
    .col-lg-4:first-child .comming-step-box:before {
        border-radius: 50%;
        background: #AD80FF;
        width: 687.548px;
        height: 273.026px;
        transform: rotate(-7.644deg);
        flex-shrink: 0;
        content: "";
        position: absolute;
        left: -9999px;
        top: -9975px;
        bottom: -9999px;
        right: -9999px;
        margin: auto;
        z-index: -1;
    }
    .col-lg-4:nth-child(2) .comming-step-box:before {
        border-radius: 50%;
        background: rgba(248, 105, 136, 0.9);
        width: 687.548px;
        height: 273.026px;
        transform: rotate(8deg);
        flex-shrink: 0;
        content: "";
        position: absolute;
        left: -9999px;
        top: -9975px;
        bottom: -9999px;
        right: -9999px;
        margin: auto;
        z-index: -2;
    }
    .col-lg-4:nth-child(3) .comming-step-box:before {
        border-radius: 50%;
        background: #ad80ff;
        width: 687.548px;
        height: 273.026px;
        transform: rotate(-7.644deg);
        flex-shrink: 0;
        content: "";
        position: absolute;
        left: -9999px;
        top: -9975px;
        bottom: -9999px;
        right: -9999px;
        margin: auto;
        z-index: -1;
    }
    .home-comming-soon:before {
        display: none;
    }
    .comming-step-box img {
        margin-top: 0;
    }
    .comming-step-box-text {
        font-size: 18px;
    }
    .comming-step-box {
        max-width: 469px;
        margin: 37px auto 24px;
        position: relative;
    }
    button.apply-btn {
        margin-top: 93px !important;
    }
    button.apply-btn {
        width: 293px;
        font-size: 22px;
    }
    .application-heading {
        font-size: 20px;
        padding-bottom: 17px;
        letter-spacing: 2px;
    }
    .step_form_custom .steps-section {
        padding: 0 !important;
        margin-bottom: 0 !important;
        margin-top: 18px;
    }
    .step_form_custom .steps-section:before {
        display: none;
    }
    .step_form_custom .steps-section img.step-icon-check {
        display: block;
        width: 16px;
    }
    .step_form_custom .step_form {
        margin-top: 30px;
    }
    .social_media_radio.campaign-type {
        margin: 0;
        padding: 0;
    }
    .form-shadow-box.long-box {
        margin-left: auto;
        margin-right: auto;
    }
    #backtop {
        font-size: 40px;
        width: 40px;
        height: 40px;
        line-height: 40px;
        margin-left: 0;
        margin-right: auto;
        display: block;
        position: absolute;
        top: -49px;
    }
    .step-nevigationbutton > div.influencer-cart {
        right: 0;
        width: 40px;
        height: 40px;
        top: -49px;
    }
    .influencer-filter button {
        height: 51px;
        padding-right: 8px;
        padding-left: 8px;
        margin-right: 12px;
    }
    .influencer-filter button:last-child {
        margin-right: 0;
    }
    div#latest-influencer-lists {
        padding: 0;
    }
    .filter-buttons span.select2.select2-container.select2-container--default {
        margin-left: 0;
        margin-right: 8px;
    }
    .connectPrising table td.taskName {
        line-height: normal;
    }
    .dashboard-right {
        margin: 0;
        padding-bottom: 5em;
    }
    .page_tab.new_steps.campaign-type,
    .page_tab.new_steps {
        padding: 10px 10px;
        margin: 0;
    }
    #new_steps3 .step-nevigationbutton.fixed {
        bottom: 87px;
    }
    .show #latest-influencer-lists .influencer-list.load-more {
        padding-bottom: 23px;
    }
    .show .step-nevigationbutton {
        position: fixed;
        bottom: 106px;
        width: calc(100% - 21px);
    }
    .ntfMenu img {
        width: 19px;
    }
    .dropdown-menu.show a {
        font-size: 16px;
    }
    .dashboard-right .pagesections {
        border-left: 0;
        padding: 0 0 26px;
        margin-bottom: 35px;
        border-right: 0;
        border-top: 0;
        border-radius: 0;
    }
    .home_login-layout .page_tab {
        padding: 26px 10px;
        margin-bottom: 0;
    }
    .home-text.font-bold {
        letter-spacing: normal;
    }
    .faqHeading {
        width: 100%;
    }
    .paymentPagenumber {
        margin: 41px 19px 25px;
        width: calc(100% - 38px);
    }
    .paymentPagenumber.greenBox h2 {
        letter-spacing: unset;
    }
    .ser_op::after {
        width: calc(100% - 37px);
        left: 35px;
        top: 14px;
    }
    .ser_op.current > span.numberTab {
        color: #AD80FF;
    }

    .profile-connect-btn {
        font-size: 14px;
        line-height: 24px;
        width: 218px;
        height: 40px;
        margin-bottom: 37px;
    }
    .new-camping {
        margin-top: 0 !important;
    }

    .camping-box {
        height: 328px;
        flex: 0 0 auto;
    }
    .steps_con .step-nevigationbutton ,
    .step-nevigationbutton {
        position: fixed;
        bottom: 106px;
        left: 0;
        width: calc(100% - 40px);
        right: 0;
        margin: auto;
    }
    .page_tab.new_steps {
        padding-bottom: 79px;
    }
    .marketplace-imports.shoutout .steps-section .steps-point:after {
        /* display: none; */
        left: calc(50% + 21px);
        top: 14px;
        width: calc(100% - 42px);
    }
    .middle-box table td.influencer-name, .middle-box table td.total-follower{
        width: auto;
    }
    .inside-table.request-content {
        padding: 15px 13px 42px;
        gap: 44px 0;
    }
    .request-content .inside-table-row {
        flex: 0 0 140px;
        width: 140px;
        margin-left: auto;
        margin-right: auto;
    }
    .modal-body {
        padding: 0;
    }
    .request-popup .modal-dialog.default-width {
        max-width: 100%;
    }
    .request-content-data .inside-table-row {
        width: 100%;
    }
    .request-content-data.icon-before {
        gap: 21px 0;
        padding: 29px 20px 46px;
    }
    .ontotalcount {
        margin: 0 0 19px;
        padding: 0;
    }
    .ontro .dr-row {
        display: grid !important;
        grid-template:
            "in-user-image in-user-name in-user-price"
            "in-user-image in-user-follow  in-user-price";
        grid-auto-columns: 46px calc(100% - 188px) 140px;
        width: 100%;
        padding: 14px 0;
    }

    .influencer-image {
        grid-area: in-user-image;
    }

    .influencer-image ~ a {
        grid-area: in-user-name;
    }

    .influencer-follo {
        grid-area: in-user-follow;
        width: auto;
        margin: unset;
    }

    .influencer-price {
        grid-area: in-user-price;
        width: 100%;
        text-align: right;
    }

    .influencer-image ~ a .influencer-name {
        margin: 0;
        width: auto;
    }
	.table-btn {
	    margin-left: 0 !important;
	    margin-right: 15px !important;
	}
    .influncerList .influncerlist-dropdown .dr-row {
        display: grid !important;
        grid-template:
            "in-delete in-user-image in-user-name in-user-price"
            "in-delete in-user-image in-user-follow in-user-price";
        grid-auto-columns: 24px 43px calc(100% - 165px) 89px;
        width: calc(100vw - 76px);
    }

    .influncerlist-dropdown .dr-row .delete-inf {
        grid-area: in-delete;
    }
    .modal-body{
        padding: 15px;
    }
    .nav-tabs .nav-link{
        padding: 10px;
    }
    .image-desktop{
        display: none !important;
    }
    .image-mobile{
        display: block !important;
    }
}
/*575px*/
@media screen and (max-width: 535px) {

}
/*535px*/
@media screen and (max-width: 400px) {

    .socialCnt {
        margin-right: 20px;
        width: 40px;
        height: 40px;
    }
    .socialUserImage {
        width: 40px;
        height: 40px;
        flex: 0 0 40px;
    }
    .socialUserDetailName {
        font-size: 17px;
    }
    .connectWithInner {
        padding: 19px 10px;
        margin: 10px 0px;
    }
    .in-value {
        width: 161px;
    }
    .selected-media-action .react-action-price-one.d-flex {
        padding-top: 12px;
        justify-content: center;
    }
    .selected-media-action > .d-flex {
        grid-template-rows: auto;
        grid-template-areas:
            "display1 display1"
            "prc1 prc1"
            "prc2 prc2";
        width: 100%;
    }
    .react-action {
        width: 100%;
    }
    .selected-media-action .campaign-type-content label {
        width: 100%;
    }
    .selected-media-action .campaign-type-content input:checked ~ label:after {
        right: auto;
        left: 41px;
        top: 62px;
    }
    .selected-media-action .campaign-type-content label:after {
        right: auto;
        top: 62px;
        left: 24px;
    }
    .mobile-menu ul li img {
        height: 29px;
    }
    .input-label {
        width: 187px;
        height: 50px;
        padding-right: 34px;
    }
    .active-inactive label {
        font-size: 19px;
    }
    .steps-section h2 {
        font-size: 20px;
    }
    .campaign-type-share-information {
        line-height: normal;
        font-size: 13px;
    }
    .campaign-type-text {
        font-size: 18px;
    }
    .steps-section {
        position: relative;
        padding: 70px 0;
        margin: 30px 0;
        overflow: hidden;
    }
    .comming-soon-box {
        padding: 58px 0 23px;
    }
    .comming-soon-box .logo img {
        width: 275px;
    }
    .dashboard-right header.site-header {
        padding: 10px 10px 10px;
        display: block !important;
        padding: 10px 20px;
        box-shadow: 0 0 10px #000 !important;
    }
    .marketplace-imports.shoutout .steps-section {
        margin-top: 6px;
        margin-bottom: 10px;
    }
    .steps-cont {
        font-size: 14px;
    }
    .steps-which {
        font-size: 19px;
        margin-bottom: 20px;
    }
    .influencer-filter > img {
        height: 34px;
        margin-left: 10px;
    }
    .influencer-filter button {
        height: 40px;
        padding-right: 8px;
        padding-left: 8px;
        margin-right: 0px;
        font-size: 14px;
    }
    .influencer-filter button img {
        margin-left: 7px;
        width: 16px !important;
    }
     .mobile-menu .image_more{
        height: 29px;
        width:29px;
    }
}
/*450px*/
@media screen and (max-width: 499px) {

    .mobile-menu .image_more{
        height: 44px;
        width:42px;
    }
}
/*499px*/
@media screen and (max-width: 416px) {

    .mobile-menu .image_more{
        height: 42px;
        width:42px;
    }
}
/*416px*/
@media screen and (max-width: 409px) {

    .mobile-menu .image_more{
        height: 32px;
        width:32px;
    }
}
/*409px*/
@media only screen and (max-width: 399px) {
    .mobile-menu .image_more{
        height: 29px;
        width:29px;
    }
    .mobile-menu ul li span.lki {
        font-size: 12px;
    }
}
/*360px */
@media only screen and (max-width: 360px) {
    .mobile-menu .image_more{
        height: 29px;
        width:29px;
    }
    .mobile-menu ul li span.lki {
        font-size: 10px;
    }
}

